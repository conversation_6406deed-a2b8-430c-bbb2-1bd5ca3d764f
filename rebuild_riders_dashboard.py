#!/usr/bin/env python3
"""
Completely Rebuild Riders Dashboard Route
Delete and recreate the problematic riders dashboard with new template and handler
"""

import os
from datetime import datetime

def analyze_current_riders_route():
    """Analyze current riders dashboard implementation"""
    
    print("🔍 ANALYZING CURRENT RIDERS DASHBOARD")
    print("=" * 50)
    
    # Check if riders route exists in app.py
    riders_routes_found = []
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find riders-related routes
        import re
        riders_patterns = re.findall(r"@app\.route\(['\"]([^'\"]*riders[^'\"]*)['\"]", content)
        
        print(f"📊 Found {len(riders_patterns)} riders routes in app.py:")
        for i, route in enumerate(riders_patterns, 1):
            print(f"   {i}. {route}")
            riders_routes_found.append(route)
        
        # Check for riders dashboard specifically
        if '/riders/dashboard' in riders_patterns:
            print("✅ Riders dashboard route found in app.py")
        else:
            print("❌ Riders dashboard route NOT found in app.py")
        
        # Look for riders dashboard function
        riders_dashboard_functions = re.findall(r"def (riders?_dashboard[^(]*)\(", content)
        print(f"📊 Found {len(riders_dashboard_functions)} riders dashboard functions:")
        for func in riders_dashboard_functions:
            print(f"   • {func}")
        
    except Exception as e:
        print(f"❌ Error analyzing app.py: {e}")
    
    # Check for riders templates
    riders_templates = []
    if os.path.exists('templates/riders'):
        for file in os.listdir('templates/riders'):
            if file.endswith('.html'):
                riders_templates.append(f'templates/riders/{file}')
        
        print(f"📊 Found {len(riders_templates)} riders templates:")
        for template in riders_templates:
            print(f"   • {template}")
    else:
        print("❌ templates/riders directory not found")
    
    return {
        'routes_found': riders_routes_found,
        'templates_found': riders_templates
    }

def delete_old_riders_dashboard():
    """Delete old problematic riders dashboard implementation"""
    
    print("\n🗑️ DELETING OLD RIDERS DASHBOARD")
    print("=" * 50)
    
    # Remove riders dashboard route from app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and remove riders dashboard route and function
        import re
        
        # Pattern to match the entire riders dashboard route and function
        pattern = r"@app\.route\(['\"][^'\"]*riders/dashboard[^'\"]*['\"]\)[^@]*?def [^(]+\([^)]*\):[^@]*?(?=@app\.route|def [a-zA-Z_][a-zA-Z0-9_]*\(|if __name__|$)"
        
        matches = re.findall(pattern, content, re.DOTALL)
        
        if matches:
            print(f"🔍 Found {len(matches)} riders dashboard implementations to remove")
            for i, match in enumerate(matches, 1):
                print(f"   {i}. Removing {len(match)} characters")
                content = content.replace(match, "")
            
            # Write back the cleaned content
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ Removed old riders dashboard from app.py")
        else:
            print("ℹ️ No riders dashboard route found to remove")
    
    except Exception as e:
        print(f"❌ Error removing old route: {e}")
    
    # Remove old riders dashboard template if it exists
    old_template_path = 'templates/riders/dashboard.html'
    if os.path.exists(old_template_path):
        try:
            os.remove(old_template_path)
            print(f"✅ Removed old template: {old_template_path}")
        except Exception as e:
            print(f"❌ Error removing template: {e}")
    else:
        print("ℹ️ No old riders dashboard template found")

def create_new_riders_dashboard_route():
    """Create new professional riders dashboard route"""
    
    print("\n🚀 CREATING NEW RIDERS DASHBOARD ROUTE")
    print("=" * 50)
    
    # New riders dashboard route code
    new_route_code = '''
# ============================================================================
# PROFESSIONAL RIDERS DASHBOARD - REBUILT
# ============================================================================

@app.route('/riders/dashboard')
@login_required
def riders_dashboard():
    """Professional Riders Management Dashboard"""
    try:
        db = get_db()
        
        # Get riders overview statistics
        total_riders = db.execute('SELECT COUNT(*) as count FROM riders').fetchone()['count']
        
        active_riders = db.execute('''
            SELECT COUNT(*) as count FROM riders
            WHERE is_active = 1 OR is_active IS NULL
        ''').fetchone()['count']
        
        # Get riders with their performance metrics
        riders_performance = db.execute('''
            SELECT r.rider_id, r.name, r.phone, r.vehicle_type, r.rating,
                   COUNT(o.order_id) as total_deliveries,
                   COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as completed_deliveries,
                   AVG(r.rating) as avg_rating,
                   MAX(o.order_date) as last_delivery_date
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            GROUP BY r.rider_id
            ORDER BY total_deliveries DESC
            LIMIT 10
        ''').fetchall()
        
        # Get recent delivery activities
        recent_deliveries = db.execute('''
            SELECT o.order_id, o.customer_name, o.status, o.order_date,
                   r.name as rider_name, o.delivery_address
            FROM orders o
            LEFT JOIN riders r ON o.rider_id = r.rider_id
            WHERE o.status IN ('Out for Delivery', 'Delivered', 'Shipped')
            ORDER BY o.order_date DESC
            LIMIT 15
        ''').fetchall()
        
        # Get delivery statistics for today
        today = datetime.now().date()
        today_stats = db.execute('''
            SELECT
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_orders,
                COUNT(CASE WHEN status = 'Out for Delivery' THEN 1 END) as out_for_delivery
            FROM orders
            WHERE DATE(order_date) = ?
        ''', (today,)).fetchone()
        
        context = {
            'title': 'Riders Management Dashboard',
            'total_riders': total_riders,
            'active_riders': active_riders,
            'riders_performance': riders_performance,
            'recent_deliveries': recent_deliveries,
            'today_stats': today_stats,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('riders/professional_dashboard.html', **context)
        
    except Exception as e:
        flash(f'Error loading riders dashboard: {str(e)}', 'error')
        return redirect(url_for('dashboard'))
'''
    
    # Add the new route to app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find a good place to insert the new route (before the main execution block)
        insertion_point = content.find('if __name__ == "__main__":')
        
        if insertion_point != -1:
            # Insert the new route before the main execution
            new_content = content[:insertion_point] + new_route_code + '\n\n' + content[insertion_point:]
            
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ Added new riders dashboard route to app.py")
        else:
            print("❌ Could not find insertion point in app.py")
            return False
    
    except Exception as e:
        print(f"❌ Error adding new route: {e}")
        return False
    
    return True

def create_new_riders_dashboard_template():
    """Create new professional riders dashboard template"""
    
    print("\n🎨 CREATING NEW RIDERS DASHBOARD TEMPLATE")
    print("=" * 50)
    
    # Ensure templates/riders directory exists
    os.makedirs('templates/riders', exist_ok=True)
    
    # New professional template
    template_content = '''{% extends 'base.html' %}

{% block title %}Riders Management Dashboard - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-motorcycle text-primary"></i> Riders Management Dashboard
        </h1>
        <div>
            <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-primary shadow-sm mr-2">
                <i class="fas fa-chart-line fa-sm text-white-50"></i> Delivery Analytics
            </a>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
            </a>
        </div>
    </div>

    <!-- Content Row - Statistics Cards -->
    <div class="row">
        <!-- Total Riders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_riders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Riders Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Active Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_riders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Today's Deliveries Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Today's Deliveries</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ today_stats.delivered_orders or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Out for Delivery Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Out for Delivery</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ today_stats.out_for_delivery or 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row - Tables -->
    <div class="row">
        <!-- Riders Performance Table -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Performing Riders</h6>
                </div>
                <div class="card-body">
                    {% if riders_performance %}
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>Rider Name</th>
                                        <th>Phone</th>
                                        <th>Vehicle</th>
                                        <th>Total Deliveries</th>
                                        <th>Completed</th>
                                        <th>Success Rate</th>
                                        <th>Rating</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in riders_performance %}
                                    <tr>
                                        <td>{{ rider.name }}</td>
                                        <td>{{ rider.phone }}</td>
                                        <td>{{ rider.vehicle_type or 'N/A' }}</td>
                                        <td>{{ rider.total_deliveries }}</td>
                                        <td>{{ rider.completed_deliveries }}</td>
                                        <td>
                                            {% if rider.total_deliveries > 0 %}
                                                {{ "%.1f"|format((rider.completed_deliveries / rider.total_deliveries) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge badge-{{ 'success' if (rider.avg_rating or 0) >= 4 else 'warning' if (rider.avg_rating or 0) >= 3 else 'danger' }}">
                                                {{ "%.1f"|format(rider.avg_rating or 0) }}
                                            </span>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No rider performance data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Deliveries -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Delivery Activities</h6>
                </div>
                <div class="card-body">
                    {% if recent_deliveries %}
                        {% for delivery in recent_deliveries %}
                        <div class="d-flex align-items-center border-bottom py-2">
                            <div class="mr-3">
                                <div class="icon-circle bg-{{ 'success' if delivery.status == 'Delivered' else 'warning' if delivery.status == 'Out for Delivery' else 'info' }}">
                                    <i class="fas fa-{{ 'check' if delivery.status == 'Delivered' else 'truck' if delivery.status == 'Out for Delivery' else 'box' }} text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="small text-gray-500">Order #{{ delivery.order_id }}</div>
                                <div class="font-weight-bold">{{ delivery.customer_name }}</div>
                                <div class="small">{{ delivery.rider_name or 'Unassigned' }}</div>
                                <div class="small text-gray-500">{{ delivery.order_date }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No recent delivery activities.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions Row -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.real_time_tracking') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-map-marker-alt"></i> Real-time Tracking
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.performance_kpis') }}" class="btn btn-success btn-block">
                                <i class="fas fa-chart-bar"></i> Performance KPIs
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('riders_management') }}" class="btn btn-info btn-block">
                                <i class="fas fa-users-cog"></i> Manage Riders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.comprehensive_reports') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-file-alt"></i> Delivery Reports
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Riders Dashboard loaded successfully');
    
    // Auto-refresh every 30 seconds for real-time updates
    setTimeout(function() {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}'''
    
    # Write the new template
    template_path = 'templates/riders/professional_dashboard.html'
    try:
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        print(f"✅ Created new template: {template_path}")
        return True
    
    except Exception as e:
        print(f"❌ Error creating template: {e}")
        return False

def test_new_riders_dashboard():
    """Test the new riders dashboard"""
    
    print("\n🧪 TESTING NEW RIDERS DASHBOARD")
    print("=" * 50)
    
    try:
        import requests
        
        response = requests.get("http://127.0.0.1:3000/riders/dashboard", timeout=10)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            if 'login' in response.text.lower():
                print("✅ New riders dashboard accessible (requires authentication)")
                return True
            else:
                print("✅ New riders dashboard accessible")
                return True
        else:
            print(f"❌ New riders dashboard not accessible: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing new riders dashboard: {e}")
        return False

def main():
    """Main execution for rebuilding riders dashboard"""
    
    print("🚀 REBUILDING RIDERS DASHBOARD")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analyze current state
    analysis = analyze_current_riders_route()
    
    # Delete old implementation
    delete_old_riders_dashboard()
    
    # Create new route
    route_created = create_new_riders_dashboard_route()
    
    # Create new template
    template_created = create_new_riders_dashboard_template()
    
    # Test new implementation
    dashboard_working = test_new_riders_dashboard()
    
    print(f"\n🎯 RIDERS DASHBOARD REBUILD SUMMARY")
    print("=" * 70)
    print(f"Old routes found: {len(analysis['routes_found'])}")
    print(f"Old templates found: {len(analysis['templates_found'])}")
    print(f"New route created: {'✅ YES' if route_created else '❌ NO'}")
    print(f"New template created: {'✅ YES' if template_created else '❌ NO'}")
    print(f"Dashboard working: {'✅ YES' if dashboard_working else '❌ NO'}")
    
    success = route_created and template_created and dashboard_working
    
    if success:
        print(f"\n🎉 RIDERS DASHBOARD SUCCESSFULLY REBUILT!")
        print(f"✅ Professional new dashboard with performance metrics")
        print(f"✅ Connected to delivery analytics system")
        print(f"✅ Real-time updates and modern UI")
    else:
        print(f"\n⚠️ Some issues occurred during rebuild")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    main()
