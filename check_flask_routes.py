#!/usr/bin/env python3
"""
Check Flask Routes Registration
"""

import sys
import os

# Add current directory to path
sys.path.insert(0, os.getcwd())

try:
    # Import the Flask app
    from app import app
    
    print("🔍 FLASK ROUTES ANALYSIS")
    print("=" * 50)
    
    # Get all registered routes
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'rule': rule.rule,
            'methods': list(rule.methods)
        })
    
    # Filter delivery analytics routes
    delivery_routes = [r for r in routes if 'delivery_analytics' in r['endpoint']]
    
    print(f"📋 DELIVERY ANALYTICS ROUTES FOUND: {len(delivery_routes)}")
    print("-" * 50)
    
    for route in delivery_routes:
        print(f"Endpoint: {route['endpoint']}")
        print(f"Rule: {route['rule']}")
        print(f"Methods: {route['methods']}")
        print("-" * 30)
    
    # Check if dashboard route exists
    dashboard_routes = [r for r in routes if 'dashboard' in r['rule'] and 'delivery_analytics' in r['endpoint']]
    
    print(f"\n🎯 DASHBOARD ROUTES FOUND: {len(dashboard_routes)}")
    if dashboard_routes:
        for route in dashboard_routes:
            print(f"✅ Found: {route['rule']} -> {route['endpoint']}")
    else:
        print("❌ No delivery_analytics dashboard routes found!")
    
    # Check all routes with 'dashboard' in the rule
    all_dashboard_routes = [r for r in routes if 'dashboard' in r['rule']]
    print(f"\n📊 ALL DASHBOARD ROUTES: {len(all_dashboard_routes)}")
    for route in all_dashboard_routes:
        print(f"   {route['rule']} -> {route['endpoint']}")
    
    # Test URL generation
    print(f"\n🧪 TESTING URL GENERATION:")
    with app.app_context():
        try:
            from flask import url_for
            
            # Try to generate URL for delivery_analytics.dashboard
            url = url_for('delivery_analytics.dashboard')
            print(f"✅ url_for('delivery_analytics.dashboard'): {url}")
        except Exception as e:
            print(f"❌ url_for('delivery_analytics.dashboard'): {e}")
        
        # Try other delivery analytics endpoints
        try:
            url = url_for('delivery_analytics.performance_kpis')
            print(f"✅ url_for('delivery_analytics.performance_kpis'): {url}")
        except Exception as e:
            print(f"❌ url_for('delivery_analytics.performance_kpis'): {e}")

except Exception as e:
    print(f"❌ Error analyzing routes: {e}")
    import traceback
    traceback.print_exc()
