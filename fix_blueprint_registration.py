#!/usr/bin/env python3
"""
Fix Blueprint Registration Issues
Ensure all new blueprints are properly registered in app.py
"""

import os
from datetime import datetime

def fix_blueprint_registration():
    """Fix blueprint registration in app.py"""
    
    print("🔧 FIXING BLUEPRINT REGISTRATION ISSUES")
    print("=" * 60)
    
    try:
        # Read current app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Define the imports and registrations needed
        blueprint_imports = [
            "from routes.delivery_analytics import delivery_analytics_bp",
            "from routes.advanced_payment import advanced_payment_bp", 
            "from routes.sales_analytics import sales_analytics_bp"
        ]
        
        blueprint_registrations = [
            "app.register_blueprint(delivery_analytics_bp)",
            "app.register_blueprint(advanced_payment_bp)",
            "app.register_blueprint(sales_analytics_bp)"
        ]
        
        # Check and add imports
        imports_added = 0
        for import_line in blueprint_imports:
            if import_line not in app_content:
                # Find a good place to add imports (after other route imports)
                if "from routes.auth import auth_bp" in app_content:
                    app_content = app_content.replace(
                        "from routes.auth import auth_bp",
                        f"from routes.auth import auth_bp\n{import_line}"
                    )
                    imports_added += 1
                    print(f"✅ Added import: {import_line}")
                else:
                    # Add after Flask imports
                    app_content = app_content.replace(
                        "from flask import Flask",
                        f"from flask import Flask\n{import_line}"
                    )
                    imports_added += 1
                    print(f"✅ Added import: {import_line}")
            else:
                print(f"ℹ️ Import already exists: {import_line}")
        
        # Check and add registrations
        registrations_added = 0
        for register_line in blueprint_registrations:
            if register_line not in app_content:
                # Find a good place to add registrations (after other blueprint registrations)
                if "app.register_blueprint(auth_bp)" in app_content:
                    app_content = app_content.replace(
                        "app.register_blueprint(auth_bp)",
                        f"app.register_blueprint(auth_bp)\n{register_line}"
                    )
                    registrations_added += 1
                    print(f"✅ Added registration: {register_line}")
                else:
                    # Add before the main execution block
                    app_content = app_content.replace(
                        'if __name__ == "__main__":',
                        f'{register_line}\n\nif __name__ == "__main__":'
                    )
                    registrations_added += 1
                    print(f"✅ Added registration: {register_line}")
            else:
                print(f"ℹ️ Registration already exists: {register_line}")
        
        # Write back to app.py
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(app_content)
        
        print(f"\n📊 Blueprint Registration Summary:")
        print(f"   Imports added: {imports_added}")
        print(f"   Registrations added: {registrations_added}")
        
        return imports_added + registrations_added > 0
        
    except Exception as e:
        print(f"❌ Error fixing blueprint registration: {e}")
        return False

def verify_blueprint_files():
    """Verify that all blueprint files exist and are valid"""
    
    print("\n🔍 VERIFYING BLUEPRINT FILES")
    print("=" * 50)
    
    blueprint_files = [
        'routes/delivery_analytics.py',
        'routes/advanced_payment.py',
        'routes/sales_analytics.py'
    ]
    
    all_exist = True
    
    for file_path in blueprint_files:
        if os.path.exists(file_path):
            try:
                # Try to read and validate the file
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for blueprint definition
                if 'Blueprint(' in content:
                    print(f"✅ {file_path} - Valid blueprint file")
                else:
                    print(f"⚠️ {file_path} - Missing Blueprint definition")
                    all_exist = False
                    
            except Exception as e:
                print(f"❌ {file_path} - Error reading file: {e}")
                all_exist = False
        else:
            print(f"❌ {file_path} - File not found")
            all_exist = False
    
    return all_exist

def create_missing_templates():
    """Create missing template directories and basic templates"""
    
    print("\n🎨 CREATING MISSING TEMPLATE DIRECTORIES")
    print("=" * 50)
    
    template_dirs = [
        'templates/delivery_analytics',
        'templates/advanced_payment',
        'templates/sales_analytics',
        'templates/admin',
        'templates/reports/advanced',
        'templates/reports/sales'
    ]
    
    created_dirs = 0
    
    for template_dir in template_dirs:
        if not os.path.exists(template_dir):
            os.makedirs(template_dir, exist_ok=True)
            print(f"✅ Created directory: {template_dir}")
            created_dirs += 1
        else:
            print(f"ℹ️ Directory exists: {template_dir}")
    
    # Create basic dashboard templates
    basic_templates = [
        ('templates/delivery_analytics/dashboard.html', 'Delivery Analytics Dashboard'),
        ('templates/advanced_payment/dashboard.html', 'Advanced Payment Management'),
        ('templates/sales_analytics/dashboard.html', 'Sales Analytics Dashboard'),
        ('templates/admin/bulk_operations.html', 'Admin Bulk Operations'),
        ('templates/admin/system_health.html', 'System Health Monitoring'),
        ('templates/reports/advanced/index.html', 'Advanced Reports'),
        ('templates/reports/sales/index.html', 'Sales Reports')
    ]
    
    created_templates = 0
    
    for template_path, title in basic_templates:
        if not os.path.exists(template_path):
            template_content = '''{% extends 'base.html' %}

{% block title %}''' + title + ''' - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line text-primary"></i> ''' + title + '''
        </h1>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">''' + title + '''</h6>
                </div>
                <div class="card-body">
                    <p>Welcome to ''' + title + '''. This feature is currently being developed.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        This dashboard will provide comprehensive analytics and management capabilities.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('''' + title + ''' page loaded');
});
</script>
{% endblock %}'''
            
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(template_content)
            
            print(f"✅ Created template: {template_path}")
            created_templates += 1
        else:
            print(f"ℹ️ Template exists: {template_path}")
    
    print(f"\n📊 Template Creation Summary:")
    print(f"   Directories created: {created_dirs}")
    print(f"   Templates created: {created_templates}")
    
    return created_dirs + created_templates

def main():
    """Main execution"""
    print("🚀 FIXING BLUEPRINT REGISTRATION ISSUES")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Fix blueprint registration
    registration_fixed = fix_blueprint_registration()
    
    # Verify blueprint files
    files_valid = verify_blueprint_files()
    
    # Create missing templates
    templates_created = create_missing_templates()
    
    print(f"\n🎯 FIX SUMMARY")
    print("=" * 70)
    print(f"Blueprint registration fixed: {registration_fixed}")
    print(f"Blueprint files valid: {files_valid}")
    print(f"Templates created: {templates_created > 0}")
    
    if registration_fixed and files_valid:
        print(f"\n✅ Blueprint registration issues fixed!")
        print(f"🔄 Please restart the server to load new blueprints")
    else:
        print(f"\n⚠️ Some issues remain - check the logs above")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
