import requests
import time

print("🔍 DEBUGGING ROUTE ISSUE")
print("=" * 50)

time.sleep(2)

# Test different variations of the delivery analytics dashboard route
test_urls = [
    'http://127.0.0.1:3000/delivery_analytics/',
    'http://127.0.0.1:3000/delivery_analytics/dashboard',
    'http://127.0.0.1:3000/delivery_analytics',
]

print("Testing different URL variations:")
for url in test_urls:
    try:
        response = requests.get(url, timeout=5)
        print(f"✅ {url:<50} Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Content length: {len(response.text)} characters")
    except Exception as e:
        print(f"❌ {url:<50} Error: {str(e)[:30]}")

# Test if Flask server is responding at all
print(f"\nTesting Flask server health:")
try:
    response = requests.get('http://127.0.0.1:3000/', timeout=5)
    print(f"✅ Main route working: Status {response.status_code}")
except Exception as e:
    print(f"❌ Flask server issue: {e}")

# Test other working routes for comparison
print(f"\nTesting other routes for comparison:")
other_routes = [
    'http://127.0.0.1:3000/sales_analytics/',
    'http://127.0.0.1:3000/riders/performance',
]

for url in other_routes:
    try:
        response = requests.get(url, timeout=5)
        print(f"✅ {url:<50} Status: {response.status_code}")
    except Exception as e:
        print(f"❌ {url:<50} Error: {str(e)[:30]}")

print(f"\n🎯 CONCLUSION:")
print(f"If /delivery_analytics/ works but /delivery_analytics/dashboard doesn't,")
print(f"there may be a route definition issue in the blueprint.")
