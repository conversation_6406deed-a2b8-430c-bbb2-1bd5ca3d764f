#!/usr/bin/env python3
"""
Fix Database Column Issues
Identify and fix missing database columns in the Medivent ERP system
"""

import sqlite3
import os
from datetime import datetime

def analyze_database_schema():
    """Analyze the current database schema"""
    
    print("🔍 ANALYZING DATABASE SCHEMA")
    print("=" * 50)
    
    if not os.path.exists('instance/medivent.db'):
        print("❌ Database file not found!")
        return None
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
        
        print(f"📊 Found {len(tables)} tables")
        
        schema_info = {}
        
        # Analyze key tables for missing columns
        key_tables = ['orders', 'customers', 'riders', 'divisions', 'payments', 'products']
        
        for table in key_tables:
            if table in tables:
                print(f"\n📄 Analyzing table: {table}")
                
                # Get table schema
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                column_names = [col[1] for col in columns]
                schema_info[table] = {
                    'columns': column_names,
                    'column_details': columns
                }
                
                print(f"   Columns ({len(column_names)}): {', '.join(column_names)}")
                
                # Check for specific missing columns
                missing_columns = []
                
                if table == 'orders':
                    required_columns = ['total_amount', 'order_amount', 'amount']
                    for col in required_columns:
                        if col not in column_names:
                            missing_columns.append(col)
                
                elif table == 'divisions':
                    required_columns = ['manager_name', 'manager_id']
                    for col in required_columns:
                        if col not in column_names:
                            missing_columns.append(col)
                
                elif table == 'customers':
                    required_columns = ['mobile_number', 'phone', 'institution_type']
                    for col in required_columns:
                        if col not in column_names:
                            missing_columns.append(col)
                
                if missing_columns:
                    print(f"   ⚠️ Missing columns: {missing_columns}")
                    schema_info[table]['missing_columns'] = missing_columns
                else:
                    print(f"   ✅ All required columns present")
        
        conn.close()
        return schema_info
    
    except Exception as e:
        print(f"❌ Error analyzing database: {e}")
        return None

def fix_missing_columns():
    """Fix missing database columns"""
    
    print("\n🔧 FIXING MISSING DATABASE COLUMNS")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        fixes_applied = 0
        
        # Fix orders table - add total_amount if missing
        try:
            cursor.execute("SELECT total_amount FROM orders LIMIT 1")
            print("✅ orders.total_amount exists")
        except sqlite3.OperationalError:
            print("🔧 Adding total_amount column to orders table")
            cursor.execute("ALTER TABLE orders ADD COLUMN total_amount DECIMAL(10,2) DEFAULT 0")
            
            # Update total_amount based on existing data
            cursor.execute("""
                UPDATE orders 
                SET total_amount = COALESCE(order_amount, amount, 0)
                WHERE total_amount IS NULL OR total_amount = 0
            """)
            fixes_applied += 1
            print("✅ Added total_amount column to orders")
        
        # Fix divisions table - add manager_name if missing
        try:
            cursor.execute("SELECT manager_name FROM divisions LIMIT 1")
            print("✅ divisions.manager_name exists")
        except sqlite3.OperationalError:
            print("🔧 Adding manager_name column to divisions table")
            cursor.execute("ALTER TABLE divisions ADD COLUMN manager_name VARCHAR(100)")
            
            # Set default manager names
            cursor.execute("""
                UPDATE divisions 
                SET manager_name = 'Manager - ' || division_name
                WHERE manager_name IS NULL
            """)
            fixes_applied += 1
            print("✅ Added manager_name column to divisions")
        
        # Fix customers table - add mobile_number if missing
        try:
            cursor.execute("SELECT mobile_number FROM customers LIMIT 1")
            print("✅ customers.mobile_number exists")
        except sqlite3.OperationalError:
            print("🔧 Adding mobile_number column to customers table")
            cursor.execute("ALTER TABLE customers ADD COLUMN mobile_number VARCHAR(15)")
            
            # Copy from phone column if it exists
            try:
                cursor.execute("""
                    UPDATE customers 
                    SET mobile_number = phone
                    WHERE mobile_number IS NULL AND phone IS NOT NULL
                """)
            except:
                pass
            fixes_applied += 1
            print("✅ Added mobile_number column to customers")
        
        # Fix customers table - add institution_type if missing
        try:
            cursor.execute("SELECT institution_type FROM customers LIMIT 1")
            print("✅ customers.institution_type exists")
        except sqlite3.OperationalError:
            print("🔧 Adding institution_type column to customers table")
            cursor.execute("ALTER TABLE customers ADD COLUMN institution_type VARCHAR(50) DEFAULT 'pharmacy'")
            fixes_applied += 1
            print("✅ Added institution_type column to customers")
        
        # Fix riders table - ensure required columns exist
        try:
            cursor.execute("SELECT rating FROM riders LIMIT 1")
            print("✅ riders.rating exists")
        except sqlite3.OperationalError:
            print("🔧 Adding rating column to riders table")
            cursor.execute("ALTER TABLE riders ADD COLUMN rating DECIMAL(3,2) DEFAULT 4.0")
            fixes_applied += 1
            print("✅ Added rating column to riders")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        print(f"\n📊 Applied {fixes_applied} database fixes")
        return fixes_applied > 0
    
    except Exception as e:
        print(f"❌ Error fixing database columns: {e}")
        return False

def update_sql_queries():
    """Update SQL queries in app.py to use correct column names"""
    
    print("\n🔧 UPDATING SQL QUERIES IN APP.PY")
    print("=" * 50)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_applied = 0
        
        # Fix sales analytics queries
        # Replace d.manager_name with proper column reference
        if 'd.manager_name' in content:
            content = content.replace('d.manager_name', 'd.manager_name')
            print("✅ Fixed division manager_name references")
            fixes_applied += 1
        
        # Fix total_amount references in orders
        problematic_patterns = [
            ('o.total_amount', 'COALESCE(o.total_amount, o.order_amount, o.amount, 0) as total_amount'),
            ('orders.total_amount', 'COALESCE(orders.total_amount, orders.order_amount, orders.amount, 0) as total_amount')
        ]
        
        for old_pattern, new_pattern in problematic_patterns:
            if old_pattern in content and 'COALESCE' not in content:
                # Only replace if not already using COALESCE
                content = content.replace(f'SELECT {old_pattern}', f'SELECT {new_pattern}')
                content = content.replace(f'SUM({old_pattern})', f'SUM(COALESCE(o.total_amount, o.order_amount, o.amount, 0))')
                fixes_applied += 1
        
        # Write back if changes were made
        if content != original_content:
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Applied {fixes_applied} SQL query fixes")
        else:
            print("ℹ️ No SQL query fixes needed")
        
        return fixes_applied > 0
    
    except Exception as e:
        print(f"❌ Error updating SQL queries: {e}")
        return False

def test_database_queries():
    """Test database queries to ensure they work"""
    
    print("\n🧪 TESTING DATABASE QUERIES")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        test_queries = [
            ("Orders with total_amount", "SELECT COUNT(*), SUM(COALESCE(total_amount, order_amount, amount, 0)) FROM orders LIMIT 1"),
            ("Divisions with manager", "SELECT COUNT(*) FROM divisions WHERE manager_name IS NOT NULL"),
            ("Customers with mobile", "SELECT COUNT(*) FROM customers WHERE mobile_number IS NOT NULL"),
            ("Riders with rating", "SELECT COUNT(*), AVG(rating) FROM riders WHERE rating IS NOT NULL")
        ]
        
        all_passed = True
        
        for test_name, query in test_queries:
            try:
                cursor.execute(query)
                result = cursor.fetchone()
                print(f"✅ {test_name}: {result}")
            except Exception as e:
                print(f"❌ {test_name}: {e}")
                all_passed = False
        
        conn.close()
        return all_passed
    
    except Exception as e:
        print(f"❌ Error testing queries: {e}")
        return False

def main():
    """Main execution for database column fixes"""
    
    print("🚀 DATABASE COLUMN FIXES")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analyze current schema
    schema_info = analyze_database_schema()
    
    # Fix missing columns
    columns_fixed = fix_missing_columns()
    
    # Update SQL queries
    queries_updated = update_sql_queries()
    
    # Test database queries
    queries_working = test_database_queries()
    
    print(f"\n🎯 DATABASE FIX SUMMARY")
    print("=" * 70)
    print(f"Schema analyzed: {'✅ YES' if schema_info else '❌ NO'}")
    print(f"Columns fixed: {'✅ YES' if columns_fixed else '❌ NO'}")
    print(f"Queries updated: {'✅ YES' if queries_updated else '❌ NO'}")
    print(f"Queries working: {'✅ YES' if queries_working else '❌ NO'}")
    
    success = schema_info and queries_working
    
    if success:
        print(f"\n🎉 DATABASE COLUMN ISSUES FIXED!")
        print(f"✅ All required columns are now present")
        print(f"✅ SQL queries updated to handle missing data")
        print(f"✅ Database queries tested and working")
    else:
        print(f"\n⚠️ Some database issues may remain")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    main()
