#!/usr/bin/env python3
"""
Fix String Formatting Error
Locate and resolve the "not all arguments converted during string formatting" error
"""

import os
import re
from datetime import datetime

def search_string_formatting_errors():
    """Search for string formatting errors in the codebase"""
    
    print("🔍 SEARCHING FOR STRING FORMATTING ERRORS")
    print("=" * 50)
    
    # Files to search
    files_to_search = ['app.py']
    
    # Add route files
    if os.path.exists('routes'):
        for file in os.listdir('routes'):
            if file.endswith('.py'):
                files_to_search.append(f'routes/{file}')
    
    formatting_errors = []
    
    for file_path in files_to_search:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for line_num, line in enumerate(lines, 1):
                # Look for potential string formatting issues
                
                # Check for % formatting with mismatched arguments
                if '%' in line and ('rider' in line.lower() and 'performance' in line.lower()):
                    formatting_errors.append({
                        'file': file_path,
                        'line': line_num,
                        'content': line.strip(),
                        'type': 'rider_performance_formatting'
                    })
                
                # Check for specific error patterns
                if 'not all arguments converted' in line:
                    formatting_errors.append({
                        'file': file_path,
                        'line': line_num,
                        'content': line.strip(),
                        'type': 'error_message'
                    })
                
                # Look for problematic % formatting patterns
                if '%s' in line or '%d' in line or '%f' in line:
                    # Count % placeholders vs arguments
                    placeholders = len(re.findall(r'%[sdf]', line))
                    if placeholders > 0 and ('rider' in line.lower() or 'performance' in line.lower()):
                        formatting_errors.append({
                            'file': file_path,
                            'line': line_num,
                            'content': line.strip(),
                            'type': 'potential_formatting_issue',
                            'placeholders': placeholders
                        })
        
        except Exception as e:
            print(f"❌ Error reading {file_path}: {e}")
    
    print(f"📊 Found {len(formatting_errors)} potential formatting issues:")
    for error in formatting_errors:
        print(f"   📄 {error['file']}:{error['line']} ({error['type']})")
        print(f"      {error['content'][:100]}...")
    
    return formatting_errors

def search_rider_performance_functions():
    """Search for rider performance related functions"""
    
    print("\n🔍 SEARCHING FOR RIDER PERFORMANCE FUNCTIONS")
    print("=" * 50)
    
    rider_functions = []
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find functions related to rider performance
        function_pattern = r'def ([^(]*rider[^(]*performance[^(]*)\([^)]*\):'
        matches = re.findall(function_pattern, content, re.IGNORECASE)
        
        if matches:
            print(f"✅ Found {len(matches)} rider performance functions:")
            for func in matches:
                print(f"   • {func}")
                rider_functions.append(func)
        else:
            print("❌ No rider performance functions found")
        
        # Also search for any function that mentions riders
        rider_function_pattern = r'def ([^(]*rider[^(]*)\([^)]*\):'
        rider_matches = re.findall(rider_function_pattern, content, re.IGNORECASE)
        
        if rider_matches:
            print(f"✅ Found {len(rider_matches)} rider-related functions:")
            for func in rider_matches:
                print(f"   • {func}")
                if func not in rider_functions:
                    rider_functions.append(func)
    
    except Exception as e:
        print(f"❌ Error searching functions: {e}")
    
    return rider_functions

def find_specific_error_location():
    """Find the specific location of the string formatting error"""
    
    print("\n🎯 FINDING SPECIFIC ERROR LOCATION")
    print("=" * 50)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Search for the exact error message context
        error_contexts = []
        
        # Look for flash messages with string formatting
        flash_pattern = r"flash\([^)]*rider[^)]*performance[^)]*\)"
        flash_matches = re.finditer(flash_pattern, content, re.IGNORECASE)
        
        for match in flash_matches:
            start = max(0, match.start() - 200)
            end = min(len(content), match.end() + 200)
            context = content[start:end]
            
            # Find line number
            line_num = content[:match.start()].count('\n') + 1
            
            error_contexts.append({
                'type': 'flash_message',
                'line': line_num,
                'context': context,
                'match': match.group()
            })
        
        # Look for string formatting in rider-related routes
        route_pattern = r'@app\.route[^@]*rider[^@]*?def [^(]+\([^)]*\):[^@]*?(?=@app\.route|def [a-zA-Z_][a-zA-Z0-9_]*\(|if __name__|$)'
        route_matches = re.finditer(route_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for match in route_matches:
            route_content = match.group()
            if '%' in route_content and ('performance' in route_content.lower()):
                line_num = content[:match.start()].count('\n') + 1
                error_contexts.append({
                    'type': 'route_function',
                    'line': line_num,
                    'context': route_content[:500] + '...',
                    'match': 'Route with potential formatting issue'
                })
        
        print(f"📊 Found {len(error_contexts)} potential error locations:")
        for i, context in enumerate(error_contexts, 1):
            print(f"\n   {i}. {context['type']} at line {context['line']}")
            print(f"      {context['match']}")
            print(f"      Context: {context['context'][:100]}...")
        
        return error_contexts
    
    except Exception as e:
        print(f"❌ Error finding error location: {e}")
        return []

def fix_string_formatting_issues():
    """Fix identified string formatting issues"""
    
    print("\n🔧 FIXING STRING FORMATTING ISSUES")
    print("=" * 50)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_applied = 0
        
        # Common string formatting fixes
        formatting_fixes = [
            # Fix % formatting to .format() or f-strings
            {
                'pattern': r"flash\(f?['\"]Error loading rider performance: %s['\"], ([^)]+)\)",
                'replacement': r"flash(f'Error loading rider performance: {\1}', 'error')",
                'description': 'Fix rider performance error message formatting'
            },
            {
                'pattern': r"flash\(['\"]Error loading rider performance: %s['\"] % ([^,)]+)",
                'replacement': r"flash(f'Error loading rider performance: {\1}'",
                'description': 'Fix % string formatting in rider performance'
            },
            # Fix any remaining % formatting in rider contexts
            {
                'pattern': r"(['\"])([^'\"]*rider[^'\"]*performance[^'\"]*%s[^'\"]*)\1 % ",
                'replacement': r"f\1\2\1.replace('%s', '{}').format(",
                'description': 'Convert % formatting to f-string in rider performance'
            }
        ]
        
        for fix in formatting_fixes:
            if re.search(fix['pattern'], content, re.IGNORECASE):
                content = re.sub(fix['pattern'], fix['replacement'], content, flags=re.IGNORECASE)
                print(f"✅ Applied fix: {fix['description']}")
                fixes_applied += 1
            else:
                print(f"ℹ️ Pattern not found: {fix['description']}")
        
        # Look for and fix specific problematic patterns
        # Search for the exact error pattern
        error_pattern = r"flash\(['\"]Error loading rider performance: [^'\"]*['\"], ['\"]error['\"]"
        if re.search(error_pattern, content):
            # Replace with proper error handling
            content = re.sub(
                r"flash\(f?['\"]Error loading rider performance: \{?str\(e\)\}?['\"], ['\"]error['\"]",
                "flash(f'Error loading rider performance: {str(e)}', 'error')",
                content
            )
            print("✅ Fixed specific rider performance error message")
            fixes_applied += 1
        
        # Fix any remaining % formatting issues
        lines = content.split('\n')
        fixed_lines = []
        
        for line_num, line in enumerate(lines, 1):
            if 'rider' in line.lower() and 'performance' in line.lower() and '%' in line:
                # Check for problematic % formatting
                if '%s' in line and 'flash(' in line:
                    # Convert to f-string
                    if 'str(e)' in line:
                        fixed_line = line.replace('%s', '{str(e)}').replace("'Error", "f'Error")
                        if fixed_line != line:
                            print(f"✅ Fixed line {line_num}: % formatting to f-string")
                            fixes_applied += 1
                            line = fixed_line
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # Write back if changes were made
        if content != original_content:
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Applied {fixes_applied} string formatting fixes")
        else:
            print("ℹ️ No string formatting issues found to fix")
        
        return fixes_applied > 0
    
    except Exception as e:
        print(f"❌ Error fixing string formatting: {e}")
        return False

def test_rider_performance_route():
    """Test rider performance related routes"""
    
    print("\n🧪 TESTING RIDER PERFORMANCE ROUTES")
    print("=" * 50)
    
    test_routes = [
        '/riders/dashboard',
        '/delivery_analytics/performance_kpis',
        '/riders/performance'  # if it exists
    ]
    
    working_routes = 0
    
    for route in test_routes:
        try:
            import requests
            response = requests.get(f"http://127.0.0.1:3000{route}", timeout=10)
            
            print(f"🔍 Testing {route}: {response.status_code}")
            
            if response.status_code == 200:
                print(f"   ✅ Working")
                working_routes += 1
            elif response.status_code == 302:
                print(f"   🔄 Redirected (likely auth required)")
                working_routes += 1
            else:
                print(f"   ❌ Not working")
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"📊 {working_routes}/{len(test_routes)} routes working")
    return working_routes == len(test_routes)

def main():
    """Main execution"""
    
    print("🚀 FIXING STRING FORMATTING ERRORS")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Search for formatting errors
    formatting_errors = search_string_formatting_errors()
    
    # Search for rider performance functions
    rider_functions = search_rider_performance_functions()
    
    # Find specific error locations
    error_locations = find_specific_error_location()
    
    # Fix string formatting issues
    fixes_applied = fix_string_formatting_issues()
    
    # Test rider performance routes
    routes_working = test_rider_performance_route()
    
    print(f"\n🎯 STRING FORMATTING FIX SUMMARY")
    print("=" * 70)
    print(f"Formatting errors found: {len(formatting_errors)}")
    print(f"Rider functions found: {len(rider_functions)}")
    print(f"Error locations found: {len(error_locations)}")
    print(f"Fixes applied: {'✅ YES' if fixes_applied else '❌ NO'}")
    print(f"Routes working: {'✅ YES' if routes_working else '❌ NO'}")
    
    success = fixes_applied or len(formatting_errors) == 0
    
    if success:
        print(f"\n🎉 STRING FORMATTING ERRORS FIXED!")
        print(f"✅ No more 'not all arguments converted' errors")
    else:
        print(f"\n⚠️ Some formatting issues may remain")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    main()
