#!/usr/bin/env python3
"""
Test Dashboard Analytics Cards
"""

import requests

def test_dashboard_analytics():
    try:
        response = requests.get('http://127.0.0.1:3000/dashboard', timeout=10)
        print(f'Dashboard Status: {response.status_code}')
        
        if response.status_code == 200:
            print('Analytics cards present:')
            print(f'  Delivery Analytics: {"delivery_analytics" in response.text}')
            print(f'  Advanced Payment: {"advanced_payment" in response.text}')
            print(f'  Sales Analytics: {"sales_analytics" in response.text}')
            print(f'  Finance Dashboard: {"finance_dashboard" in response.text}')
            print(f'  Riders Dashboard: {"riders_dashboard" in response.text}')
            
            # Check for specific button text
            print(f'  "Open Dashboard" buttons: {response.text.count("Open Dashboard")}')
            print(f'  "Analytics & Advanced Management": {"Analytics & Advanced Management" in response.text}')
            
            return True
        else:
            print(f'Dashboard not accessible: {response.status_code}')
            return False
    
    except Exception as e:
        print(f'Error: {e}')
        return False

if __name__ == "__main__":
    test_dashboard_analytics()
