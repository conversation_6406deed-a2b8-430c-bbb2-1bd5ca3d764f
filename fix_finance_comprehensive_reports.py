#!/usr/bin/env python3
"""
Fix Finance Comprehensive Reports Route
Analyze and repair the partially broken finance comprehensive reports
"""

import os
import requests
from datetime import datetime

def analyze_finance_comprehensive_reports():
    """Analyze current finance comprehensive reports route"""
    
    print("🔍 ANALYZING FINANCE COMPREHENSIVE REPORTS")
    print("=" * 50)
    
    # Test current route
    try:
        response = requests.get("http://127.0.0.1:3000/finance/comprehensive-reports", timeout=10)
        print(f"Current status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Route is accessible")
            if 'error' in response.text.lower() or 'exception' in response.text.lower():
                print("⚠️ Route has errors in content")
                return 'has_errors'
            else:
                print("✅ Route appears to be working")
                return 'working'
        elif response.status_code == 404:
            print("❌ Route not found")
            return 'not_found'
        elif response.status_code == 500:
            print("❌ Server error")
            return 'server_error'
        else:
            print(f"⚠️ Unexpected status: {response.status_code}")
            return 'unexpected'
    
    except Exception as e:
        print(f"❌ Error testing route: {e}")
        return 'connection_error'

def find_finance_comprehensive_reports_route():
    """Find the current implementation of finance comprehensive reports"""
    
    print("\n🔍 FINDING CURRENT IMPLEMENTATION")
    print("=" * 50)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Look for the route
        import re
        
        # Find the route definition
        route_pattern = r"@app\.route\(['\"][^'\"]*comprehensive[^'\"]*['\"][^@]*?def [^(]+\([^)]*\):[^@]*?(?=@app\.route|def [a-zA-Z_][a-zA-Z0-9_]*\(|if __name__|$)"
        
        matches = re.findall(route_pattern, content, re.DOTALL)
        
        if matches:
            print(f"✅ Found {len(matches)} comprehensive reports implementations")
            for i, match in enumerate(matches, 1):
                print(f"   {i}. Found implementation ({len(match)} characters)")
                # Show first few lines
                lines = match.split('\n')[:5]
                for line in lines:
                    print(f"      {line.strip()}")
                print("      ...")
            return matches
        else:
            print("❌ No comprehensive reports route found")
            return []
    
    except Exception as e:
        print(f"❌ Error searching for route: {e}")
        return []

def check_finance_comprehensive_reports_template():
    """Check if the template exists and is valid"""
    
    print("\n🎨 CHECKING TEMPLATE")
    print("=" * 50)
    
    possible_templates = [
        'templates/finance/comprehensive_reports.html',
        'templates/finance/comprehensive-reports.html',
        'templates/reports/comprehensive.html',
        'templates/reports/finance_comprehensive.html'
    ]
    
    existing_templates = []
    
    for template_path in possible_templates:
        if os.path.exists(template_path):
            print(f"✅ Found template: {template_path}")
            existing_templates.append(template_path)
            
            # Check template content
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if len(content) < 100:
                    print(f"   ⚠️ Template is very short ({len(content)} characters)")
                elif 'extends' not in content:
                    print(f"   ⚠️ Template doesn't extend base template")
                else:
                    print(f"   ✅ Template looks valid ({len(content)} characters)")
            
            except Exception as e:
                print(f"   ❌ Error reading template: {e}")
        else:
            print(f"❌ Template not found: {template_path}")
    
    return existing_templates

def fix_finance_comprehensive_reports_route():
    """Fix or create the finance comprehensive reports route"""
    
    print("\n🔧 FIXING FINANCE COMPREHENSIVE REPORTS ROUTE")
    print("=" * 50)
    
    # Create a robust comprehensive reports route
    new_route_code = '''
@app.route('/finance/comprehensive-reports')
@login_required
def comprehensive_finance_reports():
    """Comprehensive Finance Reports Dashboard"""
    try:
        db = get_db()
        
        # Get comprehensive financial data
        current_date = datetime.now()
        
        # Financial summary
        financial_summary = db.execute("""
            SELECT 
                COUNT(DISTINCT o.order_id) as total_orders,
                SUM(o.total_amount) as total_revenue,
                COUNT(DISTINCT p.payment_id) as total_payments,
                SUM(p.amount) as total_collected
            FROM orders o
            LEFT JOIN payments p ON o.order_id = p.order_id
            WHERE o.status != 'Cancelled'
        """).fetchone()
        
        # Monthly revenue trend
        monthly_revenue = db.execute("""
            SELECT strftime('%Y-%m', order_date) as month,
                   COUNT(*) as order_count,
                   SUM(total_amount) as revenue
            FROM orders
            WHERE status != 'Cancelled'
            AND DATE(order_date) >= DATE('now', '-12 months')
            GROUP BY strftime('%Y-%m', order_date)
            ORDER BY month
        """).fetchall()
        
        # Outstanding payments
        outstanding_payments = db.execute("""
            SELECT o.order_id, o.customer_name, o.total_amount,
                   COALESCE(SUM(p.amount), 0) as paid_amount,
                   (o.total_amount - COALESCE(SUM(p.amount), 0)) as outstanding
            FROM orders o
            LEFT JOIN payments p ON o.order_id = p.order_id
            WHERE o.status != 'Cancelled'
            GROUP BY o.order_id
            HAVING outstanding > 0
            ORDER BY outstanding DESC
            LIMIT 20
        """).fetchall()
        
        # Payment methods analysis
        payment_methods = db.execute("""
            SELECT payment_method, 
                   COUNT(*) as transaction_count,
                   SUM(amount) as total_amount
            FROM payments
            WHERE DATE(payment_date) >= DATE('now', '-30 days')
            GROUP BY payment_method
            ORDER BY total_amount DESC
        """).fetchall()
        
        # Top customers by revenue
        top_customers = db.execute("""
            SELECT customer_name, 
                   COUNT(*) as order_count,
                   SUM(total_amount) as total_revenue
            FROM orders
            WHERE status != 'Cancelled'
            AND DATE(order_date) >= DATE('now', '-6 months')
            GROUP BY customer_name
            ORDER BY total_revenue DESC
            LIMIT 15
        """).fetchall()
        
        context = {
            'title': 'Comprehensive Finance Reports',
            'financial_summary': financial_summary,
            'monthly_revenue': monthly_revenue,
            'outstanding_payments': outstanding_payments,
            'payment_methods': payment_methods,
            'top_customers': top_customers,
            'current_user': current_user,
            'now': current_date
        }
        
        return render_template('finance/comprehensive_reports.html', **context)
        
    except Exception as e:
        flash(f'Error loading comprehensive finance reports: {str(e)}', 'error')
        return redirect(url_for('finance_dashboard'))

'''
    
    # Add the route to app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if route already exists and remove it
        import re
        existing_pattern = r"@app\.route\(['\"][^'\"]*comprehensive[^'\"]*['\"][^@]*?def [^(]+\([^)]*\):[^@]*?(?=@app\.route|def [a-zA-Z_][a-zA-Z0-9_]*\(|if __name__|$)"
        
        matches = re.findall(existing_pattern, content, re.DOTALL)
        if matches:
            print(f"🗑️ Removing {len(matches)} existing implementations")
            for match in matches:
                content = content.replace(match, "")
        
        # Add new route
        insertion_point = content.find('if __name__ == "__main__":')
        if insertion_point != -1:
            new_content = content[:insertion_point] + new_route_code + '\n\n' + content[insertion_point:]
            
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ Added fixed comprehensive reports route")
            return True
        else:
            print("❌ Could not find insertion point")
            return False
    
    except Exception as e:
        print(f"❌ Error fixing route: {e}")
        return False

def create_comprehensive_reports_template():
    """Create comprehensive reports template"""
    
    print("\n🎨 CREATING COMPREHENSIVE REPORTS TEMPLATE")
    print("=" * 50)
    
    # Ensure directory exists
    os.makedirs('templates/finance', exist_ok=True)
    
    template_content = '''{% extends 'base.html' %}

{% block title %}Comprehensive Finance Reports - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-pie text-primary"></i> Comprehensive Finance Reports
        </h1>
        <div>
            <a href="{{ url_for('advanced_payment.analytics') }}" class="btn btn-primary shadow-sm mr-2">
                <i class="fas fa-analytics fa-sm text-white-50"></i> Payment Analytics
            </a>
            <a href="{{ url_for('finance_dashboard') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Finance
            </a>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ financial_summary.total_orders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ "%.2f"|format(financial_summary.total_revenue or 0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Payments</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ financial_summary.total_payments or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Amount Collected</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ "%.2f"|format(financial_summary.total_collected or 0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class="row">
        <!-- Monthly Revenue Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Revenue Trend</h6>
                </div>
                <div class="card-body">
                    {% if monthly_revenue %}
                        <div class="chart-area">
                            <canvas id="monthlyRevenueChart"></canvas>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No monthly revenue data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Payment Methods Pie Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    {% if payment_methods %}
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="paymentMethodsChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small">
                            {% for method in payment_methods %}
                            <span class="mr-2">
                                <i class="fas fa-circle text-primary"></i> {{ method.payment_method }}
                            </span>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No payment method data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Outstanding Payments Table -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Outstanding Payments</h6>
                </div>
                <div class="card-body">
                    {% if outstanding_payments %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Total Amount</th>
                                        <th>Paid Amount</th>
                                        <th>Outstanding</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in outstanding_payments %}
                                    <tr>
                                        <td>{{ payment.order_id }}</td>
                                        <td>{{ payment.customer_name }}</td>
                                        <td>₹{{ "%.2f"|format(payment.total_amount) }}</td>
                                        <td>₹{{ "%.2f"|format(payment.paid_amount) }}</td>
                                        <td>₹{{ "%.2f"|format(payment.outstanding) }}</td>
                                        <td>
                                            <a href="{{ url_for('advanced_payment.bulk_processing') }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-credit-card"></i> Collect
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> No outstanding payments!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Customers</h6>
                </div>
                <div class="card-body">
                    {% if top_customers %}
                        {% for customer in top_customers %}
                        <div class="d-flex align-items-center border-bottom py-2">
                            <div class="mr-3">
                                <div class="icon-circle bg-primary">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">{{ customer.customer_name }}</div>
                                <div class="small text-gray-500">{{ customer.order_count }} orders</div>
                                <div class="small">₹{{ "%.2f"|format(customer.total_revenue) }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No customer data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Revenue Chart
    {% if monthly_revenue %}
    const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    new Chart(monthlyRevenueCtx, {
        type: 'line',
        data: {
            labels: [{% for month in monthly_revenue %}'{{ month.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Revenue',
                data: [{% for month in monthly_revenue %}{{ month.revenue or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // Payment Methods Chart
    {% if payment_methods %}
    const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
    new Chart(paymentMethodsCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for method in payment_methods %}'{{ method.payment_method }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for method in payment_methods %}{{ method.total_amount or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    {% endif %}
});
</script>
{% endblock %}'''
    
    try:
        with open('templates/finance/comprehensive_reports.html', 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        print("✅ Created comprehensive reports template")
        return True
    
    except Exception as e:
        print(f"❌ Error creating template: {e}")
        return False

def test_comprehensive_reports():
    """Test the fixed comprehensive reports route"""
    
    print("\n🧪 TESTING COMPREHENSIVE REPORTS")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:3000/finance/comprehensive-reports", timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Comprehensive reports working")
            return True
        else:
            print(f"❌ Not working: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main execution"""
    
    print("🚀 FIXING FINANCE COMPREHENSIVE REPORTS")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analyze current state
    current_status = analyze_finance_comprehensive_reports()
    
    # Find current implementation
    current_implementations = find_finance_comprehensive_reports_route()
    
    # Check templates
    existing_templates = check_finance_comprehensive_reports_template()
    
    # Fix route
    route_fixed = fix_finance_comprehensive_reports_route()
    
    # Create template
    template_created = create_comprehensive_reports_template()
    
    # Test fixed route
    reports_working = test_comprehensive_reports()
    
    print(f"\n🎯 COMPREHENSIVE REPORTS FIX SUMMARY")
    print("=" * 70)
    print(f"Initial status: {current_status}")
    print(f"Existing implementations: {len(current_implementations)}")
    print(f"Existing templates: {len(existing_templates)}")
    print(f"Route fixed: {'✅ YES' if route_fixed else '❌ NO'}")
    print(f"Template created: {'✅ YES' if template_created else '❌ NO'}")
    print(f"Reports working: {'✅ YES' if reports_working else '❌ NO'}")
    
    success = route_fixed and template_created and reports_working
    
    if success:
        print(f"\n🎉 COMPREHENSIVE REPORTS SUCCESSFULLY FIXED!")
    else:
        print(f"\n⚠️ Some issues occurred")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    main()
