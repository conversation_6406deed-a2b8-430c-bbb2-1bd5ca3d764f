{% extends 'base.html' %}

{% block title %}Customer Delivery Tracking - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-search text-primary"></i> Customer Delivery Tracking
        </h1>
        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <!-- Search Form -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Search Customer Deliveries</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('delivery_analytics.customer_delivery_tracking') }}">
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="mobile_number">Mobile Number</label>
                            <input type="text" class="form-control" id="mobile_number" name="mobile_number" 
                                   value="{{ request.args.get('mobile_number', '') }}" placeholder="Enter mobile number">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="customer_name">Customer Name</label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" 
                                   value="{{ request.args.get('customer_name', '') }}" placeholder="Enter customer name">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="po_number">PO Number</label>
                            <input type="text" class="form-control" id="po_number" name="po_number" 
                                   value="{{ request.args.get('po_number', '') }}" placeholder="Enter PO number">
                        </div>
                    </div>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Search Deliveries
                </button>
            </form>
        </div>
    </div>

    <!-- Results -->
    {% if customer_deliveries %}
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Delivery Results</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Mobile</th>
                            <th>Address</th>
                            <th>Status</th>
                            <th>Order Date</th>
                            <th>Rider</th>
                            <th>Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for delivery in customer_deliveries %}
                        <tr>
                            <td>{{ delivery.order_id }}</td>
                            <td>{{ delivery.customer_name }}</td>
                            <td>{{ delivery.mobile_number or 'N/A' }}</td>
                            <td>{{ delivery.delivery_address or 'N/A' }}</td>
                            <td>
                                <span class="badge badge-{% if delivery.status == 'Delivered' %}success{% elif delivery.status == 'Out for Delivery' %}warning{% else %}info{% endif %}">
                                    {{ delivery.status }}
                                </span>
                            </td>
                            <td>{{ delivery.order_date }}</td>
                            <td>{{ delivery.rider_name or 'Not Assigned' }}</td>
                            <td>Rs. {{ "%.2f"|format(delivery.order_value or 0) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% elif request.args.get('mobile_number') or request.args.get('customer_name') or request.args.get('po_number') %}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> No deliveries found for the specified search criteria.
    </div>
    {% endif %}
</div>
{% endblock %}