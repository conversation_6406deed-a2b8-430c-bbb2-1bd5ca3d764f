{"timestamp": "2025-07-22T12:18:20.990749", "summary": {"total_route_files": 16, "total_template_files": 190, "broken_routes": 0, "string_formatting_errors": 9, "database_issues": 1}, "detailed_results": {"routes_analysis": {"routes/advanced_payment.py": {"routes": ["/", "/bulk_processing", "/automated_matching", "/file_upload", "/reconciliation", "/analytics", "/process_bulk_payment", "/api/payment_stats"], "functions": ["get_db", "dashboard", "bulk_processing", "automated_matching", "file_upload", "reconciliation", "analytics", "process_bulk_payment", "api_payment_stats"], "templates": ["advanced_payment/dashboard.html", "advanced_payment/bulk_processing.html", "advanced_payment/automated_matching.html", "advanced_payment/file_upload.html", "advanced_payment/reconciliation.html", "advanced_payment/analytics.html"], "line_count": 381}, "routes/auth.py": {"routes": ["/login", "/logout", "/profile", "/profile/update"], "functions": ["login", "logout", "profile", "update_profile"], "templates": ["auth/login.html", "auth/profile.html"], "line_count": 109}, "routes/delivery_analytics.py": {"routes": ["/", "/real_time_tracking", "/performance_kpis", "/comprehensive_reports", "/api/delivery_stats"], "functions": ["get_db", "dashboard", "real_time_tracking", "performance_kpis", "comprehensive_reports", "api_delivery_stats"], "templates": ["delivery_analytics/dashboard.html", "delivery_analytics/real_time_tracking.html", "delivery_analytics/performance_kpis.html", "delivery_analytics/comprehensive_reports.html"], "line_count": 197}, "routes/divisions_modern.py": {"routes": ["/", "/api/list", "/create", "/<division_id>", "/<division_id>/edit", "/<division_id>/delete", "/analytics", "/export"], "functions": ["log_division_action", "validate_division_data", "index", "api_list", "create", "view", "edit", "delete", "analytics", "export"], "templates": ["divisions/modern_index.html", "divisions/modern_index.html", "divisions/create.html", "divisions/view.html", "divisions/edit.html", "divisions/analytics.html", "divisions/analytics.html"], "line_count": 693}, "routes/inventory.py": {"routes": ["/", "/new", "/<inventory_id>", "/product/<product_id>", "/low-stock", "/api/products-for-inventory", "/api/validate-product/<product_id>"], "functions": ["get_db", "index", "new_inventory", "view_inventory", "product_inventory", "low_stock", "api_products_for_inventory", "api_validate_product"], "templates": ["inventory/index.html", "inventory/new.html", "inventory/view.html", "inventory/product_inventory.html", "inventory/low_stock.html"], "line_count": 331}, "routes/modern_riders.py": {"routes": ["/", "/dashboard", "/tracking", "/api/rider/<rider_id>/performance", "/api/update_location", "/analytics", "/api/rider/<rider_id>/performance"], "functions": ["dashboard", "tracking", "api_rider_performance", "api_update_location", "analytics", "rider_performance_api", "not_found_error", "internal_error"], "templates": ["riders/modern_dashboard.html", "riders/modern_tracking.html", "riders/modern_analytics.html", "errors/404.html", "errors/500.html"], "line_count": 424}, "routes/notifications.py": {"routes": ["/", "/api/notifications", "/api/unread-count", "/api/mark-read/<int:notification_id>", "/api/mark-all-read", "/api/delete/<int:notification_id>", "/api/archive/<int:notification_id>", "/api/create", "/api/stats", "/test", "/api/create-test-notifications"], "functions": ["notification_center", "api_get_notifications", "api_unread_count", "api_mark_read", "api_mark_all_read", "api_delete_notification", "api_archive_notification", "api_create_notification", "api_notification_stats", "test_notifications", "api_create_test_notifications", "create_notification_helper"], "templates": ["notifications/notification_center.html", "notifications/test_notifications.html"], "line_count": 309}, "routes/orders.py": {"routes": ["/", "/new", "/<order_id>", "/<order_id>/update", "/workflow", "/<order_id>/approve", "/<order_id>/dispatch", "/<order_id>/deliver", "/<order_id>/cancel", "/search", "/<order_id>/history", "/<order_id>/invoice", "/<order_id>/challan"], "functions": ["get_db", "generate_order_id", "generate_order_item_id", "log_order_activity", "generate_order_id", "generate_invoice_number", "generate_dc_number", "index", "new_order", "view_order", "update_order", "workflow", "approve_order", "dispatch_order", "deliver_order", "cancel_order", "search", "view_history", "view_invoice", "view_challan"], "templates": ["orders/index.html", "orders/new.html", "orders/view.html", "orders/update.html", "orders/search_results.html", "orders/history.html", "orders/invoice.html", "orders/challan.html"], "line_count": 724}, "routes/orders_enhanced.py": {"routes": ["/update/<order_id>", "/search", "/history/<order_id>", "/invoice-challan/<order_id>", "/generate-invoice/<order_id>", "/generate-challan/<order_id>", "/dispatch/<order_id>", "/dispatch/<order_id>/assign", "/delivery/<order_id>", "/delivery/<order_id>/confirm"], "functions": ["login_required", "decorated_function", "update_order", "advanced_search", "order_history", "invoice_challan_management", "generate_invoice", "generate_challan", "dispatch_management", "assign_dispatch", "delivery_management", "confirm_delivery"], "templates": ["orders/enhanced_update.html", "orders/enhanced_search.html", "orders/enhanced_history.html", "orders/invoice_challan.html", "orders/dispatch_management.html", "orders/delivery_management.html"], "line_count": 618}, "routes/orders_minimal.py": {"routes": ["/", "/new", "/<order_id>", "/<order_id>/approve", "/<order_id>/update", "/workflow"], "functions": ["get_db", "generate_order_id", "generate_order_item_id", "index", "new_order", "view_order", "approve_order", "update_order", "workflow"], "templates": ["orders/new.html", "orders/view.html", "orders/update.html", "orders/workflow.html"], "line_count": 434}, "routes/permission_api.py": {"routes": ["/users/roles/<role>/permissions", "/users/roles/<role>/permissions", "/users/roles/<role>/apply-template", "/users/templates/<template>/permissions", "/users/permissions/matrix", "/users/roles/<role>/widget-permissions", "/users/permissions/audit-logs/<role>"], "functions": ["get_role_permissions", "update_role_permissions", "apply_role_template", "get_template_permissions_api", "get_permission_matrix", "update_widget_permissions", "get_role_audit_logs"], "templates": [], "line_count": 418}, "routes/products.py": {"routes": ["/product_management/", "/product_test/", "/new", "/<product_id>"], "functions": ["get_db", "product_management", "product_test", "new_product", "view_product"], "templates": ["products/index.html", "products/new.html", "products/view.html"], "line_count": 231}, "routes/sales_analytics.py": {"routes": ["/", "/salesperson_ledger", "/team_performance", "/division_ledger", "/division_analysis", "/api/sales_trends", "/api/division_performance"], "functions": ["get_db", "dashboard", "salesperson_ledger", "team_performance", "division_ledger", "division_analysis", "api_sales_trends", "api_division_performance"], "templates": ["sales_analytics/dashboard.html", "sales_analytics/salesperson_ledger.html", "sales_analytics/team_performance.html", "sales_analytics/division_ledger.html", "sales_analytics/division_analysis.html"], "line_count": 387}, "routes/tracking.py": {"routes": ["/track", "/track/<tracking_number>", "/track", "/api/track/bulk", "/api/track/bulk/async", "/api/track/pdf", "/api/track/bulk/pdf"], "functions": ["get_text", "is_valid_tracking_data", "has_meaningful_tracking_data", "scrape_tcs_tracking", "scrape_tcs_tracking_async", "get_text_async", "track_form", "track_number", "track_post", "bulk_track", "bulk_track_async", "process_bulk_tracking_async", "track_with_semaphore", "generate_tracking_pdf", "generate_single_pdf", "generate_bulk_pdf"], "templates": ["tracking/track_form.html", "tracking/track_result.html", "tracking/track_result.html"], "line_count": 823}, "routes/users.py": {"routes": ["/manage", "/add", "/edit/<int:user_id>", "/delete/<int:user_id>", "/toggle-status/<int:user_id>", "/reset-password/<int:user_id>", "/roles", "/permissions/<role>", "/permissions/sync", "/roles/<role>/default-permissions", "/logs"], "functions": ["get_db", "log_activity", "manage", "add", "edit", "delete", "toggle_status", "reset_password", "roles", "permissions", "sync_permissions", "restore_default_permissions", "logs"], "templates": ["users/manage.html", "users/add.html", "users/edit.html", "users/reset_password.html", "users/logs.html"], "line_count": 461}, "app.py": {"routes": ["/favicon.ico", "/warehouse", "/warehouses", "/warehouse/reports", "/customers", "/customers/new", "/reports", "/orders/create-with-workflow", "/orders/<order_id>/workflow", "/orders/<order_id>/reject", "/setup-real-data", "/comprehensive-system-test", "/test-customer-ledger-buttons", "/", "/test", "/debug-routes", "/orders-fixed", "/login", "/logout", "/dashboard", "/api/dashboard/analytics", "/orders", "/orders/", "/orders/search", "/orders/dispatch", "/new-order", "/orders/new", "/orders/<order_id>/update", "/orders/workflow", "/orders/<order_id>/approve", "/orders/<order_id>/generate-challan", "/orders/<order_id>/dispatch", "/orders/<order_id>/deliver", "/orders/<order_id>/history", "/orders/<order_id>/invoice", "/orders/<order_id>/invoice/view", "/orders/<order_id>/challan", "/orders/<order_id>/challan/view", "/orders/<order_id>/allocate-inventory", "/orders/<order_id>/auto-allocate", "/orders/<order_id>/manual-allocate", "/orders/<order_id>/release-allocation", "/products", "/products/", "/products/view_all", "/products/view_all/", "/product_management", "/products/add_enhanced", "/products/add_enhanced/", "/products/<product_id>", "/products/<product_id>/toggle-status", "/finance/process-payment", "/finance/customer-statement/<customer_id>", "/finance/aging-report", "/finance/payment-history", "/finance/financial-reports", "/api/search-suggestions", "/search", "/api/v1/orders", "/api/v1/orders", "/api/v1/orders/<order_id>", "/api/v1/customers", "/api/v1/customers/<customer_id>", "/api/v1/products", "/api/v1/inventory/stock-levels", "/products/new", "/products/update_selection", "/products/<product_id>/update", "/products/<product_id>/delete", "/inventory", "/inventory/", "/inventory/new", "/inventory/transfer", "/inventory/<inventory_id>/toggle-status", "/inventory/<inventory_id>/history", "/reports/low-stock", "/reports/expiry", "/reports/stock-movements", "/reports/product-inventory", "/reports/warehouse-inventory", "/reports/warehouse-performance", "/reports/sales/daily", "/reports/sales/monthly", "/reports/sales/weekly", "/import_data", "/settings", "/export_orders_excel", "/remove_duplicate_products", "/export_inventory_excel", "/reports/", "/inventory_report", "/sales_report/monthly", "/sales_report/daily", "/sales_report/weekly", "/weekly_sales_report", "/reports/custom-date-range", "/reports/sales-by-agent", "/reports/product-performance", "/reports/customer-purchase-history", "/reports/delivery-performance", "/reports/stock-movements", "/reports/expiry", "/reports/financial-summary", "/reports/sales-team-performance", "/riders/orders", "/riders/update_status", "/riders/claim_pickup", "/api/order-details/<order_id>", "/sales-team", "/sales-team/division/<division_name>", "/finance/<view>", "/finance/pending-invoices", "/finance/customer-ledger", "/finance/payment-collection", "/finance/pending-invoices", "/finance/add-bank-detail", "/finance/customer-ledger-old", "/finance/credit-hold", "/backup_database", "/customers/export", "/customers/bulk-action", "/customers/add", "/export-tax-report/<format>", "/export-ceo-dashboard/<format>", "/dashboard/ceo", "/reports/sales-analytics", "/reports/inventory-analytics", "/reports/division-performance", "/reports/trend-analysis", "/export-receivables/<format>", "/record-payment-old", "/search-invoice", "/settings_admin", "/settings", "/settings/update", "/reports/daily_sales", "/reports/monthly_sales", "/reports/product_performance", "/ceo_dashboard", "/reports/low_stock", "/reports/customer_purchase_history", "/reports/delivery_performance", "/reports/financial_summary", "/reports/sales_team_performance", "/reports/warehouse_performance", "/reports/warehouse-utilization", "/reports/low-stock-alert", "/reports/monthly-summary", "/riders", "/riders/register", "/riders/<rider_id>/view", "/riders/<rider_id>/edit", "/riders/<rider_id>/approve", "/riders/<rider_id>/suspend", "/riders/<rider_id>/delete", "/riders/export", "/riders/bulk_approve", "/admin/rider-tracking", "/admin/rider_tracking", "/rider/dashboard", "/riders/dashboard", "/riders/delivery-routes", "/rider/delivery_routes", "/riders/performance", "/rider/performance", "/reports/sales_analytics", "/reports/inventory_analytics", "/reports/division_performance", "/reports/trend_analysis", "/finance/record_payment", "/finance/knock_off_payment", "/orders/place", "/orders/pending", "/orders/completed", "/orders/pending-approval", "/workflow", "/products/add", "/products/import", "/reports/monthly", "/api/track-order/<order_id>", "/api/track_order/<order_id>", "/track-order", "/track_order", "/orders/<order_id>/view", "/finance/accounts_receivable", "/finance/view_invoice/<order_id>", "/finance/customer_ledger_data", "/view_invoice_by_id/<invoice_id>", "/download_invoice/<invoice_id>", "/users/new", "/users/permissions", "/inventory/add", "/warehouse", "/warehouse/<warehouse_id>/edit", "/warehouse/<warehouse_id>/delete", "/warehouse/<warehouse_id>/toggle-status", "/notifications", "/api/notifications", "/notifications/<int:notification_id>/mark-read", "/notifications/mark-all-read", "/api/notifications/<int:notification_id>/read", "/api/notifications/<int:notification_id>/unread", "/api/notifications/mark-all-read", "/api/notifications/create-test", "/test-notifications", "/order_workflow", "/delivery_challans", "/generate_dc/<order_id>", "/warehouse/mark_ready_for_pickup", "/warehouse/set_priority", "/api/upload-image", "/api/product/<int:product_id>/images", "/api/image/<file_id>", "/api/image/<file_id>/download", "/analytics/sales", "/analytics/inventory", "/analytics/division_performance", "/analytics/trend_analysis", "/export/warehouse_inventory", "/export/warehouse_performance", "/users", "/finance/ledger", "/api/dashboard-data", "/api/finance-data", "/api/sales-data", "/api/ceo-dashboard-data", "/executive-dashboard", "/reports/sales", "/reports/financial", "/reports/customer", "/reports/division", "/reports/inventory", "/finance/receivables", "/reports/top-customers-revenue", "/reports/division-revenue-comparison", "/reports/agent-performance-ranking", "/finance/invoice/<invoice_number>", "/api/products", "/api/orders", "/api/generate-seaborn-charts", "/api/division-team-details/<division_name>", "/test-ceo-data", "/rider/pickup_order", "/rider/deliver_order", "/api/live_orders", "/admin/settings/save", "/api/customer-search", "/orders/<order_id>/approval", "/dc-pending", "/workflow-management", "/dc-generate/<order_id>", "/profile", "/advanced_search", "/bulk_operations", "/data_export", "/system_health", "/feedback", "/workflow/management", "/finance/test", "/finance/debug", "/finance", "/finance/", "/finance/dashboard", "/finance/pending-invoices", "/finance/customer-ledger", "/finance/payment-collection", "/finance/api/generate-invoice", "/finance/process-payment", "/finance/api/stats", "/finance/api/generate-pdf-report", "/finance/api/payment-trends", "/finance/api/payment-details/<payment_id>", "/finance/api/payment-history/<order_id>", "/finance/api/put-order-on-hold", "/finance/comprehensive-reports", "/finance/advanced-analytics", "/finance/duplicate-detection", "/finance/api/generate-comprehensive-report", "/api/intelligent-search", "/finance/payment/<payment_id>/attachments", "/finance/payment/<payment_id>/add-attachment", "/finance/payment/<payment_id>/remove-attachment/<int:attachment_id>", "/finance/api/payment-knockoff", "/finance/api/get-payment-knockoffs/<payment_id>", "/finance/accounts-receivable", "/finance/accounts-payable", "/orders/duplicate-detection", "/orders/api/resolve-duplicate", "/riders/performance", "/admin/bulk_operations", "/admin/data_export", "/admin/system_health", "/reports/advanced/index", "/reports/sales/index", "/favicon.ico", "/warehouse", "/warehouses", "/warehouse/reports", "/customers", "/customers/new", "/reports", "/orders/create-with-workflow", "/orders/<order_id>/workflow", "/orders/<order_id>/reject", "/setup-real-data", "/comprehensive-system-test", "/test-customer-ledger-buttons", "/", "/test", "/debug-routes", "/orders-fixed", "/login", "/logout", "/dashboard", "/api/dashboard/analytics", "/orders", "/orders/", "/orders/search", "/orders/dispatch", "/new-order", "/orders/new", "/orders/<order_id>/update", "/orders/workflow", "/orders/<order_id>/approve", "/orders/<order_id>/generate-challan", "/orders/<order_id>/dispatch", "/orders/<order_id>/deliver", "/orders/<order_id>/history", "/orders/<order_id>/invoice", "/orders/<order_id>/invoice/view", "/orders/<order_id>/challan", "/orders/<order_id>/challan/view", "/orders/<order_id>/allocate-inventory", "/orders/<order_id>/auto-allocate", "/orders/<order_id>/manual-allocate", "/orders/<order_id>/release-allocation", "/products", "/products/", "/products/view_all", "/products/view_all/", "/product_management", "/products/add_enhanced", "/products/add_enhanced/", "/products/<product_id>", "/products/<product_id>/toggle-status", "/finance/process-payment", "/finance/customer-statement/<customer_id>", "/finance/aging-report", "/finance/payment-history", "/finance/financial-reports", "/api/search-suggestions", "/search", "/api/v1/orders", "/api/v1/orders", "/api/v1/orders/<order_id>", "/api/v1/customers", "/api/v1/customers/<customer_id>", "/api/v1/products", "/api/v1/inventory/stock-levels", "/products/new", "/products/update_selection", "/products/<product_id>/update", "/products/<product_id>/delete", "/inventory", "/inventory/", "/inventory/new", "/inventory/transfer", "/inventory/<inventory_id>/toggle-status", "/inventory/<inventory_id>/history", "/reports/low-stock", "/reports/expiry", "/reports/stock-movements", "/reports/product-inventory", "/reports/warehouse-inventory", "/reports/warehouse-performance", "/reports/sales/daily", "/reports/sales/monthly", "/reports/sales/weekly", "/import_data", "/settings", "/export_orders_excel", "/remove_duplicate_products", "/export_inventory_excel", "/reports/", "/inventory_report", "/sales_report/monthly", "/sales_report/daily", "/sales_report/weekly", "/weekly_sales_report", "/reports/custom-date-range", "/reports/sales-by-agent", "/reports/product-performance", "/reports/customer-purchase-history", "/reports/delivery-performance", "/reports/stock-movements", "/reports/expiry", "/reports/financial-summary", "/reports/sales-team-performance", "/riders/orders", "/riders/update_status", "/riders/claim_pickup", "/api/order-details/<order_id>", "/sales-team", "/sales-team/division/<division_name>", "/finance/<view>", "/finance/pending-invoices", "/finance/customer-ledger", "/finance/payment-collection", "/finance/pending-invoices", "/finance/add-bank-detail", "/finance/customer-ledger-old", "/finance/credit-hold", "/backup_database", "/customers/export", "/customers/bulk-action", "/customers/add", "/export-tax-report/<format>", "/export-ceo-dashboard/<format>", "/dashboard/ceo", "/reports/sales-analytics", "/reports/inventory-analytics", "/reports/division-performance", "/reports/trend-analysis", "/export-receivables/<format>", "/record-payment-old", "/search-invoice", "/settings_admin", "/settings", "/settings/update", "/reports/daily_sales", "/reports/monthly_sales", "/reports/product_performance", "/ceo_dashboard", "/reports/low_stock", "/reports/customer_purchase_history", "/reports/delivery_performance", "/reports/financial_summary", "/reports/sales_team_performance", "/reports/warehouse_performance", "/reports/warehouse-utilization", "/reports/low-stock-alert", "/reports/monthly-summary", "/riders", "/riders/register", "/riders/<rider_id>/view", "/riders/<rider_id>/edit", "/riders/<rider_id>/approve", "/riders/<rider_id>/suspend", "/riders/<rider_id>/delete", "/riders/export", "/riders/bulk_approve", "/admin/rider-tracking", "/admin/rider_tracking", "/rider/dashboard", "/riders/dashboard", "/riders/delivery-routes", "/rider/delivery_routes", "/riders/performance", "/rider/performance", "/reports/sales_analytics", "/reports/inventory_analytics", "/reports/division_performance", "/reports/trend_analysis", "/finance/record_payment", "/finance/knock_off_payment", "/orders/place", "/orders/pending", "/orders/completed", "/orders/pending-approval", "/workflow", "/products/add", "/products/import", "/reports/monthly", "/api/track-order/<order_id>", "/api/track_order/<order_id>", "/track-order", "/track_order", "/orders/<order_id>/view", "/finance/accounts_receivable", "/finance/view_invoice/<order_id>", "/finance/customer_ledger_data", "/view_invoice_by_id/<invoice_id>", "/download_invoice/<invoice_id>", "/users/new", "/users/permissions", "/inventory/add", "/warehouse", "/warehouse/<warehouse_id>/edit", "/warehouse/<warehouse_id>/delete", "/warehouse/<warehouse_id>/toggle-status", "/notifications", "/api/notifications", "/notifications/<int:notification_id>/mark-read", "/notifications/mark-all-read", "/api/notifications/<int:notification_id>/read", "/api/notifications/<int:notification_id>/unread", "/api/notifications/mark-all-read", "/api/notifications/create-test", "/test-notifications", "/order_workflow", "/delivery_challans", "/generate_dc/<order_id>", "/warehouse/mark_ready_for_pickup", "/warehouse/set_priority", "/api/upload-image", "/api/product/<int:product_id>/images", "/api/image/<file_id>", "/api/image/<file_id>/download", "/analytics/sales", "/analytics/inventory", "/analytics/division_performance", "/analytics/trend_analysis", "/export/warehouse_inventory", "/export/warehouse_performance", "/users", "/finance/ledger", "/api/dashboard-data", "/api/finance-data", "/api/sales-data", "/api/ceo-dashboard-data", "/executive-dashboard", "/reports/sales", "/reports/financial", "/reports/customer", "/reports/division", "/reports/inventory", "/finance/receivables", "/reports/top-customers-revenue", "/reports/division-revenue-comparison", "/reports/agent-performance-ranking", "/finance/invoice/<invoice_number>", "/api/products", "/api/orders", "/api/generate-seaborn-charts", "/api/division-team-details/<division_name>", "/test-ceo-data", "/rider/pickup_order", "/rider/deliver_order", "/api/live_orders", "/admin/settings/save", "/api/customer-search", "/orders/<order_id>/approval", "/dc-pending", "/workflow-management", "/dc-generate/<order_id>", "/profile", "/advanced_search", "/bulk_operations", "/data_export", "/system_health", "/feedback", "/workflow/management", "/finance/test", "/finance/debug", "/finance", "/finance/", "/finance/dashboard", "/finance/pending-invoices", "/finance/customer-ledger", "/finance/payment-collection", "/finance/api/generate-invoice", "/finance/process-payment", "/finance/api/stats", "/finance/api/generate-pdf-report", "/finance/api/payment-trends", "/finance/api/payment-details/<payment_id>", "/finance/api/payment-history/<order_id>", "/finance/api/put-order-on-hold", "/finance/comprehensive-reports", "/finance/advanced-analytics", "/finance/duplicate-detection", "/finance/api/generate-comprehensive-report", "/api/intelligent-search", "/finance/payment/<payment_id>/attachments", "/finance/payment/<payment_id>/add-attachment", "/finance/payment/<payment_id>/remove-attachment/<int:attachment_id>", "/finance/api/payment-knockoff", "/finance/api/get-payment-knockoffs/<payment_id>", "/finance/accounts-receivable", "/finance/accounts-payable", "/orders/duplicate-detection", "/orders/api/resolve-duplicate", "/riders/performance", "/admin/bulk_operations", "/admin/data_export", "/admin/system_health", "/reports/advanced/index", "/reports/sales/index"], "functions": ["safe_strftime", "generate_invoice_number", "sanitize_input", "validate_csrf_token", "generate_csrf_token", "hash_password_secure", "verify_password_secure", "__init__", "is_allowed", "cleanup_old_entries", "__init__", "get_connection", "return_connection", "set_security_headers", "safe_now", "safe_datetime_for_template", "format_currency", "safe_percentage", "safe_rating", "safe_currency", "safe_number", "format_amount", "format_number", "format_datetime", "date_only", "check_permissions", "__init__", "check_password", "is_active", "get_db", "allowed_file", "generate_file_id", "generate_product_id", "create_thumbnail", "save_uploaded_image", "get_product_images", "close_db", "load_user", "can_view_menu", "inject_permissions", "__init__", "get_available_inventory", "automatic_allocation", "manual_allocation", "release_allocation", "__init__", "create_order_workflow", "process_order_workflow", "validate_order", "check_customer_credit", "check_inventory_availability", "auto_approve_order", "auto_allocate_inventory", "create_audit_log", "get_order_workflow_status", "favicon", "warehouses", "warehouse_reports", "customers", "customers_by_type", "customers_pricing_list", "customer_pricing", "customer_orders", "all_customer_orders", "new_customer", "reports_dashboard", "create_order_with_workflow", "view_order_workflow", "reject_order_manual", "setup_real_data", "comprehensive_system_test", "test_customer_ledger_buttons", "index", "test_route", "debug_routes", "orders_fixed", "login", "logout", "dashboard", "dashboard_analytics_api", "orders", "search_orders", "dispatch_orders", "new_order", "update_order", "workflow", "approve_order", "generate_challan", "dispatch_order", "deliver_order", "view_order_history", "view_invoice", "display_invoice", "view_challan", "display_challan", "allocate_inventory_page", "auto_allocate_inventory", "manual_allocate_inventory", "release_allocation", "products", "view_all_products", "product_management", "__init__", "iter_pages", "add_product_enhanced", "get_generic_categories", "get_manufacturers", "get_divisions", "view_product", "toggle_product_status", "__init__", "process_payment", "update_customer_ledger", "get_customer_statement", "get_aging_report", "process_payment", "customer_statement", "aging_report", "payment_history", "financial_reports", "__init__", "get_search_suggestions", "advanced_search", "search_suggestions", "universal_search", "api_get_orders", "api_create_order", "api_get_order", "api_get_customers", "api_get_customer", "api_get_products", "api_get_stock_levels", "new_product", "update_product_selection", "__init__", "iter_pages", "update_product", "delete_product", "inventory", "new_stock", "transfer_stock", "toggle_inventory_status", "inventory_history", "low_stock_report", "expiry_report_old", "stock_movement_report", "product_inventory_report", "warehouse_inventory_report", "warehouse_performance_report", "daily_sales_report", "monthly_sales_report", "weekly_sales_report", "import_data", "settings", "export_orders_excel", "remove_duplicate_products", "export_inventory_excel", "reports_index", "inventory_report", "monthly_sales_report_new", "daily_sales_report_new", "__init__", "weekly_sales_report_new", "__init__", "custom_date_range_report", "__init__", "sales_by_agent_report", "product_performance_report", "__init__", "customer_purchase_history", "delivery_performance_report", "stock_movements", "expiry_report", "financial_summary_report", "sales_team_performance", "riders_orders", "update_delivery_status", "claim_pickup", "get_order_details", "sales_team_dashboard", "sales_division_detail", "finance_with_view", "new_modern_pending_invoices_DISABLED", "new_modern_customer_ledger_DISABLED", "new_modern_payment_collection_DISABLED", "finance_pending_invoices_DISABLED", "finance_add_bank_detail", "old_customer_ledger", "orphaned_financial_reports_DISABLED", "finance_credit_hold", "backup_database", "export_customers", "customers_bulk_action", "add_customer", "export_tax_report", "export_ceo_dashboard", "calculate_efficiency_score", "calculate_trend", "ceo_dashboard", "get_date_range", "calculate_efficiency_score", "sales_analytics", "inventory_analytics", "division_performance_chart", "trend_analysis", "export_receivables", "record_payment_old", "search_invoice", "settings_admin", "update_settings", "daily_sales", "monthly_sales", "product_performance", "ceo_dashboard_redirect", "low_stock_alias", "customer_purchase_history_alias", "delivery_performance_alias", "financial_summary_alias", "sales_team_performance_alias", "warehouse_performance_alias", "warehouse_utilization_report", "low_stock_alert_report", "monthly_summary_report", "riders", "register_rider", "view_rider", "edit_rider", "approve_rider", "suspend_rider", "delete_rider", "export_riders", "bulk_approve_riders", "admin_rider_tracking", "rider_dashboard", "rider_delivery_routes", "rider_performance", "sales_analytics_alias", "inventory_analytics_alias", "division_performance_alias", "trend_analysis_alias", "record_payment", "generate_payment_id", "knock_off_payment", "orders_place", "orders_pending", "orders_completed", "orders_pending_approval", "workflow_alias", "add_product", "products_import", "reports_monthly", "api_track_order", "format_date", "api_track_order_alias", "track_order_page", "track_order_page_alias", "view_order", "finance_accounts_receivable", "finance_view_invoice", "finance_customer_ledger_data", "view_invoice_by_id", "download_invoice", "users_new", "users_permissions", "inventory_add", "warehouse", "edit_warehouse", "delete_warehouse", "toggle_warehouse_status", "notifications", "api_notifications", "mark_notification_read", "mark_all_notifications_read", "api_mark_notification_read", "api_mark_notification_unread", "api_mark_all_notifications_read", "create_test_notification_api", "test_notifications", "create_notification", "create_system_notification", "order_workflow_alias", "delivery_challans", "generate_dc", "mark_ready_for_pickup", "set_order_priority", "upload_image", "get_product_images_api", "serve_image", "download_image", "analytics_sales", "analytics_inventory", "analytics_division_performance", "analytics_trend_analysis", "export_warehouse_inventory", "export_warehouse_performance", "users_index", "finance_ledger", "api_dashboard_data", "api_finance_data", "api_sales_data", "api_ceo_dashboard_data", "executive_dashboard", "reports_sales", "reports_financial", "reports_customer", "reports_division", "reports_inventory", "finance_receivables", "top_customers_revenue_report", "division_revenue_comparison_report", "agent_performance_ranking_report", "invoice_detail", "api_products", "api_orders", "generate_seaborn_charts", "division_team_details", "test_ceo_data", "rider_pickup_order", "rider_deliver_order", "api_live_orders", "save_system_settings", "api_customer_search", "order_approval", "dc_pending", "workflow_management", "dc_generate", "initialize_system_notifications", "user_profile", "advanced_search", "bulk_operations", "data_export", "system_health", "feedback", "workflow_management_alt", "__init__", "get_dashboard_stats", "get_pending_invoices", "get_customer_ledger", "get_payment_collection_data", "get_customer_ledger_enhanced", "get_aging_analysis_data", "get_pending_invoices_enhanced", "finance_test", "finance_debug", "finance_dashboard", "finance_pending_invoices_management", "finance_customer_ledger", "finance_payment_collection", "finance_generate_invoice", "finance_process_payment", "finance_api_stats", "generate_pdf_report", "payment_trends", "payment_details_api", "payment_history_api", "put_order_on_hold", "comprehensive_finance_reports", "advanced_financial_analytics", "duplicate_detection", "generate_comprehensive_report", "generate_report_data", "generate_pdf_comprehensive_report", "generate_excel_comprehensive_report", "intelligent_search", "search_customers", "search_orders", "search_products", "search_finance", "calculate_relevance_score", "generate_search_suggestions", "view_payment_attachments", "add_payment_attachment", "remove_payment_attachment", "payment_knockoff_api", "get_payment_knockoffs", "accounts_receivable", "accounts_payable", "duplicate_order_detection", "detect_duplicate_orders", "resolve_duplicate_order", "log_duplicate_resolution", "rider_performance_dashboard", "admin_bulk_operations", "admin_data_export", "admin_system_health", "advanced_reports_index", "sales_reports_index"], "templates": ["warehouses/index.html", "warehouses/index.html", "warehouses/index.html", "warehouse/reports.html", "customers/add_customer.html", "customers/set_pricing.html", "customers/customers_list.html", "customers/by_type.html", "customers/pricing.html", "customers/set_pricing.html", "customers/orders.html", "customers/all_orders.html", "customers/add_customer.html", "reports/reports_dashboard.html", "orders/workflow_status.html", "login.html", "login.html", "dashboard.html", "dashboard.html", "orders/index.html", "orders/index.html", "orders/new.html", "orders/update.html", "orders/workflow.html", "orders/generate_challan.html", "orders/dispatch.html", "orders/deliver.html", "orders/history.html", "orders/invoice.html", "orders/challan.html", "orders/allocate_inventory.html", "products/index.html", "products/view_all_products.html", "products/product_management.html", "products/add_product_enhanced.html", "products/add_product_enhanced.html", "products/add_product_enhanced.html", "products/product_details.html", "finance/customer_statement.html", "finance/aging_report.html", "finance/payment_history.html", "finance/financial_reports.html", "search/universal_search.html", "products/new.html", "products/update_selection.html", "products/update.html", "inventory/index.html", "inventory/new.html", "inventory/transfer.html", "inventory/history.html", "reports/low_stock.html", "reports/expiry.html", "reports/stock_movements.html", "reports/product_inventory.html", "reports/warehouse_inventory.html", "reports/warehouse_performance.html", "reports/sales/daily.html", "reports/monthly_sales.html", "reports/sales/weekly.html", "import_data.html", "settings.html", "reports/product_inventory.html", "reports/monthly_sales.html", "reports/daily_sales.html", "reports/weekly_sales.html", "reports/custom_date_range.html", "reports/sales_by_agent.html", "reports/product_performance.html", "reports/customer_purchase_history.html", "reports/delivery_performance.html", "reports/stock_movements.html", "reports/expiry_report.html", "reports/advanced/financial_summary.html", "reports/sales_team_performance.html", "riders/orders.html", "riders/orders.html", "sales_team/dashboard.html", "sales_team/dashboard.html", "sales_team/division_detail.html", "sales_team/division_detail.html", "finance/pending_invoices.html", "finance/customer_ledger.html", "finance/payment_collection.html", "finance/modern_pending_invoices.html", "finance/generate_invoice.html", "finance/financial_reports.html", "dashboard/ceo.html", "reports/sales_analytics.html", "reports/inventory_analytics.html", "reports/division_performance.html", "reports/trend_analysis.html", "finance/search_results.html", "finance/search_results.html", "admin/settings.html", "reports/warehouse_utilization.html", "reports/low_stock_alert.html", "reports/monthly_summary.html", "riders/index.html", "riders/register_simple.html", "riders/view.html", "riders/edit.html", "admin/rider_tracking.html", "riders/dashboard.html", "riders/delivery_routes.html", "riders/performance.html", "finance/invoice_details.html", "orders/index.html", "orders/index.html", "track_order.html", "track_order.html", "orders/view.html", "finance/accounts_receivable.html", "users/new.html", "users/permissions.html", "inventory/add.html", "warehouse/index.html", "warehouse/edit.html", "notifications/index.html", "test_notifications.html", "delivery_challans/index.html", "reports/top_customers_revenue.html", "reports/division_revenue_comparison.html", "reports/agent_performance_ranking.html", "finance/invoice_detail.html", "orders/approval.html", "warehouse/dc_pending.html", "workflow/management.html", "warehouse/dc_generate.html", "users/profile.html", "search/advanced.html", "admin/bulk_operations.html", "admin/data_export.html", "admin/system_health.html", "admin/feedback.html", "finance/modern_dashboard.html", "finance/invoice_generation_new.html", "finance/aging_analysis.html", "finance/customer_ledger_enhanced.html", "finance/payment_collection_new.html", "finance/comprehensive_reports.html", "finance/advanced_analytics.html", "finance/duplicate_detection.html", "finance/payment_attachments.html", "finance/accounts_receivable.html", "finance/accounts_payable.html", "orders/duplicate_detection.html", "riders/performance.html", "admin/bulk_operations.html", "admin/data_export.html", "admin/system_health.html", "reports/advanced/index.html", "reports/sales/index.html"], "line_count": 21011}}, "templates_analysis": {"templates\\404.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dashboard"], "extends": ["base.html"], "includes": [], "line_count": 25}, "templates\\500.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dashboard"], "extends": ["base.html"], "includes": [], "line_count": 25}, "templates\\base.html": {"href_links": ["https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css", "https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css", "https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css", "https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css", "{{ url_for(", "{{ url_for(", "#orderSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#productSubmenu", "{{ url_for(", "/product_management", "/products/new", "{{ url_for(", "/products/view_all/", "#warehouseSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#rider<PERSON><PERSON><PERSON><PERSON>", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#reportsSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#financeSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#salesSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#userSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#divisionSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#customerSubmenu", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#", "/notifications", "/notifications", "#", "#", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#"], "url_for_calls": ["dashboard", "dashboard", "new_order", "orders", "orders", "workflow", "workflow", "workflow", "orders", "orders", "products", "update_product_selection", "warehouses", "inventory", "delivery_challans", "warehouse_reports", "riders", "register_rider", "rider_delivery_routes", "rider_performance", "rider_dashboard", "admin_rider_tracking", "tracking.track_form", "daily_sales_report_new", "weekly_sales_report_new", "monthly_sales_report_new", "custom_date_range_report", "sales_by_agent_report", "product_performance_report", "inventory_report", "reports", "finance_dashboard", "finance_pending_invoices_management", "finance_customer_ledger", "finance_payment_collection", "financial_reports", "comprehensive_finance_reports", "sales_team_dashboard", "sales_division_detail", "sales_division_detail", "sales_division_detail", "sales_division_detail", "sales_by_agent_report", "users_index", "users_new", "users_permissions", "settings", "users_permissions", "divisions.index", "divisions.analytics", "divisions.export", "customers", "customers", "customers", "customers", "customers", "customers", "import_data", "settings", "track_order_page", "logout", "dashboard", "orders", "workflow", "products", "inventory", "reports", "dashboard", "settings", "dashboard", "static", "static"], "extends": [], "includes": ["components/intelligent_search.html"], "line_count": 1943}, "templates\\dashboard.html": {"href_links": ["{{ url_for(", "{{ url_for(", "#", "#", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#executive-dashboard", "#sales-analytics", "#division-analytics", "#inventory-analytics", "#financial-analytics", "#operational-analytics"], "url_for_calls": ["orders", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "orders", "new_order", "workflow", "workflow", "workflow"], "extends": ["base.html"], "includes": [], "line_count": 1791}, "templates\\import_data.html": {"href_links": [], "url_for_calls": ["import_data"], "extends": ["base.html"], "includes": [], "line_count": 157}, "templates\\login.html": {"href_links": ["https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css", "https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"], "url_for_calls": ["login"], "extends": [], "includes": [], "line_count": 363}, "templates\\settings.html": {"href_links": ["{{ url_for("], "url_for_calls": ["settings", "backup_database"], "extends": ["base.html"], "includes": [], "line_count": 121}, "templates\\test_notifications.html": {"href_links": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css", "/notifications", "/dashboard", "/notifications"], "url_for_calls": [], "extends": [], "includes": [], "line_count": 169}, "templates\\track_order.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 351}, "templates\\admin\\bulk_operations.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "#"], "url_for_calls": ["dashboard", "export_orders_excel", "export_inventory_excel"], "extends": ["base.html"], "includes": [], "line_count": 78}, "templates\\admin\\data_export.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["dashboard", "export_orders_excel", "export_inventory_excel"], "extends": ["base.html"], "includes": [], "line_count": 89}, "templates\\admin\\feedback.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 47}, "templates\\admin\\rider_tracking.html": {"href_links": ["{{ url_for(", "{{ url_for(", "tel:${rider.phone_number}", "/admin/api_keys"], "url_for_calls": ["warehouses", "dashboard"], "extends": ["base.html"], "includes": [], "line_count": 982}, "templates\\admin\\settings.html": {"href_links": ["{{ url_for(", "#", "{{ url_for("], "url_for_calls": ["dashboard", "update_settings", "backup_database"], "extends": ["base.html"], "includes": [], "line_count": 240}, "templates\\admin\\system_health.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dashboard"], "extends": ["base.html"], "includes": [], "line_count": 146}, "templates\\advanced_payment\\automated_matching.html": {"href_links": ["{{ url_for("], "url_for_calls": ["advanced_payment.dashboard"], "extends": ["base.html"], "includes": [], "line_count": 116}, "templates\\advanced_payment\\bulk_processing.html": {"href_links": ["{{ url_for("], "url_for_calls": ["advanced_payment.dashboard", "advanced_payment.process_bulk_payment"], "extends": ["base.html"], "includes": [], "line_count": 91}, "templates\\advanced_payment\\dashboard.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dashboard"], "extends": ["base.html"], "includes": [], "line_count": 34}, "templates\\auth\\login.html": {"href_links": ["https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.1/css/all.min.css", "#"], "url_for_calls": ["login"], "extends": [], "includes": [], "line_count": 204}, "templates\\auth\\profile.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dashboard"], "extends": ["base.html"], "includes": [], "line_count": 172}, "templates\\components\\intelligent_search.html": {"href_links": ["#", "#", "#", "#", "#", "${result.url}"], "url_for_calls": [], "extends": [], "includes": [], "line_count": 509}, "templates\\customers\\add_customer.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "add_customer", "customers"], "extends": ["base.html"], "includes": [], "line_count": 343}, "templates\\customers\\all_orders.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "customers", "customers", "customers", "customers", "customers", "customers"], "extends": ["base.html"], "includes": [], "line_count": 283}, "templates\\customers\\by_type.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "customers", "customers", "customers", "customers", "customers", "customers", "customers", "customers"], "extends": ["base.html"], "includes": [], "line_count": 165}, "templates\\customers\\customers_list.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "tel:{{ customer.phone }}", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "customers", "customers", "customers", "customers", "customers", "customers", "customers", "customers"], "extends": ["base.html"], "includes": [], "line_count": 463}, "templates\\customers\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "customers", "customers", "customers", "customers", "customers", "export_customers", "add_customer", "customers", "customers", "customers"], "extends": ["base.html"], "includes": [], "line_count": 178}, "templates\\customers\\orders.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "customers", "customers", "customers", "customers", "view_order_history", "view_invoice", "view_challan", "customers"], "extends": ["base.html"], "includes": [], "line_count": 134}, "templates\\customers\\pricing.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "customers", "customers", "customers", "customers", "customers"], "extends": ["base.html"], "includes": [], "line_count": 216}, "templates\\customers\\select_customer.html": {"href_links": ["{{ url_for("], "url_for_calls": ["customers", "customers"], "extends": ["base.html"], "includes": [], "line_count": 51}, "templates\\customers\\set_pricing.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["customers", "customers", "customers", "customers", "customers", "customers"], "extends": ["base.html"], "includes": [], "line_count": 224}, "templates\\dashboard\\ceo.html": {"href_links": ["{{ url_for(", "{{ url_for(", "#", "#", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#executive-dashboard", "#sales-analytics", "#division-analytics", "#inventory-analytics", "#financial-analytics", "#operational-analytics"], "url_for_calls": ["orders", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "workflow", "orders"], "extends": ["base.html"], "includes": [], "line_count": 1904}, "templates\\delivery_analytics\\comprehensive_reports.html": {"href_links": ["{{ url_for("], "url_for_calls": ["delivery_analytics.dashboard"], "extends": ["base.html"], "includes": [], "line_count": 40}, "templates\\delivery_analytics\\dashboard.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dashboard"], "extends": ["base.html"], "includes": [], "line_count": 34}, "templates\\delivery_analytics\\performance_kpis.html": {"href_links": ["{{ url_for("], "url_for_calls": ["delivery_analytics.dashboard"], "extends": ["base.html"], "includes": [], "line_count": 138}, "templates\\delivery_analytics\\real_time_tracking.html": {"href_links": ["{{ url_for("], "url_for_calls": ["delivery_analytics.dashboard"], "extends": ["base.html"], "includes": [], "line_count": 76}, "templates\\delivery_challans\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders", "view_order_history", "view_challan"], "extends": ["base.html"], "includes": [], "line_count": 241}, "templates\\divisions\\analytics.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["divisions.index", "divisions.export", "divisions.index"], "extends": ["base.html"], "includes": [], "line_count": 435}, "templates\\divisions\\create.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["divisions.index", "divisions.index", "divisions.create", "divisions.index"], "extends": ["base.html"], "includes": [], "line_count": 351}, "templates\\divisions\\edit.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["divisions.view", "divisions.index", "divisions.view", "divisions.edit", "divisions.view", "divisions.delete", "divisions.index"], "extends": ["base.html"], "includes": [], "line_count": 423}, "templates\\divisions\\index.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["divisions.analytics", "divisions.export"], "extends": ["base.html"], "includes": [], "line_count": 385}, "templates\\divisions\\modern_index.html": {"href_links": ["{{ url_for(", "#", "#", "#", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#", "#", "#"], "url_for_calls": ["divisions.analytics", "divisions.view", "divisions.edit", "divisions.create", "divisions.delete", "divisions.api_list", "divisions.view", "divisions.edit", "divisions.export"], "extends": ["base.html"], "includes": [], "line_count": 682}, "templates\\divisions\\view.html": {"href_links": ["{{ url_for(", "{{ url_for(", "mailto:{{ division.contact_email }}", "tel:{{ division.contact_phone }}", "{{ url_for(", "#", "#", "#"], "url_for_calls": ["divisions.edit", "divisions.index", "divisions.edit", "divisions.export", "divisions.analytics", "divisions.delete", "divisions.index"], "extends": ["base.html"], "includes": [], "line_count": 425}, "templates\\feedback\\index.html": {"href_links": [], "url_for_calls": ["feedback", "feedback"], "extends": ["base.html"], "includes": [], "line_count": 126}, "templates\\finance\\accounts_payable.html": {"href_links": ["/finance"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 604}, "templates\\finance\\accounts_receivable.html": {"href_links": ["/finance"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 592}, "templates\\finance\\advanced_analytics.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 750}, "templates\\finance\\aging_analysis.html": {"href_links": ["{{ url_for("], "url_for_calls": ["finance_aging_analysis"], "extends": ["base.html"], "includes": [], "line_count": 523}, "templates\\finance\\comprehensive_reports.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 780}, "templates\\finance\\customer_ledger.html": {"href_links": ["{{ url_for("], "url_for_calls": ["finance_dashboard"], "extends": ["base.html"], "includes": [], "line_count": 380}, "templates\\finance\\customer_ledger_enhanced.html": {"href_links": ["{{ url_for("], "url_for_calls": ["finance_customer_ledger"], "extends": ["base.html"], "includes": [], "line_count": 733}, "templates\\finance\\duplicate_detection.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 474}, "templates\\finance\\financial_reports.html": {"href_links": ["/finance/payment/${paymentId}/attachments"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 932}, "templates\\finance\\invoice_generation_enhanced.html": {"href_links": ["{{ url_for("], "url_for_calls": ["finance_pending_invoices"], "extends": ["base.html"], "includes": [], "line_count": 716}, "templates\\finance\\invoice_generation_new.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 838}, "templates\\finance\\modern_dashboard.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#", "#", "#", "{{ url_for(", "{{ url_for("], "url_for_calls": ["finance_pending_invoices_management", "finance_customer_ledger", "finance_payment_collection", "financial_reports", "accounts_receivable", "accounts_payable", "duplicate_order_detection", "comprehensive_finance_reports", "advanced_financial_analytics", "finance_api_stats"], "extends": ["base.html"], "includes": [], "line_count": 472}, "templates\\finance\\payment_attachments.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["finance_payment_collection", "add_payment_attachment", "serve_uploaded_file", "serve_uploaded_file", "serve_uploaded_file", "remove_payment_attachment"], "extends": ["base.html"], "includes": [], "line_count": 357}, "templates\\finance\\payment_collection.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["finance_dashboard", "finance_dashboard", "finance_process_payment"], "extends": ["base.html"], "includes": [], "line_count": 317}, "templates\\finance\\payment_collection_enhanced.html": {"href_links": [], "url_for_calls": ["finance_process_payment"], "extends": ["base.html"], "includes": [], "line_count": 903}, "templates\\finance\\payment_collection_new.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 865}, "templates\\finance\\pending_invoices.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["finance_dashboard", "finance_dashboard", "finance_process_payment"], "extends": ["base.html"], "includes": [], "line_count": 310}, "templates\\inventory\\add.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["inventory", "inventory_add", "inventory", "products", "warehouse", "inventory"], "extends": ["base.html"], "includes": [], "line_count": 248}, "templates\\inventory\\history.html": {"href_links": ["{{ url_for("], "url_for_calls": ["inventory"], "extends": ["base.html"], "includes": [], "line_count": 157}, "templates\\inventory\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["inventory", "export_inventory_excel", "new_stock", "transfer_stock", "toggle_inventory_status", "inventory_history", "new_stock", "transfer_stock"], "extends": ["base.html"], "includes": [], "line_count": 160}, "templates\\inventory\\inventory_view.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#", "#"], "url_for_calls": ["add_inventory", "inventory_transfer", "products", "inventory", "inventory", "inventory", "inventory", "inventory", "inventory", "inventory", "inventory", "edit_inventory", "inventory_transfer", "inventory_history", "products"], "extends": ["base.html"], "includes": [], "line_count": 341}, "templates\\inventory\\new.html": {"href_links": ["{{ url_for("], "url_for_calls": ["new_stock", "inventory"], "extends": ["base.html"], "includes": [], "line_count": 88}, "templates\\inventory\\transfer.html": {"href_links": ["{{ url_for("], "url_for_calls": ["transfer_stock", "inventory"], "extends": ["base.html"], "includes": [], "line_count": 123}, "templates\\notifications\\index.html": {"href_links": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css", "/", "/dashboard", "/orders", "/products", "/notifications", "/logout", "#", "#", "#", "#", "#", "#", "{{ notification.action_url }}", "${notification.action_url}"], "url_for_calls": [], "extends": [], "includes": [], "line_count": 2013}, "templates\\notifications\\notification_center.html": {"href_links": ["{{ notification.data.action_url }}"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 769}, "templates\\notifications\\test_notifications.html": {"href_links": ["/notifications"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 492}, "templates\\orders\\allocate_inventory.html": {"href_links": ["{{ url_for("], "url_for_calls": ["auto_allocate_inventory", "release_allocation", "release_allocation", "manual_allocate_inventory", "orders"], "extends": ["base.html"], "includes": [], "line_count": 261}, "templates\\orders\\approval.html": {"href_links": ["{{ url_for("], "url_for_calls": ["orders.index"], "extends": ["base.html"], "includes": [], "line_count": 221}, "templates\\orders\\challan.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["display_challan", "workflow"], "extends": ["base.html"], "includes": [], "line_count": 448}, "templates\\orders\\deliver.html": {"href_links": ["{{ url_for("], "url_for_calls": ["deliver_order", "workflow"], "extends": ["base.html"], "includes": [], "line_count": 137}, "templates\\orders\\dispatch.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dispatch_order", "workflow"], "extends": ["base.html"], "includes": [], "line_count": 131}, "templates\\orders\\duplicate_detection.html": {"href_links": ["/orders"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 509}, "templates\\orders\\enhanced_search.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders_enhanced.advanced_search", "orders.view_order", "orders_enhanced.update_order", "orders_enhanced.order_history", "orders_enhanced.advanced_search", "orders_enhanced.advanced_search", "orders_enhanced.advanced_search", "orders_enhanced.advanced_search"], "extends": ["base.html"], "includes": [], "line_count": 300}, "templates\\orders\\enhanced_update.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders.view_order", "orders.view_order", "orders_enhanced.order_history"], "extends": ["base.html"], "includes": [], "line_count": 253}, "templates\\orders\\generate_challan.html": {"href_links": ["javascript:history.back()", "{{ url_for("], "url_for_calls": ["workflow"], "extends": ["base.html"], "includes": [], "line_count": 220}, "templates\\orders\\generate_invoice.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["finance_invoices", "finance_pending_invoices"], "extends": ["base.html"], "includes": [], "line_count": 259}, "templates\\orders\\history.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["orders", "view_challan"], "extends": ["base.html"], "includes": [], "line_count": 242}, "templates\\orders\\index.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["orders.new_order", "new_order"], "extends": ["base.html"], "includes": [], "line_count": 244}, "templates\\orders\\invoice.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["display_invoice", "workflow"], "extends": ["base.html"], "includes": [], "line_count": 594}, "templates\\orders\\new.html": {"href_links": ["{{ url_for("], "url_for_calls": ["orders.new_order", "orders.index"], "extends": ["base.html"], "includes": [], "line_count": 544}, "templates\\orders\\new_order.html": {"href_links": ["{{ url_for("], "url_for_calls": ["create_order_with_workflow", "orders"], "extends": ["base.html"], "includes": [], "line_count": 513}, "templates\\orders\\orders_list.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["new_order", "workflow", "orders", "orders", "orders", "orders", "orders", "orders", "orders", "orders", "view_order", "update_order", "allocate_inventory_page", "generate_challan", "view_invoice", "view_order_history"], "extends": ["base.html"], "includes": [], "line_count": 264}, "templates\\orders\\order_details.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders", "update_order", "allocate_inventory_page", "generate_challan", "view_invoice"], "extends": ["base.html"], "includes": [], "line_count": 267}, "templates\\orders\\order_history.html": {"href_links": ["/orders", "/orders/${activity.order_id}/view"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 370}, "templates\\orders\\search_results.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders", "orders", "orders", "view_order", "update_order", "view_invoice", "orders"], "extends": ["base.html"], "includes": [], "line_count": 211}, "templates\\orders\\update.html": {"href_links": ["{{ url_for("], "url_for_calls": ["update_order", "orders"], "extends": ["base.html"], "includes": [], "line_count": 326}, "templates\\orders\\view.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders.index", "orders.approve_order", "finance_generate_invoice", "generate_challan", "orders.update_order", "finance.view_invoice", "orders.index"], "extends": ["base.html"], "includes": [], "line_count": 234}, "templates\\orders\\workflow.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["workflow", "workflow", "orders", "view_order_history", "approve_order", "generate_challan", "view_challan", "display_challan", "finance_generate_invoice", "view_invoice", "display_invoice", "dispatch_order", "deliver_order", "update_order"], "extends": ["base.html"], "includes": [], "line_count": 189}, "templates\\orders\\workflow_status.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["approve_order_manual", "reject_order_manual", "orders", "view_order", "allocate_inventory_page", "generate_challan"], "extends": ["base.html"], "includes": [], "line_count": 319}, "templates\\organization\\by_division.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["organization", "organization", "organization", "organization", "organization"], "extends": ["base.html"], "includes": [], "line_count": 345}, "templates\\organization\\chart.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["organization", "organization", "organization", "organization"], "extends": ["base.html"], "includes": [], "line_count": 241}, "templates\\organization\\divisions.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["organization", "organization", "organization", "organization", "organization", "organization"], "extends": ["base.html"], "includes": [], "line_count": 178}, "templates\\organization\\hierarchy.html": {"href_links": ["{{ url_for("], "url_for_calls": ["organization"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\organization\\index.html": {"href_links": ["#"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 55}, "templates\\organization\\members.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["organization", "organization", "organization", "organization"], "extends": ["base.html"], "includes": [], "line_count": 320}, "templates\\organization\\member_details.html": {"href_links": ["{{ url_for("], "url_for_calls": ["organization"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\organization\\performance.html": {"href_links": ["{{ url_for("], "url_for_calls": ["organization"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\organization\\team_by_division.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["organization", "organization", "organization", "organization", "organization", "organization"], "extends": ["base.html"], "includes": [], "line_count": 351}, "templates\\products\\add_product_enhanced.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["view_all_products", "products", "view_all_products"], "extends": ["base.html"], "includes": [], "line_count": 350}, "templates\\products\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["new_product", "inventory", "products", "products", "products", "products", "products", "products", "products", "products", "products", "update_product", "new_product"], "extends": ["base.html"], "includes": [], "line_count": 272}, "templates\\products\\new.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["products", "new_product", "products"], "extends": ["base.html"], "includes": [], "line_count": 409}, "templates\\products\\products_list.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#", "#"], "url_for_calls": ["new_product", "inventory", "products", "products", "products", "products", "products", "products", "products", "products", "products", "products", "update_product", "inventory", "inventory_add", "reports"], "extends": ["base.html"], "includes": [], "line_count": 322}, "templates\\products\\product_details.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["view_order", "products", "update_product", "inventory_add", "inventory", "reports"], "extends": ["base.html"], "includes": [], "line_count": 342}, "templates\\products\\product_management.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["new_product", "inventory", "product_management", "product_management", "product_management", "product_management", "product_management", "product_management", "product_management", "product_management", "product_management", "view_product", "update_product", "new_product", "product_management", "product_management", "product_management"], "extends": ["base.html"], "includes": [], "line_count": 408}, "templates\\products\\update.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["products", "view_product", "update_product", "update_product_selection", "view_product"], "extends": ["base.html"], "includes": [], "line_count": 393}, "templates\\products\\update_selection.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["update_product", "update_product_selection", "update_product_selection", "update_product_selection", "update_product_selection"], "extends": ["base.html"], "includes": [], "line_count": 336}, "templates\\products\\view_all_products.html": {"href_links": ["{{ url_for(", "#", "#", "#", "#", "#", "#", "#"], "url_for_calls": ["view_all_products", "view_all_products", "view_all_products"], "extends": ["base.html"], "includes": [], "line_count": 494}, "templates\\reports\\agent_performance_ranking.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 172}, "templates\\reports\\customer_purchase_history.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["reports", "customer_purchase_history", "view_order", "generate_invoice"], "extends": ["base.html"], "includes": [], "line_count": 333}, "templates\\reports\\custom_date_range.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "custom_date_range_report"], "extends": ["base.html"], "includes": [], "line_count": 196}, "templates\\reports\\daily_sales.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 80}, "templates\\reports\\delivery_performance.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\reports\\division_performance.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "division_performance_chart"], "extends": ["base.html"], "includes": [], "line_count": 301}, "templates\\reports\\division_revenue_comparison.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 250}, "templates\\reports\\expiry.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "expiry_report"], "extends": ["base.html"], "includes": [], "line_count": 162}, "templates\\reports\\expiry_report.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\reports\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["low_stock_report", "expiry_report", "stock_movement_report", "inventory_report", "daily_sales_report_new", "weekly_sales_report", "monthly_sales_report_new", "custom_date_range_report", "sales_by_agent_report", "product_performance_report", "customer_purchase_history", "delivery_performance_report", "financial_summary_report", "sales_team_performance", "warehouse_inventory_report", "batch_tracking_report", "warehouse_performance_report", "sales_analytics", "inventory_analytics", "division_performance_chart", "trend_analysis"], "extends": ["base.html"], "includes": [], "line_count": 169}, "templates\\reports\\inventory_analytics.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "inventory_analytics"], "extends": ["base.html"], "includes": [], "line_count": 251}, "templates\\reports\\low_stock.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["reports", "low_stock_report", "new_stock", "new_stock"], "extends": ["base.html"], "includes": [], "line_count": 169}, "templates\\reports\\low_stock_alert.html": {"href_links": ["/products/{{ product.product_id }}", "/inventory/add?product_id={{ product.product_id }}"], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 174}, "templates\\reports\\monthly_sales.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 127}, "templates\\reports\\monthly_summary.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 237}, "templates\\reports\\product_inventory.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["reports", "product_inventory_report"], "extends": ["base.html"], "includes": [], "line_count": 284}, "templates\\reports\\product_performance.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "product_performance_report"], "extends": ["base.html"], "includes": [], "line_count": 283}, "templates\\reports\\reports_base.html": {"href_links": [], "url_for_calls": ["static"], "extends": ["base.html"], "includes": [], "line_count": 71}, "templates\\reports\\reports_dashboard.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["daily_sales_report_new", "weekly_sales_report_new", "monthly_sales_report_new", "inventory_report", "product_performance_report", "sales_by_agent_report", "daily_sales_report_new", "weekly_sales_report_new", "monthly_sales_report_new", "custom_date_range_report", "inventory_report", "inventory", "reports", "inventory", "financial_reports", "finance_pending_invoices", "finance_customer_ledger", "finance_payment_collection", "sales_by_agent_report", "customers", "customers", "product_performance_report"], "extends": ["base.html"], "includes": [], "line_count": 467}, "templates\\reports\\sales_analytics.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "sales_analytics"], "extends": ["base.html"], "includes": [], "line_count": 382}, "templates\\reports\\sales_by_agent.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "sales_by_agent_report"], "extends": ["base.html"], "includes": [], "line_count": 186}, "templates\\reports\\sales_team_performance.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\reports\\stock_movements.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "stock_movement_report"], "extends": ["base.html"], "includes": [], "line_count": 291}, "templates\\reports\\top_customers_revenue.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 160}, "templates\\reports\\trend_analysis.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\reports\\warehouse_inventory.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["reports", "export_warehouse_inventory", "export_warehouse_inventory", "warehouse_inventory_report"], "extends": ["base.html"], "includes": [], "line_count": 151}, "templates\\reports\\warehouse_performance.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["reports", "export_warehouse_performance", "export_warehouse_performance", "warehouse_performance_report"], "extends": ["base.html"], "includes": [], "line_count": 181}, "templates\\reports\\warehouse_utilization.html": {"href_links": ["{{ url_for("], "url_for_calls": ["warehouse_reports"], "extends": ["base.html"], "includes": [], "line_count": 197}, "templates\\reports\\weekly_sales.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports"], "extends": ["base.html"], "includes": [], "line_count": 36}, "templates\\reports\\advanced\\customer_history.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "customer_purchase_history"], "extends": ["base.html"], "includes": [], "line_count": 220}, "templates\\reports\\advanced\\delivery-performance.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "delivery_performance_report"], "extends": ["base.html"], "includes": [], "line_count": 200}, "templates\\reports\\advanced\\delivery_performance.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "delivery_performance_report"], "extends": ["base.html"], "includes": [], "line_count": 200}, "templates\\reports\\advanced\\financial_summary.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "financial_summary_report", "financial_summary_report", "financial_summary_report", "financial_summary_report"], "extends": ["base.html"], "includes": [], "line_count": 401}, "templates\\reports\\advanced\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["reports", "financial_summary_report", "sales_team_performance", "delivery_performance_report"], "extends": ["base.html"], "includes": [], "line_count": 64}, "templates\\reports\\advanced\\product_performance.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["static", "static", "export_product_performance_pdf", "reports", "product_performance_report", "product_performance_report", "product_performance_report"], "extends": ["reports/reports_base.html"], "includes": [], "line_count": 471}, "templates\\reports\\advanced\\sales_team.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["static", "static", "static", "static", "export_sales_team_pdf", "reports", "sales_team_performance", "sales_team_performance", "sales_team_performance"], "extends": ["reports/reports_base.html"], "includes": [], "line_count": 2566}, "templates\\reports\\sales\\by_agent.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "sales_by_agent_report"], "extends": ["base.html"], "includes": [], "line_count": 271}, "templates\\reports\\sales\\custom.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "custom_date_range_report"], "extends": ["base.html"], "includes": [], "line_count": 219}, "templates\\reports\\sales\\daily.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "daily_sales_report_new"], "extends": ["base.html"], "includes": [], "line_count": 214}, "templates\\reports\\sales\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["reports_dashboard", "daily_sales_report", "weekly_sales_report", "monthly_sales_report", "sales_by_agent_report", "product_performance_report", "top_customers_revenue_report"], "extends": ["base.html"], "includes": [], "line_count": 56}, "templates\\reports\\sales\\monthly.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "monthly_sales_report_new"], "extends": ["base.html"], "includes": [], "line_count": 265}, "templates\\reports\\sales\\weekly.html": {"href_links": ["{{ url_for("], "url_for_calls": ["reports", "weekly_sales_report"], "extends": ["base.html"], "includes": [], "line_count": 212}, "templates\\rider\\professional_dashboard.html": {"href_links": ["{{ order.google_maps_route }}", "{{ url_for(", "tel:************", "{{ url_for(", "/admin/api_keys"], "url_for_calls": ["riders_orders", "dashboard"], "extends": ["base.html"], "includes": [], "line_count": 600}, "templates\\riders\\dashboard.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["riders_orders", "rider_delivery_routes", "rider_performance"], "extends": ["base.html"], "includes": [], "line_count": 723}, "templates\\riders\\delivery_routes.html": {"href_links": ["{{ url_for("], "url_for_calls": ["riders"], "extends": ["base.html"], "includes": [], "line_count": 490}, "templates\\riders\\edit.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["view_rider", "edit_rider", "view_rider", "view_rider", "riders"], "extends": ["base.html"], "includes": [], "line_count": 304}, "templates\\riders\\index.html": {"href_links": ["{{ url_for("], "url_for_calls": ["register_rider"], "extends": ["base.html"], "includes": [], "line_count": 590}, "templates\\riders\\modern_dashboard.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["riders.tracking", "riders.tracking", "riders.analytics"], "extends": ["base.html"], "includes": [], "line_count": 313}, "templates\\riders\\modern_tracking.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 416}, "templates\\riders\\orders.html": {"href_links": ["{{ url_for(", "tel:{{ order.phone_number }}", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["riders_orders", "riders_orders", "update_delivery_status", "riders_orders", "riders_dashboard", "workflow", "claim_pickup"], "extends": ["base.html"], "includes": [], "line_count": 437}, "templates\\riders\\performance.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 386}, "templates\\riders\\register.html": {"href_links": [], "url_for_calls": ["register_rider"], "extends": ["base.html"], "includes": [], "line_count": 418}, "templates\\riders\\register_simple.html": {"href_links": ["{{ url_for("], "url_for_calls": ["riders", "register_rider"], "extends": ["base.html"], "includes": [], "line_count": 387}, "templates\\riders\\view.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["riders", "edit_rider", "edit_rider"], "extends": ["base.html"], "includes": [], "line_count": 324}, "templates\\sales_analytics\\dashboard.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dashboard"], "extends": ["base.html"], "includes": [], "line_count": 34}, "templates\\sales_team\\dashboard.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["sales_division_detail", "orders", "sales_by_agent_report", "sales_division_detail", "orders", "orders", "reports", "orders"], "extends": ["base.html"], "includes": [], "line_count": 368}, "templates\\sales_team\\division_detail.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["dashboard", "sales_team_dashboard", "orders", "sales_team_dashboard", "reports"], "extends": ["base.html"], "includes": [], "line_count": 467}, "templates\\search\\advanced.html": {"href_links": [], "url_for_calls": [], "extends": ["base.html"], "includes": [], "line_count": 58}, "templates\\search\\universal_search.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "#"], "url_for_calls": ["universal_search", "universal_search", "customer_statement", "products", "view_order"], "extends": ["base.html"], "includes": [], "line_count": 479}, "templates\\tracking\\track_form.html": {"href_links": ["/track/${result.tracking_number}", "/track/${result.tracking_number}"], "url_for_calls": ["tracking.track_post"], "extends": ["base.html"], "includes": [], "line_count": 579}, "templates\\tracking\\track_result.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["tracking.track_form", "tracking.track_form"], "extends": ["base.html"], "includes": [], "line_count": 336}, "templates\\users\\add.html": {"href_links": ["{{ url_for("], "url_for_calls": ["users.add", "users.manage"], "extends": ["base.html"], "includes": [], "line_count": 101}, "templates\\users\\edit.html": {"href_links": ["{{ url_for("], "url_for_calls": ["users.edit", "users.manage"], "extends": ["base.html"], "includes": [], "line_count": 88}, "templates\\users\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["users.add", "users.reset_password", "users.logs", "users.add", "users.reset_password", "users.reset_password"], "extends": ["base.html"], "includes": [], "line_count": 247}, "templates\\users\\logs.html": {"href_links": ["{{ url_for(", "#", "#", "#", "#", "#", "#", "#"], "url_for_calls": ["users.manage"], "extends": ["base.html"], "includes": [], "line_count": 762}, "templates\\users\\manage.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["users.add", "users.roles", "users.logs", "users.edit", "users.toggle_status", "users.toggle_status", "users.delete", "users.reset_password"], "extends": ["base.html"], "includes": [], "line_count": 270}, "templates\\users\\new.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["users_index", "users_new", "users_index"], "extends": ["base.html"], "includes": [], "line_count": 202}, "templates\\users\\permissions.html": {"href_links": ["{{ url_for(", "#permissions", "#templates", "#visualization", "#widgets", "#audit", "{{ url_for("], "url_for_calls": ["users.roles", "users.permissions", "users.roles"], "extends": ["base.html"], "includes": [], "line_count": 711}, "templates\\users\\profile.html": {"href_links": ["#edit", "#password", "#activity"], "url_for_calls": ["users", "users"], "extends": ["base.html"], "includes": [], "line_count": 177}, "templates\\users\\reset_password.html": {"href_links": ["{{ url_for("], "url_for_calls": ["users.reset_password", "users.manage"], "extends": ["base.html"], "includes": [], "line_count": 98}, "templates\\users\\roles.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["users.permissions", "users.manage", "users.sync_permissions"], "extends": ["base.html"], "includes": [], "line_count": 248}, "templates\\warehouse\\dc_generate.html": {"href_links": ["{{ url_for("], "url_for_calls": ["dc_pending"], "extends": ["base.html"], "includes": [], "line_count": 303}, "templates\\warehouse\\dc_pending.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders.view_order", "dc_generate", "orders.index", "orders.index", "warehouses", "workflow_management", "reports"], "extends": ["base.html"], "includes": [], "line_count": 215}, "templates\\warehouse\\edit.html": {"href_links": ["{{ url_for(", "{{ url_for("], "url_for_calls": ["warehouse", "edit_warehouse", "warehouse"], "extends": ["base.html"], "includes": [], "line_count": 118}, "templates\\warehouse\\index.html": {"href_links": ["#", "{{ url_for(", "{{ url_for(", "#", "#"], "url_for_calls": ["inventory", "inventory", "warehouse"], "extends": ["base.html"], "includes": [], "line_count": 366}, "templates\\warehouse\\orders.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders", "admin_rider_tracking", "dashboard"], "extends": ["base.html"], "includes": [], "line_count": 284}, "templates\\warehouse\\reports.html": {"href_links": ["{{ url_for(", "#", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["warehouses", "warehouse_utilization_report", "low_stock_alert_report", "warehouse_performance_report", "monthly_summary_report"], "extends": ["base.html"], "includes": [], "line_count": 397}, "templates\\warehouse\\warehouses.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["inventory", "delivery_challans", "inventory", "delivery_challans"], "extends": ["base.html"], "includes": [], "line_count": 448}, "templates\\warehouses\\index.html": {"href_links": ["{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["dashboard", "warehouse_generate_dc", "view_challan", "view_order_history", "inventory", "workflow", "reports", "delivery_challans"], "extends": ["base.html"], "includes": [], "line_count": 266}, "templates\\workflow\\management.html": {"href_links": ["#{{ status.lower().replace(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for(", "{{ url_for("], "url_for_calls": ["orders.view_order", "order_approval", "dc_generate", "orders.new_order", "orders.index", "dc_pending", "warehouses", "reports", "dashboard"], "extends": ["base.html"], "includes": [], "line_count": 205}}, "navigation_links": {"/finance/dashboard": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}, "/riders/dashboard": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}, "/finance/comprehensive-reports": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}, "/delivery_analytics/": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}, "/advanced_payment/": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}, "/sales_analytics/": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}, "/admin/bulk_operations": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}, "/admin/system_health": {"status_code": 200, "status": "AUTH_REQUIRED", "accessible": true}}, "database_issues": {"error": "foreign key mismatch - \"division_permissions\" referencing \"users\""}, "broken_routes": [], "missing_templates": [], "href_issues": [], "string_formatting_errors": [{"file": "app.py", "line": 62, "content": "def safe_strftime(date_obj, format_str='%d-%m-%Y %H:%M:%S'):"}, {"file": "app.py", "line": 158, "content": "format='%(asctime)s %(levelname)s %(name)s %(message)s',"}, {"file": "app.py", "line": 408, "content": "def format_datetime(value, format_str='%Y-%m-%d:%H:%M'):"}, {"file": "app.py", "line": 13457, "content": "date_format = '%Y-%m-%d'"}, {"file": "app.py", "line": 13460, "content": "date_format = '%Y-W%W'"}, {"file": "app.py", "line": 13463, "content": "date_format = '%Y-%m'"}, {"file": "app.py", "line": 13466, "content": "date_format = '%Y-Q%Q'"}, {"file": "app.py", "line": 15642, "content": "dt = datetime.strptime(str(date_str)[:19], '%Y-%m-%d %H:%M:%S')"}, {"file": "routes/divisions_modern.py", "line": 229, "content": "div_dict['created_at_formatted'] = datetime.fromisoformat(str(div_dict['created_at'])).strftime('%Y-%m-%d %H:%M')"}]}}