{% extends 'base.html' %}

{% block title %}Sales Team Performance - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-users text-primary"></i> Sales Team Performance
        </h1>
        <a href="{{ url_for('sales_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <!-- Team Performance Metrics -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Sales Agents</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ team_metrics.total_agents if team_metrics else 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ team_metrics.total_orders if team_metrics else 0 }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Total Sales</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs. {{ "{:,.0f}"|format(team_metrics.total_sales if team_metrics else 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Avg Order Value</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                Rs. {{ "{:,.0f}"|format(team_metrics.avg_order_value if team_metrics else 0) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Weekly Performance -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Weekly Team Performance</h6>
        </div>
        <div class="card-body">
            {% if weekly_performance %}
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Sales Agent</th>
                            <th>Weekly Orders</th>
                            <th>Weekly Sales</th>
                            <th>Avg Order Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for agent in weekly_performance %}
                        <tr>
                            <td>{{ agent.sales_agent }}</td>
                            <td>{{ agent.weekly_orders }}</td>
                            <td>Rs. {{ "{:,.0f}"|format(agent.weekly_sales) }}</td>
                            <td>Rs. {{ "{:,.0f}"|format(agent.avg_order_value) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No weekly performance data available.
            </div>
            {% endif %}
        </div>
    </div>

    <!-- Monthly Performance -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-success">Monthly Team Performance</h6>
        </div>
        <div class="card-body">
            {% if monthly_performance %}
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Sales Agent</th>
                            <th>Monthly Orders</th>
                            <th>Monthly Sales</th>
                            <th>Avg Order Value</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for agent in monthly_performance %}
                        <tr>
                            <td>{{ agent.sales_agent }}</td>
                            <td>{{ agent.monthly_orders }}</td>
                            <td>Rs. {{ "{:,.0f}"|format(agent.monthly_sales) }}</td>
                            <td>Rs. {{ "{:,.0f}"|format(agent.avg_order_value) }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> No monthly performance data available.
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}