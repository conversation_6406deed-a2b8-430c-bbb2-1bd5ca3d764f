#!/usr/bin/env python3
"""
Investigate Missing Route Visibility
Check why new analytics and payment routes are not visible/active in browser navigation
"""

import os
import re
import requests
from datetime import datetime

def test_new_analytics_routes():
    """Test all new analytics and payment routes"""
    
    print("🔍 TESTING NEW ANALYTICS AND PAYMENT ROUTES")
    print("=" * 50)
    
    new_routes = [
        '/delivery_analytics/',
        '/delivery_analytics/real_time_tracking',
        '/delivery_analytics/performance_kpis',
        '/delivery_analytics/comprehensive_reports',
        '/advanced_payment/',
        '/advanced_payment/bulk_processing',
        '/advanced_payment/automated_matching',
        '/advanced_payment/reconciliation',
        '/advanced_payment/analytics',
        '/sales_analytics/',
        '/sales_analytics/salesperson_ledger',
        '/sales_analytics/team_performance',
        '/sales_analytics/division_ledger',
        '/sales_analytics/division_analysis'
    ]
    
    working_routes = []
    broken_routes = []
    
    for route in new_routes:
        try:
            response = requests.get(f"http://127.0.0.1:3000{route}", timeout=10)
            
            print(f"🔍 Testing {route}: {response.status_code}")
            
            if response.status_code == 200:
                if 'login' in response.text.lower():
                    print(f"   🔐 Requires authentication (working)")
                    working_routes.append(route)
                else:
                    print(f"   ✅ Accessible")
                    working_routes.append(route)
            elif response.status_code == 302:
                print(f"   🔄 Redirected (likely auth required)")
                working_routes.append(route)
            elif response.status_code == 404:
                print(f"   ❌ Not found")
                broken_routes.append(route)
            else:
                print(f"   ⚠️ Status: {response.status_code}")
                broken_routes.append(route)
        
        except Exception as e:
            print(f"   ❌ Error: {e}")
            broken_routes.append(route)
    
    print(f"\n📊 Route Testing Summary:")
    print(f"   Working routes: {len(working_routes)}")
    print(f"   Broken routes: {len(broken_routes)}")
    
    if broken_routes:
        print(f"\n❌ Broken routes:")
        for route in broken_routes:
            print(f"   • {route}")
    
    return working_routes, broken_routes

def check_navigation_templates():
    """Check if navigation templates include links to new routes"""
    
    print("\n🧭 CHECKING NAVIGATION TEMPLATES")
    print("=" * 50)
    
    navigation_templates = [
        'templates/base.html',
        'templates/navbar.html',
        'templates/sidebar.html',
        'templates/navigation.html',
        'templates/dashboard.html'
    ]
    
    navigation_links_found = {}
    
    for template_path in navigation_templates:
        if os.path.exists(template_path):
            print(f"\n📄 Analyzing: {template_path}")
            
            try:
                with open(template_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for links to new analytics routes
                analytics_links = []
                
                # Search for delivery analytics links
                if 'delivery_analytics' in content:
                    analytics_links.append('delivery_analytics')
                
                # Search for advanced payment links
                if 'advanced_payment' in content:
                    analytics_links.append('advanced_payment')
                
                # Search for sales analytics links
                if 'sales_analytics' in content:
                    analytics_links.append('sales_analytics')
                
                # Look for href patterns
                href_patterns = re.findall(r'href=["\']([^"\']*analytics[^"\']*)["\']', content)
                payment_patterns = re.findall(r'href=["\']([^"\']*payment[^"\']*)["\']', content)
                
                navigation_links_found[template_path] = {
                    'analytics_references': analytics_links,
                    'href_analytics': href_patterns,
                    'href_payments': payment_patterns,
                    'has_navigation': len(analytics_links) > 0 or len(href_patterns) > 0 or len(payment_patterns) > 0
                }
                
                print(f"   Analytics references: {analytics_links}")
                print(f"   Analytics hrefs: {href_patterns}")
                print(f"   Payment hrefs: {payment_patterns}")
                
            except Exception as e:
                print(f"   ❌ Error reading template: {e}")
        else:
            print(f"❌ Template not found: {template_path}")
    
    return navigation_links_found

def check_main_dashboard_links():
    """Check if main dashboard has links to new features"""
    
    print("\n🏠 CHECKING MAIN DASHBOARD LINKS")
    print("=" * 50)
    
    dashboard_template = 'templates/dashboard.html'
    
    if os.path.exists(dashboard_template):
        try:
            with open(dashboard_template, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Look for analytics and payment links
            analytics_links = re.findall(r'href=["\']([^"\']*analytics[^"\']*)["\']', content)
            payment_links = re.findall(r'href=["\']([^"\']*payment[^"\']*)["\']', content)
            delivery_links = re.findall(r'href=["\']([^"\']*delivery[^"\']*)["\']', content)
            
            print(f"📊 Dashboard link analysis:")
            print(f"   Analytics links: {len(analytics_links)}")
            print(f"   Payment links: {len(payment_links)}")
            print(f"   Delivery links: {len(delivery_links)}")
            
            if analytics_links:
                print(f"   Analytics links found:")
                for link in analytics_links:
                    print(f"     • {link}")
            
            if payment_links:
                print(f"   Payment links found:")
                for link in payment_links:
                    print(f"     • {link}")
            
            if delivery_links:
                print(f"   Delivery links found:")
                for link in delivery_links:
                    print(f"     • {link}")
            
            # Check if there are placeholder links that should be updated
            placeholder_links = re.findall(r'href="#"', content)
            print(f"   Placeholder links (#): {len(placeholder_links)}")
            
            return {
                'analytics_links': analytics_links,
                'payment_links': payment_links,
                'delivery_links': delivery_links,
                'placeholder_links': len(placeholder_links)
            }
        
        except Exception as e:
            print(f"❌ Error reading dashboard template: {e}")
            return None
    else:
        print(f"❌ Dashboard template not found: {dashboard_template}")
        return None

def add_navigation_links_to_dashboard():
    """Add navigation links to the main dashboard"""
    
    print("\n🔗 ADDING NAVIGATION LINKS TO DASHBOARD")
    print("=" * 50)
    
    dashboard_template = 'templates/dashboard.html'
    
    if not os.path.exists(dashboard_template):
        print(f"❌ Dashboard template not found")
        return False
    
    try:
        with open(dashboard_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if analytics links are already present
        if 'delivery_analytics' in content and 'advanced_payment' in content and 'sales_analytics' in content:
            print("✅ Analytics links already present in dashboard")
            return True
        
        # Find a good place to add analytics cards
        # Look for existing card sections
        card_section_pattern = r'<div class="row">[^<]*<div class="col[^>]*>[^<]*<div class="card'
        
        if re.search(card_section_pattern, content):
            print("✅ Found existing card section in dashboard")
            
            # Add analytics cards section
            analytics_cards = '''
    <!-- Analytics and Management Cards -->
    <div class="row mt-4">
        <div class="col-12">
            <h4 class="mb-3 text-gray-800">
                <i class="fas fa-chart-line text-primary"></i> Analytics & Management
            </h4>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Delivery Analytics
                            </div>
                            <div class="text-gray-900">Real-time tracking, KPIs, and reports</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-chart-line"></i> Open Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Payment Management
                            </div>
                            <div class="text-gray-900">Bulk processing, matching, analytics</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('advanced_payment.dashboard') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-dollar-sign"></i> Open Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Sales Analytics
                            </div>
                            <div class="text-gray-900">Team performance, division analysis</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('sales_analytics.dashboard') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-users"></i> Open Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
'''
            
            # Find the end of the content div and add before it
            end_content_pattern = r'</div>\s*{% endblock %}'
            if re.search(end_content_pattern, content):
                content = re.sub(end_content_pattern, analytics_cards + '\n</div>\n{% endblock %}', content)
                
                # Write back to file
                with open(dashboard_template, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ Added analytics cards to dashboard")
                return True
            else:
                print("⚠️ Could not find insertion point for analytics cards")
                return False
        else:
            print("⚠️ Could not find card section in dashboard")
            return False
    
    except Exception as e:
        print(f"❌ Error adding navigation links: {e}")
        return False

def test_dashboard_after_updates():
    """Test dashboard after adding navigation links"""
    
    print("\n🧪 TESTING DASHBOARD AFTER UPDATES")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:3000/dashboard", timeout=10)
        
        print(f"Dashboard status: {response.status_code}")
        
        if response.status_code == 200:
            # Check if new links are present in the response
            if 'delivery_analytics' in response.text:
                print("✅ Delivery analytics link found in dashboard")
            else:
                print("❌ Delivery analytics link not found")
            
            if 'advanced_payment' in response.text:
                print("✅ Advanced payment link found in dashboard")
            else:
                print("❌ Advanced payment link not found")
            
            if 'sales_analytics' in response.text:
                print("✅ Sales analytics link found in dashboard")
            else:
                print("❌ Sales analytics link not found")
            
            return True
        else:
            print(f"❌ Dashboard not accessible: {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ Error testing dashboard: {e}")
        return False

def main():
    """Main execution"""
    
    print("🚀 INVESTIGATING ROUTE VISIBILITY")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test new analytics routes
    working_routes, broken_routes = test_new_analytics_routes()
    
    # Check navigation templates
    navigation_links = check_navigation_templates()
    
    # Check main dashboard links
    dashboard_links = check_main_dashboard_links()
    
    # Add navigation links to dashboard
    links_added = add_navigation_links_to_dashboard()
    
    # Test dashboard after updates
    dashboard_working = test_dashboard_after_updates()
    
    print(f"\n🎯 ROUTE VISIBILITY INVESTIGATION SUMMARY")
    print("=" * 70)
    print(f"Working routes: {len(working_routes)}")
    print(f"Broken routes: {len(broken_routes)}")
    print(f"Navigation templates checked: {len(navigation_links)}")
    print(f"Dashboard links added: {'✅ YES' if links_added else '❌ NO'}")
    print(f"Dashboard working: {'✅ YES' if dashboard_working else '❌ NO'}")
    
    success = len(working_routes) > len(broken_routes) and dashboard_working
    
    if success:
        print(f"\n🎉 ROUTE VISIBILITY ISSUES RESOLVED!")
        print(f"✅ New analytics and payment routes are now accessible")
        print(f"✅ Navigation links added to main dashboard")
    else:
        print(f"\n⚠️ Some visibility issues may remain")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    main()
