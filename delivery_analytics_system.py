#!/usr/bin/env python3
"""
Professional Delivery Analytics Dashboard System
Replace the problematic rider performance system with comprehensive delivery analytics
"""

import os
from datetime import datetime

def create_delivery_analytics_routes():
    """Create comprehensive delivery analytics routes"""
    
    print("🚀 CREATING DELIVERY ANALYTICS DASHBOARD SYSTEM")
    print("=" * 60)
    
    # Create the delivery analytics routes file
    routes_content = '''"""
Professional Delivery Analytics Dashboard Routes
Real-time delivery tracking, performance KPIs, route optimization analytics, and comprehensive reporting
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import sqlite3
import json

delivery_analytics_bp = Blueprint('delivery_analytics', __name__, url_prefix='/delivery_analytics')

def get_db():
    """Get database connection"""
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    return conn

@delivery_analytics_bp.route('/')
@login_required
def dashboard():
    """Main Delivery Analytics Dashboard"""
    try:
        db = get_db()
        
        # Get key metrics
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        # Total deliveries today
        today_deliveries = db.execute('''
            SELECT COUNT(*) as count
            FROM orders
            WHERE DATE(order_date) = ? AND status = 'Delivered'
        ''', (today,)).fetchone()['count']
        
        # Pending deliveries
        pending_deliveries = db.execute('''
            SELECT COUNT(*) as count 
            FROM orders 
            WHERE status IN ('Processing', 'Shipped', 'Out for Delivery')
        ''').fetchone()['count']
        
        # Average delivery time (in hours)
        avg_delivery_time = db.execute('''
            SELECT AVG(
                CASE 
                    WHEN actual_delivery_date IS NOT NULL AND order_date IS NOT NULL
                    THEN (julianday(actual_delivery_date) - julianday(order_date)) * 24
                    ELSE NULL
                END
            ) as avg_hours
            FROM orders 
            WHERE status = 'Delivered' AND actual_delivery_date IS NOT NULL
        ''').fetchone()['avg_hours'] or 0
        
        # Top performing riders
        top_riders = db.execute('''
            SELECT r.name, r.phone, 
                   COUNT(o.order_id) as total_deliveries,
                   AVG(COALESCE(r.rating, 4.0)) as avg_rating,
                   COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as completed_deliveries
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            WHERE DATE(o.order_date) >= ?
            GROUP BY r.rider_id
            ORDER BY completed_deliveries DESC
            LIMIT 5
        ''', (week_ago,)).fetchall()
        
        # Recent delivery activities
        recent_activities = db.execute('''
            SELECT o.order_id, o.customer_name, o.status, o.order_date,
                   r.name as rider_name, o.total_amount
            FROM orders o
            LEFT JOIN riders r ON o.rider_id = r.rider_id
            WHERE o.status IN ('Delivered', 'Out for Delivery', 'Shipped')
            ORDER BY o.order_date DESC
            LIMIT 10
        ''').fetchall()
        
        # Delivery performance by area
        area_performance = db.execute('''
            SELECT COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified"), 
                   COUNT(*) as total_orders,
                   COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered_orders,
                   AVG(
                       CASE 
                           WHEN actual_delivery_date IS NOT NULL AND order_date IS NOT NULL
                           THEN (julianday(actual_delivery_date) - julianday(order_date)) * 24
                           ELSE NULL
                       END
                   ) as avg_delivery_hours
            FROM orders o
            WHERE DATE(o.order_date) >= ?
            GROUP BY COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified")
            HAVING total_orders >= 2
            ORDER BY delivered_orders DESC
            LIMIT 10
        ''', (month_ago,)).fetchall()
        
        context = {
            'title': 'Delivery Analytics Dashboard',
            'today_deliveries': today_deliveries,
            'pending_deliveries': pending_deliveries,
            'avg_delivery_time': round(avg_delivery_time, 1),
            'top_riders': top_riders,
            'recent_activities': recent_activities,
            'area_performance': area_performance,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/dashboard.html', **context)
        
    except Exception as e:
        flash(f'Error loading delivery analytics dashboard: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@delivery_analytics_bp.route('/real_time_tracking')
@login_required
def real_time_tracking():
    """Real-time Delivery Tracking"""
    try:
        db = get_db()
        
        # Get active deliveries
        active_deliveries = db.execute('''
            SELECT o.order_id, o.customer_name, COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified"), o.status,
                   o.order_date, o.estimated_delivery_date,
                   r.name as rider_name, r.phone as rider_phone, r.vehicle_type
            FROM orders o
            LEFT JOIN riders r ON o.rider_id = r.rider_id
            WHERE o.status IN ('Processing', 'Shipped', 'Out for Delivery')
            ORDER BY o.order_date DESC
        ''').fetchall()
        
        # Get delivery locations for mapping
        delivery_locations = db.execute('''
            SELECT DISTINCT delivery_address, 
                   COUNT(*) as order_count,
                   COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_count
            FROM orders
            WHERE delivery_address IS NOT NULL
            GROUP BY delivery_address
            ORDER BY order_count DESC
            LIMIT 20
        ''').fetchall()
        
        context = {
            'title': 'Real-time Delivery Tracking',
            'active_deliveries': active_deliveries,
            'delivery_locations': delivery_locations,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/real_time_tracking.html', **context)
        
    except Exception as e:
        flash(f'Error loading real-time tracking: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/performance_kpis')
@login_required
def performance_kpis():
    """Delivery Performance KPIs"""
    try:
        db = get_db()
        
        # Calculate various KPIs
        today = datetime.now().date()
        week_ago = today - timedelta(days=7)
        month_ago = today - timedelta(days=30)
        
        # Delivery success rate
        success_rate = db.execute('''
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_orders
            FROM orders
            WHERE DATE(order_date) >= ?
        ''', (month_ago,)).fetchone()
        
        # On-time delivery rate
        ontime_rate = db.execute('''
            SELECT 
                COUNT(*) as total_delivered,
                COUNT(CASE 
                    WHEN actual_delivery_date <= estimated_delivery_date THEN 1 
                    END) as ontime_delivered
            FROM orders
            WHERE status = 'Delivered' 
            AND actual_delivery_date IS NOT NULL 
            AND estimated_delivery_date IS NOT NULL
            AND DATE(order_date) >= ?
        ''', (month_ago,)).fetchone()
        
        # Average delivery cost per order
        avg_delivery_cost = db.execute('''
            SELECT AVG(delivery_charges) as avg_cost
            FROM orders
            WHERE status = 'Delivered' AND DATE(order_date) >= ?
        ''', (month_ago,)).fetchone()['avg_cost'] or 0
        
        # Rider efficiency metrics
        rider_efficiency = db.execute('''
            SELECT r.name, r.rider_id,
                   COUNT(o.order_id) as total_assignments,
                   COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as completed_deliveries,
                   AVG(COALESCE(r.rating, 4.0)) as avg_rating,
                   AVG(
                       CASE 
                           WHEN o.actual_delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                           THEN (julianday(o.actual_delivery_date) - julianday(o.order_date)) * 24
                           ELSE NULL
                       END
                   ) as avg_delivery_hours
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            WHERE DATE(o.order_date) >= ?
            GROUP BY r.rider_id
            HAVING total_assignments > 0
            ORDER BY completed_deliveries DESC
        ''', (month_ago,)).fetchall()
        
        context = {
            'title': 'Delivery Performance KPIs',
            'success_rate': success_rate,
            'ontime_rate': ontime_rate,
            'avg_delivery_cost': round(avg_delivery_cost, 2),
            'rider_efficiency': rider_efficiency,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/performance_kpis.html', **context)
        
    except Exception as e:
        flash(f'Error loading performance KPIs: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/route_optimization')
@login_required
def route_optimization():
    """Route Optimization Analytics"""
    try:
        db = get_db()
        
        # Get route efficiency data
        route_data = db.execute('''
            SELECT delivery_address,
                   COUNT(*) as total_deliveries,
                   AVG(
                       CASE 
                           WHEN actual_delivery_date IS NOT NULL AND order_date IS NOT NULL
                           THEN (julianday(actual_delivery_date) - julianday(order_date)) * 24
                           ELSE NULL
                       END
                   ) as avg_delivery_hours,
                   AVG(delivery_charges) as avg_delivery_cost,
                   COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as successful_deliveries
            FROM orders
            WHERE delivery_address IS NOT NULL
            AND DATE(order_date) >= DATE('now', '-30 days')
            GROUP BY delivery_address
            HAVING total_deliveries >= 2
            ORDER BY total_deliveries DESC
        ''').fetchall()
        
        # Get rider route efficiency
        rider_routes = db.execute('''
            SELECT r.name, r.rider_id,
                   COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified"),
                   COUNT(*) as deliveries_to_area,
                   AVG(
                       CASE 
                           WHEN o.actual_delivery_date IS NOT NULL AND o.order_date IS NOT NULL
                           THEN (julianday(o.actual_delivery_date) - julianday(o.order_date)) * 24
                           ELSE NULL
                       END
                   ) as avg_delivery_hours
            FROM riders r
            JOIN orders o ON r.rider_id = o.rider_id
            WHERE COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified") IS NOT NULL
            AND DATE(o.order_date) >= DATE('now', '-30 days')
            GROUP BY r.rider_id, COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified")
            HAVING deliveries_to_area >= 2
            ORDER BY r.name, deliveries_to_area DESC
        ''').fetchall()
        
        context = {
            'title': 'Route Optimization Analytics',
            'route_data': route_data,
            'rider_routes': rider_routes,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/route_optimization.html', **context)
        
    except Exception as e:
        flash(f'Error loading route optimization: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/comprehensive_reports')
@login_required
def comprehensive_reports():
    """Comprehensive Delivery Reports"""
    try:
        db = get_db()
        
        # Available report types
        report_types = [
            {
                'id': 'daily_delivery_summary',
                'name': 'Daily Delivery Summary',
                'description': 'Complete summary of daily delivery activities',
                'icon': 'calendar-day'
            },
            {
                'id': 'weekly_performance',
                'name': 'Weekly Performance Report',
                'description': 'Weekly delivery performance metrics and trends',
                'icon': 'calendar-week'
            },
            {
                'id': 'monthly_analytics',
                'name': 'Monthly Analytics Report',
                'description': 'Comprehensive monthly delivery analytics',
                'icon': 'calendar-alt'
            },
            {
                'id': 'rider_performance',
                'name': 'Rider Performance Report',
                'description': 'Individual rider performance analysis',
                'icon': 'user-tie'
            },
            {
                'id': 'area_analysis',
                'name': 'Delivery Area Analysis',
                'description': 'Performance analysis by delivery areas',
                'icon': 'map-marked-alt'
            },
            {
                'id': 'cost_analysis',
                'name': 'Delivery Cost Analysis',
                'description': 'Analysis of delivery costs and efficiency',
                'icon': 'dollar-sign'
            },
            {
                'id': 'customer_satisfaction',
                'name': 'Customer Satisfaction Report',
                'description': 'Customer feedback and satisfaction metrics',
                'icon': 'smile'
            },
            {
                'id': 'route_efficiency',
                'name': 'Route Efficiency Report',
                'description': 'Analysis of delivery route efficiency',
                'icon': 'route'
            },
            {
                'id': 'time_analysis',
                'name': 'Delivery Time Analysis',
                'description': 'Analysis of delivery times and patterns',
                'icon': 'clock'
            },
            {
                'id': 'trend_analysis',
                'name': 'Delivery Trend Analysis',
                'description': 'Long-term delivery trends and forecasting',
                'icon': 'chart-line'
            }
        ]
        
        context = {
            'title': 'Comprehensive Delivery Reports',
            'report_types': report_types,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/comprehensive_reports.html', **context)
        
    except Exception as e:
        flash(f'Error loading comprehensive reports: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/api/delivery_stats')
@login_required
def api_delivery_stats():
    """API endpoint for delivery statistics"""
    try:
        db = get_db()
        
        # Get delivery stats for charts
        daily_stats = db.execute('''
            SELECT DATE(order_date) as date,
                   COUNT(*) as total_orders,
                   COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_orders
            FROM orders
            WHERE DATE(order_date) >= DATE('now', '-30 days')
            GROUP BY DATE(order_date)
            ORDER BY date
        ''').fetchall()
        
        stats_data = {
            'daily_stats': [dict(row) for row in daily_stats],
            'success': True
        }
        
        return jsonify(stats_data)
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})

# Register the blueprint
def register_delivery_analytics_routes(app):
    """Register delivery analytics routes with the main app"""
    app.register_blueprint(delivery_analytics_bp)
'''
    
    # Save the routes file
    with open('routes/delivery_analytics.py', 'w', encoding='utf-8') as f:
        f.write(routes_content)
    
    print("✅ Created delivery analytics routes")
    return True

def main():
    """Main execution"""
    print("🚀 DELIVERY ANALYTICS SYSTEM CREATION")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create delivery analytics routes
    routes_created = create_delivery_analytics_routes()
    
    print(f"\n🎯 CREATION SUMMARY")
    print("=" * 70)
    print(f"Delivery analytics routes created: {routes_created}")
    
    print(f"\n✅ Delivery analytics system created successfully!")
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
