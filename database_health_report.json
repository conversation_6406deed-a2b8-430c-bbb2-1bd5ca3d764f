{"timestamp": "2025-07-22T11:56:09.002045", "total_tables": 96, "table_stats": {"activity_logs": {"row_count": 14, "column_count": 9}, "aging_analysis": {"row_count": 0, "column_count": 16}, "ai_bug_reports": {"row_count": 0, "column_count": 13}, "ai_error_patterns": {"row_count": 0, "column_count": 8}, "ai_performance_metrics": {"row_count": 0, "column_count": 6}, "api_key_usage_log": {"row_count": 0, "column_count": 7}, "api_keys": {"row_count": 4, "column_count": 11}, "api_usage_logs": {"row_count": 0, "column_count": 11}, "approval_workflow": {"row_count": 0, "column_count": 7}, "audit_logs": {"row_count": 0, "column_count": 10}, "bank_details": {"row_count": 0, "column_count": 13}, "batch_allocations": {"row_count": 0, "column_count": 8}, "batch_financial_data": {"row_count": 0, "column_count": 22}, "batch_management": {"row_count": 0, "column_count": 10}, "batches": {"row_count": 0, "column_count": 10}, "bike_documents": {"row_count": 0, "column_count": 13}, "categories": {"row_count": 0, "column_count": 8}, "challans": {"row_count": 0, "column_count": 13}, "chart_of_accounts": {"row_count": 0, "column_count": 10}, "collection_activities": {"row_count": 0, "column_count": 17}, "cost_centers": {"row_count": 0, "column_count": 9}, "credit_management": {"row_count": 0, "column_count": 15}, "customer_files": {"row_count": 0, "column_count": 12}, "customer_financial_profile": {"row_count": 0, "column_count": 21}, "customer_ledger": {"row_count": 0, "column_count": 14}, "customer_ledger_advanced": {"row_count": 0, "column_count": 25}, "customer_ledger_enhanced": {"row_count": 0, "column_count": 17}, "customer_pricing": {"row_count": 0, "column_count": 6}, "customers": {"row_count": 0, "column_count": 30}, "delivery_attempts": {"row_count": 0, "column_count": 10}, "delivery_challans": {"row_count": 0, "column_count": 14}, "delivery_routes": {"row_count": 0, "column_count": 9}, "division_analytics": {"row_count": 6, "column_count": 7}, "division_audit_log": {"row_count": 7, "column_count": 9}, "division_permissions": {"row_count": 0, "column_count": 8}, "divisions": {"row_count": 6, "column_count": 25}, "document_tracking": {"row_count": 0, "column_count": 9}, "duplicate_resolutions": {"row_count": 0, "column_count": 9}, "employees": {"row_count": 0, "column_count": 13}, "file_uploads": {"row_count": 0, "column_count": 15}, "financial_metrics": {"row_count": 0, "column_count": 13}, "financial_metrics_daily": {"row_count": 0, "column_count": 14}, "financial_reports_config": {"row_count": 0, "column_count": 10}, "financial_tracking": {"row_count": 0, "column_count": 13}, "inventory": {"row_count": 0, "column_count": 25}, "inventory_allocations": {"row_count": 0, "column_count": 11}, "invoice_items": {"row_count": 0, "column_count": 9}, "invoice_payments": {"row_count": 0, "column_count": 11}, "invoices": {"row_count": 0, "column_count": 11}, "invoices_enhanced": {"row_count": 0, "column_count": 25}, "multi_dimensional_ledger": {"row_count": 0, "column_count": 12}, "notification_delivery_log": {"row_count": 0, "column_count": 7}, "notification_settings": {"row_count": 0, "column_count": 9}, "notification_stats": {"row_count": 0, "column_count": 8}, "notification_templates": {"row_count": 10, "column_count": 7}, "notification_types": {"row_count": 10, "column_count": 7}, "notifications": {"row_count": 19, "column_count": 13}, "order_approvals": {"row_count": 0, "column_count": 7}, "order_attachments": {"row_count": 0, "column_count": 6}, "order_files": {"row_count": 0, "column_count": 13}, "order_items": {"row_count": 0, "column_count": 15}, "order_status_history": {"row_count": 0, "column_count": 8}, "orders": {"row_count": 0, "column_count": 77}, "payment_allocations": {"row_count": 0, "column_count": 15}, "payment_allocations_enhanced": {"row_count": 0, "column_count": 8}, "payment_attachments": {"row_count": 0, "column_count": 6}, "payment_knockoffs": {"row_count": 0, "column_count": 7}, "payments": {"row_count": 0, "column_count": 15}, "payments_advanced": {"row_count": 0, "column_count": 23}, "payments_enhanced": {"row_count": 0, "column_count": 18}, "pending_invoices": {"row_count": 0, "column_count": 28}, "permission_audit_logs": {"row_count": 0, "column_count": 10}, "permissions": {"row_count": 0, "column_count": 5}, "po_types": {"row_count": 0, "column_count": 6}, "product_images": {"row_count": 0, "column_count": 10}, "products": {"row_count": 0, "column_count": 47}, "rider_assignments": {"row_count": 0, "column_count": 13}, "rider_bikes": {"row_count": 0, "column_count": 15}, "rider_documents": {"row_count": 0, "column_count": 13}, "rider_locations": {"row_count": 0, "column_count": 6}, "rider_performance_logs": {"row_count": 0, "column_count": 14}, "riders": {"row_count": 5, "column_count": 28}, "role_permissions": {"row_count": 0, "column_count": 3}, "roles": {"row_count": 0, "column_count": 8}, "search_suggestions": {"row_count": 0, "column_count": 8}, "settings": {"row_count": 0, "column_count": 7}, "stock_movements": {"row_count": 0, "column_count": 12}, "suppliers": {"row_count": 0, "column_count": 16}, "system_notifications": {"row_count": 0, "column_count": 14}, "system_settings": {"row_count": 0, "column_count": 8}, "user_notifications": {"row_count": 0, "column_count": 12}, "user_permissions": {"row_count": 0, "column_count": 4}, "user_sessions": {"row_count": 0, "column_count": 9}, "users": {"row_count": 1, "column_count": 16}, "warehouse_processing": {"row_count": 0, "column_count": 9}, "warehouses": {"row_count": 1, "column_count": 17}}}