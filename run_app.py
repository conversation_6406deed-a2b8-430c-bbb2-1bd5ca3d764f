#!/usr/bin/env python3
"""
Simple launcher for the Medivent ERP application
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Starting Medivent ERP Application...")
    print("Current directory:", os.getcwd())
    print("Python version:", sys.version)
    
    # Try importing required modules
    print("Checking Flask...")
    import flask
    print(f"Flask {flask.__version__} found")
    
    print("Checking other dependencies...")
    import sqlite3
    import pandas as pd
    print("Core dependencies found")
    
    # Import and run the app
    print("Importing app module...")
    import app
    print("App module imported successfully")
    
    print("Starting Flask server...")
    print("URL: http://localhost:3000")
    print("=" * 50)
    
    # Run the app
    app.app.run(host='0.0.0.0', port=3000, debug=True, use_reloader=False)
    
except ImportError as e:
    print(f"Import Error: {e}")
    print("Try installing missing dependencies with: pip install -r requirements.txt")
    sys.exit(1)
except Exception as e:
    print(f"Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
