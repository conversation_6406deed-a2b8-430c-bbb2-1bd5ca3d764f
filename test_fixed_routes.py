import time
import requests

print("Testing fixed routes...")
time.sleep(3)

# Test the previously problematic routes
test_routes = [
    ('/sales_analytics/', 'Sales Analytics Dashboard'),
    ('/sales_analytics/team_performance', 'Sales Team Performance'),
    ('/riders/performance', 'Rider Performance'),
    ('/delivery_analytics/performance_kpis', 'Delivery Performance KPIs'),
]

print("\n🧪 TESTING FIXED ROUTES")
print("=" * 50)

working_routes = 0
total_routes = len(test_routes)

for route, name in test_routes:
    try:
        response = requests.get(f"http://127.0.0.1:3000{route}", timeout=10)
        
        if response.status_code == 200:
            status = "✅ WORKING"
            working_routes += 1
        elif response.status_code == 302:
            status = "🔄 REDIRECT (Auth)"
            working_routes += 1
        elif response.status_code == 404:
            status = "❌ NOT FOUND"
        elif response.status_code == 500:
            status = "💥 SERVER ERROR"
        else:
            status = f"⚠️ STATUS {response.status_code}"
        
        print(f"{status:<20} {route:<35} ({name})")
        
    except requests.exceptions.ConnectionError:
        print(f"❌ CONNECTION ERROR  {route:<35} ({name})")
    except Exception as e:
        print(f"❌ ERROR: {str(e)[:20]:<15} {route:<35} ({name})")

print(f"\n📊 TESTING SUMMARY:")
print(f"Total routes tested: {total_routes}")
print(f"Working routes: {working_routes}")
print(f"Success rate: {(working_routes/total_routes*100):.1f}%")

if working_routes == total_routes:
    print(f"\n🎉 ALL ROUTES WORKING!")
    print(f"✅ Sales analytics errors resolved")
    print(f"✅ Rider performance errors resolved")
    print(f"✅ Database column errors fixed")
    print(f"✅ Missing templates created")
else:
    print(f"\n⚠️ Some routes may need additional attention")
