#!/usr/bin/env python3
"""
Create Safety Backup Before Cleanup
"""

import shutil
import os
from datetime import datetime

def create_safety_backup():
    """Create a safety backup before cleanup"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"SAFETY_BACKUP_BEFORE_CLEANUP_{timestamp}"
    
    print(f"🛡️ Creating safety backup: {backup_name}")
    
    try:
        # Create backup directory
        if not os.path.exists(backup_name):
            os.makedirs(backup_name)
        
        # Copy critical files
        critical_files = [
            'app.py',
            'requirements.txt',
            'start_ai_enhanced_erp.py',
            'instance/medivent.db'
        ]
        
        for file in critical_files:
            if os.path.exists(file):
                if '/' in file:
                    # Create subdirectory if needed
                    subdir = os.path.dirname(file)
                    backup_subdir = os.path.join(backup_name, subdir)
                    if not os.path.exists(backup_subdir):
                        os.makedirs(backup_subdir)
                
                shutil.copy2(file, os.path.join(backup_name, file))
                print(f"  ✅ Backed up: {file}")
        
        # Copy critical directories
        critical_dirs = ['routes', 'utils', 'templates']
        
        for dir_name in critical_dirs:
            if os.path.exists(dir_name):
                shutil.copytree(dir_name, os.path.join(backup_name, dir_name))
                print(f"  ✅ Backed up directory: {dir_name}")
        
        print(f"✅ Safety backup created successfully: {backup_name}")
        return backup_name
        
    except Exception as e:
        print(f"❌ Backup failed: {e}")
        return None

if __name__ == "__main__":
    create_safety_backup()
