#!/usr/bin/env python3
"""
Simple Blueprint Registration Fix
"""

import os
from datetime import datetime

def fix_blueprint_registration():
    """Fix blueprint registration in app.py"""
    
    print("🔧 FIXING BLUEPRINT REGISTRATION")
    print("=" * 50)
    
    try:
        # Read current app.py
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # Check if blueprints are already registered
        blueprints_to_add = []
        
        if "from routes.delivery_analytics import delivery_analytics_bp" not in app_content:
            blueprints_to_add.append(("from routes.delivery_analytics import delivery_analytics_bp", "app.register_blueprint(delivery_analytics_bp)"))
        
        if "from routes.advanced_payment import advanced_payment_bp" not in app_content:
            blueprints_to_add.append(("from routes.advanced_payment import advanced_payment_bp", "app.register_blueprint(advanced_payment_bp)"))
        
        if "from routes.sales_analytics import sales_analytics_bp" not in app_content:
            blueprints_to_add.append(("from routes.sales_analytics import sales_analytics_bp", "app.register_blueprint(sales_analytics_bp)"))
        
        if not blueprints_to_add:
            print("✅ All blueprints already registered")
            return True
        
        # Add imports after other route imports
        for import_line, register_line in blueprints_to_add:
            if import_line not in app_content:
                # Add import after existing route imports
                app_content = app_content.replace(
                    "from datetime import datetime",
                    f"from datetime import datetime\n{import_line}"
                )
                print(f"✅ Added import: {import_line}")
        
        # Add registrations before the main execution block
        for import_line, register_line in blueprints_to_add:
            if register_line not in app_content:
                app_content = app_content.replace(
                    'if __name__ == "__main__":',
                    f'{register_line}\n\nif __name__ == "__main__":'
                )
                print(f"✅ Added registration: {register_line}")
        
        # Write back to app.py
        with open('app.py', 'w', encoding='utf-8') as f:
            f.write(app_content)
        
        print(f"✅ Blueprint registration fixed")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_template_directories():
    """Create missing template directories"""
    
    print("\n🎨 CREATING TEMPLATE DIRECTORIES")
    print("=" * 50)
    
    dirs_to_create = [
        'templates/delivery_analytics',
        'templates/advanced_payment', 
        'templates/sales_analytics'
    ]
    
    for dir_path in dirs_to_create:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"✅ Created: {dir_path}")
        else:
            print(f"ℹ️ Exists: {dir_path}")

def create_basic_templates():
    """Create basic dashboard templates"""
    
    print("\n📄 CREATING BASIC TEMPLATES")
    print("=" * 50)
    
    # Basic template content
    basic_template = '''{% extends 'base.html' %}

{% block title %}Dashboard - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-line text-primary"></i> Dashboard
        </h1>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Dashboard</h6>
                </div>
                <div class="card-body">
                    <p>Welcome to the dashboard. This feature is currently being developed.</p>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        This dashboard will provide comprehensive analytics and management capabilities.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
'''
    
    templates_to_create = [
        'templates/delivery_analytics/dashboard.html',
        'templates/advanced_payment/dashboard.html',
        'templates/sales_analytics/dashboard.html'
    ]
    
    for template_path in templates_to_create:
        if not os.path.exists(template_path):
            with open(template_path, 'w', encoding='utf-8') as f:
                f.write(basic_template)
            print(f"✅ Created: {template_path}")
        else:
            print(f"ℹ️ Exists: {template_path}")

def main():
    """Main execution"""
    print("🚀 SIMPLE BLUEPRINT FIX")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Fix blueprint registration
    registration_fixed = fix_blueprint_registration()
    
    # Create template directories
    create_template_directories()
    
    # Create basic templates
    create_basic_templates()
    
    print(f"\n🎯 FIX SUMMARY")
    print("=" * 70)
    print(f"Blueprint registration: {'✅ FIXED' if registration_fixed else '❌ FAILED'}")
    
    if registration_fixed:
        print(f"\n✅ Blueprint issues fixed!")
        print(f"🔄 Server restart required to load new blueprints")
    else:
        print(f"\n⚠️ Some issues remain")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
