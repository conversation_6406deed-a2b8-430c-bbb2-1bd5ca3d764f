{% extends 'base.html' %}

{% block title %}View Rider - {{ rider.name }} - Medivent Pharmaceuticals ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-motorcycle text-primary"></i> Rider Details - {{ rider.name }}
        </h1>
        <div>
            <a href="{{ url_for('riders') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Riders
            </a>
            <a href="{{ url_for('edit_rider', rider_id=rider.rider_id) }}" class="btn btn-primary shadow-sm">
                <i class="fas fa-edit fa-sm text-white-50"></i> Edit Rider
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Rider Information Card -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Rider Information</h6>
                    <div class="dropdown no-arrow">
                        <span class="badge badge-{{ 'success' if rider.status == 'Active' else 'warning' if rider.status == 'pending' else 'danger' }} badge-lg">
                            {{ rider.status }}
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Rider ID:</label>
                                <p class="text-gray-900">{{ rider.rider_id }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">Full Name:</label>
                                <p class="text-gray-900">{{ rider.name }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">Email:</label>
                                <p class="text-gray-900">{{ rider.email or 'Not provided' }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">Phone:</label>
                                <p class="text-gray-900">{{ rider.phone }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Vehicle Type:</label>
                                <p class="text-gray-900">{{ rider.vehicle_type }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">Vehicle Number:</label>
                                <p class="text-gray-900">{{ rider.vehicle_number or 'Not provided' }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">License Number:</label>
                                <p class="text-gray-900">{{ rider.license_number or 'Not provided' }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">Current Location:</label>
                                <p class="text-gray-900">{{ rider.current_location or 'Not available' }}</p>
                            </div>
                        </div>
                    </div>

                    {% if rider.make %}
                    <hr>
                    <h6 class="font-weight-bold text-primary">Bike Information</h6>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">Make & Model:</label>
                                <p class="text-gray-900">{{ rider.make }} {{ rider.model }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">Year:</label>
                                <p class="text-gray-900">{{ rider.year or 'Not specified' }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="font-weight-bold">License Plate:</label>
                                <p class="text-gray-900">{{ rider.license_plate }}</p>
                            </div>
                            <div class="form-group">
                                <label class="font-weight-bold">Color:</label>
                                <p class="text-gray-900">{{ rider.color or 'Not specified' }}</p>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Performance Stats Card -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Performance Stats</h6>
                </div>
                <div class="card-body">
                    <div class="text-center">
                        <div class="mb-3">
                            <h4 class="text-primary">{{ "%.1f"|format((stats.avg_rating if stats and stats.avg_rating else 0)|float) }}</h4>
                            <small class="text-muted">Average Rating</small>
                        </div>
                        <div class="row text-center">
                            <div class="col-6">
                                <h5 class="text-success">{{ stats.total_orders or 0 }}</h5>
                                <small class="text-muted">Total Orders</small>
                            </div>
                            <div class="col-6">
                                <h5 class="text-info">{{ stats.completed_orders or 0 }}</h5>
                                <small class="text-muted">Completed</small>
                            </div>
                        </div>
                        <hr>
                        <div class="text-center">
                            <h5 class="text-warning">Rs. {{ "{:,.0f}"|format((stats.total_revenue if stats and stats.total_revenue else 0)|float) }}</h5>
                            <small class="text-muted">Total Revenue</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions Card -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        {% if rider.status == 'pending' %}
                        <button class="btn btn-success btn-sm" onclick="approveRider('{{ rider.rider_id }}')">
                            <i class="fas fa-check"></i> Approve Rider
                        </button>
                        {% endif %}
                        
                        {% if rider.status == 'Active' %}
                        <button class="btn btn-warning btn-sm" onclick="suspendRider('{{ rider.rider_id }}')">
                            <i class="fas fa-ban"></i> Suspend Rider
                        </button>
                        {% endif %}
                        
                        {% if rider.status == 'Suspended' %}
                        <button class="btn btn-success btn-sm" onclick="approveRider('{{ rider.rider_id }}')">
                            <i class="fas fa-check"></i> Reactivate Rider
                        </button>
                        {% endif %}
                        
                        <a href="{{ url_for('edit_rider', rider_id=rider.rider_id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit"></i> Edit Details
                        </a>
                        
                        <button class="btn btn-danger btn-sm" onclick="deleteRider('{{ rider.rider_id }}')">
                            <i class="fas fa-trash"></i> Delete Rider
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Orders -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recent Orders (Last 20)</h6>
        </div>
        <div class="card-body">
            {% if orders %}
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Status</th>
                            <th>Rating</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for order in orders %}
                        <tr>
                            <td>{{ order.order_id }}</td>
                            <td>{{ order.customer_name }}</td>
                            <td>Rs. {{ "{:,.0f}"|format((order.order_amount if order.order_amount else 0)|float) }}</td>
                            <td>{{ order.order_date[:10] if order.order_date else 'N/A' }}</td>
                            <td>
                                <span class="badge badge-{{ 'success' if order.status == 'Delivered' else 'primary' if order.status == 'Dispatched' else 'warning' }}">
                                    {{ order.status }}
                                </span>
                            </td>
                            <td>
                                {% if order.delivery_rating %}
                                    {% for i in range(order.delivery_rating) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(5 - order.delivery_rating) %}
                                        <i class="far fa-star text-muted"></i>
                                    {% endfor %}
                                {% else %}
                                    <span class="text-muted">Not rated</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                <p class="text-muted">No orders found for this rider.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
function approveRider(riderId) {
    if (confirm('Are you sure you want to approve this rider?')) {
        fetch(`/riders/${riderId}/approve`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error approving rider', 'error');
        });
    }
}

function suspendRider(riderId) {
    if (confirm('Are you sure you want to suspend this rider?')) {
        fetch(`/riders/${riderId}/suspend`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'warning');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error suspending rider', 'error');
        });
    }
}

function deleteRider(riderId) {
    if (confirm('Are you sure you want to delete this rider? This action cannot be undone.')) {
        fetch(`/riders/${riderId}/delete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');
                setTimeout(() => window.location.href = '/riders', 1500);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('Error deleting rider', 'error');
        });
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}
</script>
{% endblock %}
