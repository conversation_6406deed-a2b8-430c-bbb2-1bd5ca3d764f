import time
import requests

print("Testing routes...")
time.sleep(3)

routes = [
    '/delivery_analytics/dashboard',
    '/delivery_analytics/comprehensive_reports', 
    '/riders/dashboard'
]

for route in routes:
    try:
        response = requests.get(f"http://127.0.0.1:3000{route}")
        print(f'{route}: {response.status_code}')
    except Exception as e:
        print(f'{route}: ERROR - {e}')
