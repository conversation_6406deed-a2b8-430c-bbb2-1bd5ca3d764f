{"timestamp": "2025-07-22T09:24:41.082427", "summary": {"total_tests": 36, "passed_tests": 11, "success_rate": 30.555555555555557}, "detailed_results": {"api_tests": [{"endpoint": "/api/customers", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/inventory", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/reports", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/users", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/reports/sales_summary", "method": "POST", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/reports/inventory_status", "method": "POST", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/delivery_analytics/api/delivery_stats", "method": "GET", "status": "FAIL", "issue": "Endpoint not found"}, {"endpoint": "/advanced_payment/api/payment_stats", "method": "GET", "status": "FAIL", "issue": "Endpoint not found"}, {"endpoint": "/sales_analytics/api/sales_trends", "method": "GET", "status": "FAIL", "issue": "Endpoint not found"}, {"endpoint": "/sales_analytics/api/division_performance", "method": "GET", "status": "FAIL", "issue": "Endpoint not found"}], "database_tests": [{"test": "integrity_check", "status": "PASS"}, {"test": "general", "status": "FAIL", "issue": "foreign key mismatch - \"division_permissions\" referencing \"users\""}], "authentication_tests": [{"endpoint": "/api/users", "status": "PASS", "protection": "LOGIN_PAGE"}, {"endpoint": "/admin/bulk_operations", "status": "FAIL", "issue": "No authentication required"}, {"endpoint": "/admin/system_health", "status": "FAIL", "issue": "No authentication required"}, {"endpoint": "/advanced_payment/bulk_processing", "status": "FAIL", "issue": "No authentication required"}, {"endpoint": "/sales_analytics/division_ledger", "status": "FAIL", "issue": "No authentication required"}], "route_tests": [{"route": "/delivery_analytics/", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/delivery_analytics/real_time_tracking", "status": "FAIL", "issue": "Route not found"}, {"route": "/delivery_analytics/performance_kpis", "status": "FAIL", "issue": "Route not found"}, {"route": "/delivery_analytics/comprehensive_reports", "status": "FAIL", "issue": "Route not found"}, {"route": "/advanced_payment/", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/advanced_payment/bulk_processing", "status": "FAIL", "issue": "Route not found"}, {"route": "/advanced_payment/automated_matching", "status": "FAIL", "issue": "Route not found"}, {"route": "/advanced_payment/reconciliation", "status": "FAIL", "issue": "Route not found"}, {"route": "/advanced_payment/analytics", "status": "FAIL", "issue": "Route not found"}, {"route": "/sales_analytics/", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/sales_analytics/salesperson_ledger", "status": "FAIL", "issue": "Route not found"}, {"route": "/sales_analytics/team_performance", "status": "FAIL", "issue": "Route not found"}, {"route": "/sales_analytics/division_ledger", "status": "FAIL", "issue": "Route not found"}, {"route": "/sales_analytics/division_analysis", "status": "FAIL", "issue": "Route not found"}, {"route": "/admin/bulk_operations", "status": "FAIL", "issue": "Route not found"}, {"route": "/admin/data_export", "status": "FAIL", "issue": "Route not found"}, {"route": "/admin/system_health", "status": "FAIL", "issue": "Route not found"}, {"route": "/reports/advanced/index", "status": "FAIL", "issue": "Route not found"}, {"route": "/reports/sales/index", "status": "FAIL", "issue": "Route not found"}], "file_upload_tests": []}}