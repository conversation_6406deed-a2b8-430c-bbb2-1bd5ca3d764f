#!/usr/bin/env python3
"""
Debug Routes - Check what routes are actually registered
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def debug_flask_routes():
    """Debug Flask routes to see what's actually registered"""
    
    print("🔍 DEBUGGING FLASK ROUTES")
    print("=" * 50)
    
    try:
        # Import the Flask app
        from app import app
        
        print("✅ Flask app imported successfully")
        
        # Get all registered routes
        routes = []
        for rule in app.url_map.iter_rules():
            routes.append({
                'endpoint': rule.endpoint,
                'methods': list(rule.methods),
                'rule': str(rule)
            })
        
        # Filter for rider-related routes
        rider_routes = [r for r in routes if 'rider' in r['endpoint'].lower()]
        
        print(f"\n📊 TOTAL ROUTES: {len(routes)}")
        print(f"📊 RIDER-RELATED ROUTES: {len(rider_routes)}")
        
        print(f"\n🔍 RIDER-RELATED ROUTES:")
        for route in rider_routes:
            print(f"   Endpoint: {route['endpoint']}")
            print(f"   Rule: {route['rule']}")
            print(f"   Methods: {route['methods']}")
            print(f"   ---")
        
        # Check specifically for dashboard routes
        dashboard_routes = [r for r in routes if 'dashboard' in r['endpoint'].lower()]
        
        print(f"\n🔍 DASHBOARD-RELATED ROUTES:")
        for route in dashboard_routes:
            print(f"   Endpoint: {route['endpoint']}")
            print(f"   Rule: {route['rule']}")
            print(f"   Methods: {route['methods']}")
            print(f"   ---")
        
        # Check if riders_dashboard exists
        riders_dashboard_exists = any(r['endpoint'] == 'riders_dashboard' for r in routes)
        rider_dashboard_exists = any(r['endpoint'] == 'rider_dashboard' for r in routes)
        
        print(f"\n🎯 SPECIFIC ROUTE CHECK:")
        print(f"   'riders_dashboard' exists: {riders_dashboard_exists}")
        print(f"   'rider_dashboard' exists: {rider_dashboard_exists}")
        
        if not riders_dashboard_exists and rider_dashboard_exists:
            print(f"   ⚠️ ISSUE: Template references 'riders_dashboard' but only 'rider_dashboard' exists")
        elif riders_dashboard_exists and not rider_dashboard_exists:
            print(f"   ✅ CORRECT: 'riders_dashboard' exists as expected")
        elif not riders_dashboard_exists and not rider_dashboard_exists:
            print(f"   ❌ ERROR: Neither 'riders_dashboard' nor 'rider_dashboard' exists")
        else:
            print(f"   ⚠️ CONFLICT: Both 'riders_dashboard' and 'rider_dashboard' exist")
        
        return routes
        
    except Exception as e:
        print(f"❌ Error debugging routes: {e}")
        import traceback
        traceback.print_exc()
        return None

def check_template_references():
    """Check template references"""
    
    print(f"\n🎨 CHECKING TEMPLATE REFERENCES")
    print("=" * 50)
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all url_for references to rider dashboard
        import re
        
        rider_dashboard_refs = re.findall(r"url_for\(['\"]([^'\"]*rider[^'\"]*dashboard[^'\"]*)['\"]", content)
        
        print(f"Template references to rider dashboard:")
        for ref in rider_dashboard_refs:
            print(f"   - {ref}")
        
        # Check line 659 specifically
        lines = content.split('\n')
        if len(lines) > 658:
            line_659 = lines[658]  # 0-indexed
            print(f"\nLine 659 content:")
            print(f"   {line_659.strip()}")
        
        return rider_dashboard_refs
        
    except Exception as e:
        print(f"❌ Error checking template: {e}")
        return None

def main():
    """Main debug execution"""
    
    print("🚀 FLASK ROUTING DEBUG")
    print("=" * 70)
    
    # Debug routes
    routes = debug_flask_routes()
    
    # Check template references
    template_refs = check_template_references()
    
    print(f"\n🎯 DIAGNOSIS:")
    if routes and template_refs:
        riders_dashboard_exists = any(r['endpoint'] == 'riders_dashboard' for r in routes)
        
        if 'riders_dashboard' in template_refs and not riders_dashboard_exists:
            print(f"❌ PROBLEM: Template references 'riders_dashboard' but route doesn't exist")
            print(f"💡 SOLUTION: Either fix the route registration or update template reference")
        elif 'riders_dashboard' in template_refs and riders_dashboard_exists:
            print(f"⚠️ MYSTERY: Both template and route exist but Flask can't find it")
            print(f"💡 SOLUTION: Check for route conflicts or restart Flask server")
        else:
            print(f"🔍 Need to investigate further...")
    
    return routes is not None

if __name__ == "__main__":
    main()
