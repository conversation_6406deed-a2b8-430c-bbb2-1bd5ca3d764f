#!/usr/bin/env python3
"""
Check Dashboard Response
"""

import requests

def check_dashboard_response():
    try:
        response = requests.get('http://127.0.0.1:3000/dashboard', timeout=10)
        print(f'Status: {response.status_code}')
        print(f'Response length: {len(response.text)}')
        
        # Check if it's a login page
        if 'login' in response.text.lower():
            print('✅ Redirected to login page (expected for unauthenticated request)')
            print('This means the dashboard route is working correctly')
            return True
        
        # Check if it contains dashboard content
        if 'container-fluid' in response.text:
            print('✅ Dashboard content found')
            print('Analytics cards present:')
            print(f'  Delivery Analytics: {"delivery_analytics" in response.text}')
            print(f'  Advanced Payment: {"advanced_payment" in response.text}')
            print(f'  Sales Analytics: {"sales_analytics" in response.text}')
            return True
        
        # Show first 500 characters to understand what we're getting
        print('Response preview:')
        print(response.text[:500])
        print('...')
        
        return False
    
    except Exception as e:
        print(f'Error: {e}')
        return False

if __name__ == "__main__":
    check_dashboard_response()
