#!/usr/bin/env python3
"""
Test Enhanced Analytics System
"""

import requests

def test_enhanced_analytics():
    """Test the enhanced analytics routes"""
    
    print("🧪 TESTING ENHANCED ANALYTICS SYSTEM")
    print("=" * 50)
    
    routes_to_test = [
        ('/delivery_analytics/', 'Enhanced Delivery Analytics Dashboard'),
        ('/delivery_analytics/individual_rider_reports', 'Individual Rider Reports'),
        ('/delivery_analytics/customer_delivery_tracking', 'Customer Delivery Tracking'),
        ('/delivery_analytics/delivery_history', 'Delivery History'),
        ('/delivery_analytics/customer_satisfaction', 'Customer Satisfaction'),
        ('/riders/dashboard', 'Rebuilt Riders Dashboard'),
        ('/dashboard', 'Main Dashboard with Analytics Cards')
    ]
    
    working_routes = 0
    
    for route, description in routes_to_test:
        try:
            response = requests.get(f"http://127.0.0.1:3000{route}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {route} - {description}: Working")
                working_routes += 1
            else:
                print(f"❌ {route} - {description}: Status {response.status_code}")
        
        except Exception as e:
            print(f"❌ {route} - {description}: Error {e}")
    
    print(f"\n📊 ENHANCED ANALYTICS TEST SUMMARY")
    print("=" * 50)
    print(f"Working routes: {working_routes}/{len(routes_to_test)}")
    print(f"Success rate: {(working_routes/len(routes_to_test)*100):.1f}%")
    
    if working_routes == len(routes_to_test):
        print(f"\n🎉 ALL ENHANCED ANALYTICS FEATURES WORKING!")
        print(f"✅ Enhanced delivery analytics dashboard")
        print(f"✅ Individual rider performance reports")
        print(f"✅ Customer delivery tracking system")
        print(f"✅ Comprehensive delivery history")
        print(f"✅ Customer satisfaction metrics")
        print(f"✅ Rebuilt riders dashboard")
        print(f"✅ Main dashboard with analytics cards")
    else:
        print(f"\n⚠️ Some routes may need attention")
    
    return working_routes == len(routes_to_test)

if __name__ == "__main__":
    test_enhanced_analytics()
