{"timestamp": "2025-07-22T11:58:07.994597", "summary": {"total_tests": 36, "passed_tests": 35, "success_rate": 97.22222222222221}, "detailed_results": {"api_tests": [{"endpoint": "/api/customers", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/inventory", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/reports", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/users", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/reports/sales_summary", "method": "POST", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/api/reports/inventory_status", "method": "POST", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/delivery_analytics/api/delivery_stats", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/advanced_payment/api/payment_stats", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/sales_analytics/api/sales_trends", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"endpoint": "/sales_analytics/api/division_performance", "method": "GET", "status": "PASS", "response_type": "AUTH_REQUIRED"}], "database_tests": [{"test": "integrity_check", "status": "PASS"}, {"test": "general", "status": "FAIL", "issue": "foreign key mismatch - \"division_permissions\" referencing \"users\""}], "authentication_tests": [{"endpoint": "/api/users", "status": "PASS", "protection": "LOGIN_PAGE"}, {"endpoint": "/admin/bulk_operations", "status": "PASS", "protection": "LOGIN_PAGE"}, {"endpoint": "/admin/system_health", "status": "PASS", "protection": "LOGIN_PAGE"}, {"endpoint": "/advanced_payment/bulk_processing", "status": "PASS", "protection": "LOGIN_PAGE"}, {"endpoint": "/sales_analytics/division_ledger", "status": "PASS", "protection": "LOGIN_PAGE"}], "route_tests": [{"route": "/delivery_analytics/", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/delivery_analytics/real_time_tracking", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/delivery_analytics/performance_kpis", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/delivery_analytics/comprehensive_reports", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/advanced_payment/", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/advanced_payment/bulk_processing", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/advanced_payment/automated_matching", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/advanced_payment/reconciliation", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/advanced_payment/analytics", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/sales_analytics/", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/sales_analytics/salesperson_ledger", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/sales_analytics/team_performance", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/sales_analytics/division_ledger", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/sales_analytics/division_analysis", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/admin/bulk_operations", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/admin/data_export", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/admin/system_health", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/reports/advanced/index", "status": "PASS", "response_type": "AUTH_REQUIRED"}, {"route": "/reports/sales/index", "status": "PASS", "response_type": "AUTH_REQUIRED"}], "file_upload_tests": []}}