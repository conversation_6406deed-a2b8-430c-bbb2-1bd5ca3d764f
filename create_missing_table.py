#!/usr/bin/env python3
"""
Create Missing Database Table
"""

import sqlite3
from datetime import datetime

def create_duplicate_resolutions_table():
    """Create the missing duplicate_resolutions table"""
    
    print("🔧 CREATING MISSING TABLE: duplicate_resolutions")
    print("=" * 50)
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    try:
        # Create duplicate_resolutions table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS duplicate_resolutions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                order1_id INTEGER NOT NULL,
                order2_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                resolved_by TEXT NOT NULL,
                resolution_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create indexes for the table
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_duplicate_resolutions_order1 ON duplicate_resolutions(order1_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_duplicate_resolutions_order2 ON duplicate_resolutions(order2_id)')
        cursor.execute('CREATE INDEX IF NOT EXISTS idx_duplicate_resolutions_resolved_by ON duplicate_resolutions(resolved_by)')
        
        conn.commit()
        print("✅ Created duplicate_resolutions table with indexes")
        
    except sqlite3.Error as e:
        print(f"❌ Error creating table: {e}")
    
    conn.close()

if __name__ == "__main__":
    create_duplicate_resolutions_table()
