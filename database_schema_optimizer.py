#!/usr/bin/env python3
"""
Database Schema Optimization System
Add missing tables/columns, remove duplicates, ensure proper relationships and indexes
"""

import sqlite3
import os
from datetime import datetime

def analyze_current_schema():
    """Analyze current database schema"""
    
    print("🔍 ANALYZING CURRENT DATABASE SCHEMA")
    print("=" * 50)
    
    if not os.path.exists('instance/medivent.db'):
        print("❌ Database file not found!")
        return False
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = [row[0] for row in cursor.fetchall()]
    
    print(f"📊 Found {len(tables)} tables:")
    
    schema_info = {}
    
    for table in tables:
        if table.startswith('sqlite_'):
            continue
            
        try:
            # Get table info
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            
            # Get indexes
            cursor.execute(f"PRAGMA index_list({table})")
            indexes = cursor.fetchall()
            
            # Get foreign keys
            cursor.execute(f"PRAGMA foreign_key_list({table})")
            foreign_keys = cursor.fetchall()
            
            schema_info[table] = {
                'columns': columns,
                'indexes': indexes,
                'foreign_keys': foreign_keys
            }
            
            print(f"   ✅ {table}: {len(columns)} columns, {len(indexes)} indexes, {len(foreign_keys)} foreign keys")
            
        except sqlite3.Error as e:
            print(f"   ❌ Error analyzing {table}: {e}")
    
    conn.close()
    return schema_info

def identify_missing_tables():
    """Identify missing tables required by the application"""
    
    print("\n🔍 IDENTIFYING MISSING TABLES")
    print("=" * 50)
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    # Get existing tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    existing_tables = {row[0] for row in cursor.fetchall()}
    
    # Required tables based on application analysis
    required_tables = {
        'users', 'customers', 'products', 'orders', 'order_items',
        'inventory', 'warehouses', 'divisions', 'riders', 'payments',
        'invoices', 'delivery_challans', 'stock_movements', 'activity_logs',
        'notifications', 'user_permissions', 'file_uploads', 'system_settings',
        'customer_ledger', 'payment_attachments', 'duplicate_resolutions'
    }
    
    missing_tables = required_tables - existing_tables
    
    if missing_tables:
        print(f"❌ Missing tables: {', '.join(missing_tables)}")
    else:
        print("✅ All required tables exist")
    
    conn.close()
    return missing_tables

def identify_duplicate_tables():
    """Identify duplicate or similar tables"""
    
    print("\n🔍 IDENTIFYING DUPLICATE TABLES")
    print("=" * 50)
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    # Get all tables
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
    tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
    
    # Look for similar table names
    similar_tables = []
    for i, table1 in enumerate(tables):
        for table2 in tables[i+1:]:
            if are_similar_table_names(table1, table2):
                similar_tables.append((table1, table2))
                print(f"🔍 Similar tables: {table1} ↔ {table2}")
    
    # Look for tables with identical structures
    duplicate_structures = []
    table_structures = {}
    
    for table in tables:
        try:
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            structure_sig = tuple(sorted([(col[1], col[2]) for col in columns]))
            
            if structure_sig in table_structures:
                duplicate_structures.append((table_structures[structure_sig], table))
                print(f"🔍 Duplicate structure: {table_structures[structure_sig]} ↔ {table}")
            else:
                table_structures[structure_sig] = table
                
        except sqlite3.Error as e:
            print(f"⚠️ Error analyzing {table}: {e}")
    
    conn.close()
    
    print(f"📊 Found {len(similar_tables)} similar table pairs")
    print(f"📊 Found {len(duplicate_structures)} duplicate structure pairs")
    
    return similar_tables, duplicate_structures

def are_similar_table_names(name1, name2):
    """Check if two table names are similar"""
    # Remove common suffixes
    clean_name1 = name1.lower().replace('_enhanced', '').replace('_advanced', '').replace('_new', '')
    clean_name2 = name2.lower().replace('_enhanced', '').replace('_advanced', '').replace('_new', '')
    
    return clean_name1 == clean_name2 and name1 != name2

def add_missing_indexes():
    """Add missing indexes for performance optimization"""
    
    print("\n🚀 ADDING MISSING INDEXES")
    print("=" * 50)
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    # Indexes to add for performance
    indexes_to_add = [
        # Orders table indexes
        ('idx_orders_customer_id', 'orders', 'customer_id'),
        ('idx_orders_status', 'orders', 'status'),
        ('idx_orders_order_date', 'orders', 'order_date'),
        ('idx_orders_rider_id', 'orders', 'rider_id'),
        
        # Order items indexes
        ('idx_order_items_order_id', 'order_items', 'order_id'),
        ('idx_order_items_product_id', 'order_items', 'product_id'),
        
        # Inventory indexes
        ('idx_inventory_product_id', 'inventory', 'product_id'),
        ('idx_inventory_warehouse_id', 'inventory', 'warehouse_id'),
        ('idx_inventory_batch_number', 'inventory', 'batch_number'),
        
        # Payments indexes
        ('idx_payments_order_id', 'payments', 'order_id'),
        ('idx_payments_customer_id', 'payments', 'customer_id'),
        ('idx_payments_payment_date', 'payments', 'payment_date'),
        
        # Activity logs indexes
        ('idx_activity_logs_user_id', 'activity_logs', 'user_id'),
        ('idx_activity_logs_timestamp', 'activity_logs', 'timestamp'),
        ('idx_activity_logs_action', 'activity_logs', 'action'),
        
        # Notifications indexes
        ('idx_notifications_user_id', 'notifications', 'user_id'),
        ('idx_notifications_is_read', 'notifications', 'is_read'),
        ('idx_notifications_created_at', 'notifications', 'created_at'),
        
        # Stock movements indexes
        ('idx_stock_movements_product_id', 'stock_movements', 'product_id'),
        ('idx_stock_movements_warehouse_id', 'stock_movements', 'warehouse_id'),
        ('idx_stock_movements_movement_date', 'stock_movements', 'movement_date')
    ]
    
    added_count = 0
    
    for index_name, table_name, column_name in indexes_to_add:
        try:
            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                print(f"⚠️ Table {table_name} doesn't exist, skipping index {index_name}")
                continue
            
            # Check if column exists
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = [col[1] for col in cursor.fetchall()]
            if column_name not in columns:
                print(f"⚠️ Column {column_name} doesn't exist in {table_name}, skipping index {index_name}")
                continue
            
            # Check if index already exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name=?", (index_name,))
            if cursor.fetchone():
                print(f"ℹ️ Index {index_name} already exists")
                continue
            
            # Create index
            cursor.execute(f"CREATE INDEX {index_name} ON {table_name}({column_name})")
            print(f"✅ Created index: {index_name} on {table_name}({column_name})")
            added_count += 1
            
        except sqlite3.Error as e:
            print(f"❌ Error creating index {index_name}: {e}")
    
    conn.commit()
    conn.close()
    
    print(f"📊 Added {added_count} new indexes")
    return added_count

def add_missing_columns():
    """Add missing columns to existing tables"""
    
    print("\n🚀 ADDING MISSING COLUMNS")
    print("=" * 50)
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    # Columns to add
    columns_to_add = [
        # Orders table enhancements
        ('orders', 'priority', 'TEXT DEFAULT "Normal"'),
        ('orders', 'estimated_delivery_date', 'DATE'),
        ('orders', 'actual_delivery_date', 'DATE'),
        ('orders', 'delivery_notes', 'TEXT'),
        
        # Customers table enhancements
        ('customers', 'credit_limit', 'DECIMAL(10,2) DEFAULT 0'),
        ('customers', 'payment_terms', 'INTEGER DEFAULT 30'),
        ('customers', 'last_order_date', 'DATE'),
        
        # Products table enhancements
        ('products', 'reorder_level', 'INTEGER DEFAULT 10'),
        ('products', 'max_stock_level', 'INTEGER DEFAULT 1000'),
        ('products', 'supplier_id', 'INTEGER'),
        
        # Riders table enhancements
        ('riders', 'vehicle_type', 'TEXT DEFAULT "Motorcycle"'),
        ('riders', 'license_number', 'TEXT'),
        ('riders', 'emergency_contact', 'TEXT'),
        
        # Warehouses table enhancements
        ('warehouses', 'capacity', 'INTEGER DEFAULT 10000'),
        ('warehouses', 'current_utilization', 'INTEGER DEFAULT 0'),
        ('warehouses', 'manager_id', 'INTEGER')
    ]
    
    added_count = 0
    
    for table_name, column_name, column_definition in columns_to_add:
        try:
            # Check if table exists
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            if not cursor.fetchone():
                print(f"⚠️ Table {table_name} doesn't exist, skipping column {column_name}")
                continue
            
            # Check if column already exists
            cursor.execute(f"PRAGMA table_info({table_name})")
            existing_columns = [col[1] for col in cursor.fetchall()]
            if column_name in existing_columns:
                print(f"ℹ️ Column {column_name} already exists in {table_name}")
                continue
            
            # Add column
            cursor.execute(f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_definition}")
            print(f"✅ Added column: {table_name}.{column_name}")
            added_count += 1
            
        except sqlite3.Error as e:
            print(f"❌ Error adding column {table_name}.{column_name}: {e}")
    
    conn.commit()
    conn.close()
    
    print(f"📊 Added {added_count} new columns")
    return added_count

def optimize_database():
    """Run database optimization commands"""
    
    print("\n🚀 OPTIMIZING DATABASE")
    print("=" * 50)
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    try:
        # Analyze database for query optimization
        print("📊 Analyzing database for query optimization...")
        cursor.execute("ANALYZE")
        
        # Vacuum database to reclaim space
        print("🧹 Vacuuming database to reclaim space...")
        cursor.execute("VACUUM")
        
        # Update statistics
        print("📈 Updating database statistics...")
        cursor.execute("PRAGMA optimize")
        
        print("✅ Database optimization completed")
        
    except sqlite3.Error as e:
        print(f"❌ Error during optimization: {e}")
    
    conn.close()

def main():
    """Main schema optimization execution"""
    print("🚀 DATABASE SCHEMA OPTIMIZATION")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Analyze current schema
    schema_info = analyze_current_schema()
    if not schema_info:
        return
    
    # Identify issues
    missing_tables = identify_missing_tables()
    similar_tables, duplicate_structures = identify_duplicate_tables()
    
    # Add missing columns
    added_columns = add_missing_columns()
    
    # Add missing indexes
    added_indexes = add_missing_indexes()
    
    # Optimize database
    optimize_database()
    
    print(f"\n🎯 OPTIMIZATION SUMMARY")
    print("=" * 70)
    print(f"Tables analyzed: {len(schema_info)}")
    print(f"Missing tables identified: {len(missing_tables)}")
    print(f"Similar table pairs: {len(similar_tables)}")
    print(f"Duplicate structure pairs: {len(duplicate_structures)}")
    print(f"Columns added: {added_columns}")
    print(f"Indexes added: {added_indexes}")
    
    if missing_tables:
        print(f"\n⚠️ MISSING TABLES TO CREATE:")
        for table in missing_tables:
            print(f"   • {table}")
    
    if similar_tables or duplicate_structures:
        print(f"\n⚠️ DUPLICATE TABLES TO REVIEW:")
        for table1, table2 in similar_tables + duplicate_structures:
            print(f"   • {table1} ↔ {table2}")
    
    print(f"\n✅ Database schema optimization completed!")
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
