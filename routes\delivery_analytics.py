"""
Delivery Analytics Routes
"""

from flask import Blueprint, render_template, request, redirect, url_for, flash, jsonify
from flask_login import login_required, current_user
from datetime import datetime, timedelta
import sqlite3

delivery_analytics_bp = Blueprint('delivery_analytics', __name__, url_prefix='/delivery_analytics')

def get_db():
    """Get database connection"""
    conn = sqlite3.connect('instance/medivent.db')
    conn.row_factory = sqlite3.Row
    return conn

@delivery_analytics_bp.route('/')
@delivery_analytics_bp.route('/dashboard')
@login_required
def dashboard():
    """Enhanced Delivery Analytics Dashboard"""
    try:
        db = get_db()

        # Get comprehensive delivery statistics
        today = datetime.now().date()
        last_month = today - timedelta(days=30)

        # Delivery Statistics
        delivery_stats = db.execute("""
            SELECT
                COUNT(*) as total_deliveries,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as completed_deliveries,
                COUNT(CASE WHEN DATE(order_date) = ? THEN 1 END) as today_deliveries,
                AVG(CASE WHEN status = 'Delivered' THEN 1.0 ELSE 0.0 END) * 100 as success_rate,
                COUNT(CASE WHEN DATE(order_date) >= ? THEN 1 END) as monthly_deliveries
            FROM orders
            WHERE order_date >= ?
        """, (today, last_month, last_month)).fetchone()

        # Calculate growth and improvements (mock data for demo)
        delivery_stats_dict = {
            'total_deliveries': delivery_stats['total_deliveries'] or 0,
            'success_rate': delivery_stats['success_rate'] or 0,
            'avg_delivery_time': 2.5,  # Mock data
            'customer_satisfaction': 4.3,  # Mock data
            'delivery_growth': 15.2,  # Mock data
            'success_improvement': 3.1,  # Mock data
            'time_improvement': 8.5,  # Mock data
            'total_ratings': 156  # Mock data
        }

        # Status Distribution
        status_distribution = db.execute("""
            SELECT status, COUNT(*) as count
            FROM orders
            WHERE DATE(order_date) >= ?
            GROUP BY status
            ORDER BY count DESC
        """, (last_month,)).fetchall()

        # Active Riders
        active_riders = db.execute("""
            SELECT r.rider_id, r.name, r.phone, r.vehicle_type,
                   COUNT(o.order_id) as deliveries_today,
                   CASE
                       WHEN COUNT(CASE WHEN o.status = 'Out for Delivery' THEN 1 END) > 0 THEN 'Out for Delivery'
                       WHEN COUNT(CASE WHEN o.status = 'Delivered' AND DATE(o.order_date) = ? THEN 1 END) > 0 THEN 'Available'
                       ELSE 'Idle'
                   END as status
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id AND DATE(o.order_date) = ?
            WHERE ((r.is_active = 1 OR r.is_active IS NULL) OR r.is_active IS NULL) OR r.is_active IS NULL
            GROUP BY r.rider_id
            ORDER BY deliveries_today DESC
            LIMIT 10
        """, (today, today)).fetchall()

        # Add mock location data for active riders
        active_riders_with_location = []
        locations = ['Downtown Area', 'North Zone', 'South Zone', 'East District', 'West Side']
        for i, rider in enumerate(active_riders):
            rider_dict = dict(rider)
            rider_dict['current_location'] = locations[i % len(locations)]
            active_riders_with_location.append(rider_dict)

        # Top Performing Riders
        top_performers = db.execute("""
            SELECT r.name, r.rider_id,
                   COUNT(o.order_id) as deliveries_completed,
                   COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as successful_deliveries,
                   CASE
                       WHEN COUNT(o.order_id) > 0
                       THEN (COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) * 100.0 / COUNT(o.order_id))
                       ELSE 0
                   END as success_rate
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            WHERE DATE(o.order_date) >= ? OR o.order_date IS NULL
            GROUP BY r.rider_id
            HAVING COUNT(o.order_id) > 0
            ORDER BY success_rate DESC, deliveries_completed DESC
            LIMIT 5
        """, (last_month,)).fetchall()

        # Efficiency Metrics (mock data for comprehensive dashboard)
        efficiency_metrics = {
            'on_time_percentage': 87.5,
            'avg_distance': 12.3,
            'fuel_efficiency': 2.1,
            'cost_per_delivery': 45.50
        }

        # All Riders for filter dropdown
        all_riders = db.execute("""
            SELECT rider_id, name FROM riders
            WHERE is_active = 1 OR is_active IS NULL
            ORDER BY name
        """).fetchall()

        db.close()

        context = {
            'delivery_stats': delivery_stats_dict,
            'status_distribution': status_distribution,
            'active_riders': active_riders_with_location,
            'top_performers': top_performers,
            'efficiency_metrics': efficiency_metrics,
            'riders': all_riders,
            'current_user': current_user,
            'now': datetime.now()
        }

        return render_template('delivery_analytics/dashboard.html', **context)

    except Exception as e:
        flash(f'Error loading delivery analytics: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@delivery_analytics_bp.route('/individual_rider_reports')
@login_required
def individual_rider_reports():
    """Individual Rider Performance Reports"""
    try:
        db = get_db()

        # Get rider filter from request
        selected_rider = request.args.get('rider_id', '')
        date_range = request.args.get('date_range', '30')  # Default 30 days

        # Calculate date range
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=int(date_range))

        # Get all riders for dropdown
        all_riders = db.execute("""
            SELECT rider_id, name FROM riders
            WHERE is_active = 1 OR is_active IS NULL
            ORDER BY name
        """).fetchall()

        rider_performance = None
        delivery_history = []

        if selected_rider:
            # Get rider performance data
            rider_performance = db.execute("""
                SELECT r.name, r.phone, r.vehicle_type, COALESCE(r.rating, 4.0),
                       COUNT(o.order_id) as total_deliveries,
                       COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as completed_deliveries,
                       COUNT(CASE WHEN o.status = 'Out for Delivery' THEN 1 END) as current_deliveries,
                       AVG(CASE WHEN o.status = 'Delivered' THEN 1.0 ELSE 0.0 END) * 100 as success_rate
                FROM riders r
                LEFT JOIN orders o ON r.rider_id = o.rider_id
                    AND DATE(o.order_date) BETWEEN ? AND ?
                WHERE r.rider_id = ?
                GROUP BY r.rider_id
            """, (start_date, end_date, selected_rider)).fetchone()

            # Get delivery history
            delivery_history = db.execute("""
                SELECT o.order_id, o.customer_name, o.status, o.order_date,
                       COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified"), c.mobile_number, c.institution_type,
                       COALESCE(o.total_amount, o.order_amount, 0) as order_value
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                WHERE o.rider_id = ? AND DATE(o.order_date) BETWEEN ? AND ?
                ORDER BY o.order_date DESC
                LIMIT 50
            """, (selected_rider, start_date, end_date)).fetchall()

        db.close()

        context = {
            'all_riders': all_riders,
            'selected_rider': selected_rider,
            'date_range': date_range,
            'rider_performance': rider_performance,
            'delivery_history': delivery_history,
            'start_date': start_date,
            'end_date': end_date,
            'current_user': current_user
        }

        return render_template('delivery_analytics/individual_rider_reports.html', **context)

    except Exception as e:
        flash(f'Error loading rider reports: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/customer_delivery_tracking')
@login_required
def customer_delivery_tracking():
    """Customer Delivery Tracking by Mobile Number"""
    try:
        db = get_db()

        # Get search parameters
        mobile_number = request.args.get('mobile_number', '').strip()
        customer_name = request.args.get('customer_name', '').strip()
        po_number = request.args.get('po_number', '').strip()

        customer_deliveries = []
        customer_info = None

        if mobile_number or customer_name or po_number:
            # Build search query
            where_conditions = []
            params = []

            if mobile_number:
                where_conditions.append("c.mobile_number LIKE ?")
                params.append(f"%{mobile_number}%")

            if customer_name:
                where_conditions.append("c.name LIKE ?")
                params.append(f"%{customer_name}%")

            if po_number:
                where_conditions.append("o.po_number LIKE ?")
                params.append(f"%{po_number}%")

            where_clause = " AND ".join(where_conditions)

            # Get customer deliveries
            customer_deliveries = db.execute(f"""
                SELECT o.order_id, o.customer_name, o.status, o.order_date,
                       COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified"), o.po_number, r.name as rider_name,
                       c.mobile_number, c.institution_type,
                       COALESCE(o.total_amount, o.order_amount, 0) as order_value
                FROM orders o
                LEFT JOIN customers c ON o.customer_id = c.customer_id
                LEFT JOIN riders r ON o.rider_id = r.rider_id
                WHERE {where_clause}
                ORDER BY o.order_date DESC
                LIMIT 100
            """, params).fetchall()

            # Get customer summary info
            if customer_deliveries:
                customer_info = db.execute(f"""
                    SELECT c.name, c.mobile_number, c.institution_type, c.address,
                           COUNT(o.order_id) as total_orders,
                           COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as delivered_orders,
                           SUM(COALESCE(o.total_amount, o.order_amount, 0)) as total_value
                    FROM customers c
                    LEFT JOIN orders o ON c.customer_id = o.customer_id
                    WHERE {where_clause}
                    GROUP BY c.customer_id
                    LIMIT 1
                """, params).fetchone()

        db.close()

        context = {
            'mobile_number': mobile_number,
            'customer_name': customer_name,
            'po_number': po_number,
            'customer_deliveries': customer_deliveries,
            'customer_info': customer_info,
            'current_user': current_user
        }

        return render_template('delivery_analytics/customer_delivery_tracking.html', **context)

    except Exception as e:
        flash(f'Error loading customer tracking: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/delivery_history')
@login_required
def delivery_history():
    """Comprehensive Delivery History with Advanced Filters"""
    try:
        db = get_db()

        # Get filter parameters
        date_range = request.args.get('date_range', '30')
        rider_filter = request.args.get('rider_id', '')
        status_filter = request.args.get('status', '')
        customer_type = request.args.get('customer_type', '')

        # Calculate date range
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=int(date_range))

        # Build query with filters
        where_conditions = ["DATE(o.order_date) BETWEEN ? AND ?"]
        params = [start_date, end_date]

        if rider_filter:
            where_conditions.append("o.rider_id = ?")
            params.append(rider_filter)

        if status_filter:
            where_conditions.append("o.status = ?")
            params.append(status_filter)

        if customer_type:
            where_conditions.append("c.institution_type = ?")
            params.append(customer_type)

        where_clause = " AND ".join(where_conditions)

        # Get delivery history
        delivery_history = db.execute(f"""
            SELECT o.order_id, o.customer_name, o.status, o.order_date,
                   COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified"), o.po_number, r.name as rider_name,
                   c.mobile_number, c.institution_type,
                   COALESCE(o.total_amount, o.order_amount, 0) as order_value
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            LEFT JOIN riders r ON o.rider_id = r.rider_id
            WHERE {where_clause}
            ORDER BY o.order_date DESC
            LIMIT 200
        """, params).fetchall()

        # Get summary statistics
        summary_stats = db.execute(f"""
            SELECT
                COUNT(*) as total_deliveries,
                COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as completed_deliveries,
                SUM(COALESCE(o.total_amount, o.order_amount, 0)) as total_value,
                COUNT(DISTINCT o.rider_id) as riders_involved,
                COUNT(DISTINCT c.customer_id) as customers_served
            FROM orders o
            LEFT JOIN customers c ON o.customer_id = c.customer_id
            WHERE {where_clause}
        """, params).fetchone()

        # Get filter options
        all_riders = db.execute("SELECT rider_id, name FROM riders ORDER BY name").fetchall()
        customer_types = ['hospital', 'pharmacy', 'clinic', 'distributor']
        statuses = ['Delivered', 'Out for Delivery', 'Dispatched', 'Pending', 'Processing']

        db.close()

        context = {
            'delivery_history': delivery_history,
            'summary_stats': summary_stats,
            'all_riders': all_riders,
            'customer_types': customer_types,
            'statuses': statuses,
            'filters': {
                'date_range': date_range,
                'rider_id': rider_filter,
                'status': status_filter,
                'customer_type': customer_type
            },
            'start_date': start_date,
            'end_date': end_date,
            'current_user': current_user
        }

        return render_template('delivery_analytics/delivery_history.html', **context)

    except Exception as e:
        flash(f'Error loading delivery history: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/customer_satisfaction')
@login_required
def customer_satisfaction():
    """Customer Satisfaction Metrics and Feedback Analysis"""
    try:
        db = get_db()

        # Get satisfaction metrics (mock data for comprehensive demo)
        satisfaction_metrics = {
            'overall_rating': 4.3,
            'total_reviews': 156,
            'five_star': 78,
            'four_star': 45,
            'three_star': 23,
            'two_star': 7,
            'one_star': 3,
            'response_rate': 67.8
        }

        # Get recent feedback (mock data)
        recent_feedback = [
            {
                'order_id': 'ORD001',
                'customer_name': 'ABC Hospital',
                'rider_name': 'John Doe',
                'rating': 5,
                'feedback': 'Excellent service, on-time delivery',
                'date': '2025-07-22'
            },
            {
                'order_id': 'ORD002',
                'customer_name': 'XYZ Pharmacy',
                'rider_name': 'Jane Smith',
                'rating': 4,
                'feedback': 'Good service, could be faster',
                'date': '2025-07-21'
            }
        ]

        # Get rider satisfaction ratings
        rider_ratings = db.execute("""
            SELECT r.name, COALESCE(r.rating, 4.0), COUNT(o.order_id) as total_deliveries
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            WHERE COALESCE(r.rating, 4.0) IS NOT NULL
            GROUP BY r.rider_id
            ORDER BY COALESCE(r.rating, 4.0) DESC
            LIMIT 10
        """).fetchall()

        db.close()

        context = {
            'satisfaction_metrics': satisfaction_metrics,
            'recent_feedback': recent_feedback,
            'rider_ratings': rider_ratings,
            'current_user': current_user
        }

        return render_template('delivery_analytics/customer_satisfaction.html', **context)

    except Exception as e:
        flash(f'Error loading customer satisfaction: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

# Continue with existing routes...

@delivery_analytics_bp.route('/real_time_tracking')
@login_required
def real_time_tracking():
    """Real-time Delivery Tracking"""
    try:
        db = get_db()
        
        # Get active deliveries
        active_deliveries = db.execute(
            """SELECT o.order_id, o.customer_name, COALESCE(COALESCE(o.delivery_address, o.customer_address, o.shipping_address, "Address not specified"), o.customer_address, "Address not specified"), o.status,
                      r.name as rider_name 
               FROM orders o
               LEFT JOIN riders r ON o.rider_id = r.rider_id
               WHERE o.status IN ('Processing', 'Shipped', 'Out for Delivery')
               ORDER BY o.order_date DESC"""
        ).fetchall()
        
        context = {
            'title': 'Real-time Delivery Tracking',
            'active_deliveries': active_deliveries,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/real_time_tracking.html', **context)
        
    except Exception as e:
        flash(f'Error loading real-time tracking: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/performance_kpis')
@login_required
def performance_kpis():
    """Delivery Performance KPIs"""
    try:
        db = get_db()
        
        # Calculate KPIs
        total_orders = db.execute("SELECT COUNT(*) as count FROM orders").fetchone()['count']
        delivered_orders = db.execute("SELECT COUNT(*) as count FROM orders WHERE status = 'Delivered'").fetchone()['count']
        
        success_rate = (delivered_orders / total_orders * 100) if total_orders > 0 else 0
        
        # Rider performance
        rider_performance = db.execute(
            """SELECT r.name, COUNT(o.order_id) as total_deliveries,
                      AVG(COALESCE(r.rating, 4.0)) as avg_rating
               FROM riders r
               LEFT JOIN orders o ON r.rider_id = o.rider_id
               GROUP BY r.rider_id
               ORDER BY total_deliveries DESC"""
        ).fetchall()
        
        context = {
            'title': 'Delivery Performance KPIs',
            'success_rate': round(success_rate, 1),
            'total_orders': total_orders,
            'delivered_orders': delivered_orders,
            'rider_performance': rider_performance,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/performance_kpis.html', **context)
        
    except Exception as e:
        flash(f'Error loading performance KPIs: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/comprehensive_reports')
@login_required
def comprehensive_reports():
    """Comprehensive Delivery Reports"""
    try:
        # Available report types
        report_types = [
            {'id': 'daily_summary', 'name': 'Daily Delivery Summary', 'icon': 'calendar-day'},
            {'id': 'weekly_performance', 'name': 'Weekly Performance', 'icon': 'calendar-week'},
            {'id': 'monthly_analytics', 'name': 'Monthly Analytics', 'icon': 'calendar-alt'},
            {'id': 'rider_performance', 'name': 'Rider Performance', 'icon': 'user-tie'},
            {'id': 'area_analysis', 'name': 'Area Analysis', 'icon': 'map-marked-alt'},
            {'id': 'cost_analysis', 'name': 'Cost Analysis', 'icon': 'dollar-sign'},
            {'id': 'customer_satisfaction', 'name': 'Customer Satisfaction', 'icon': 'smile'},
            {'id': 'route_efficiency', 'name': 'Route Efficiency', 'icon': 'route'},
            {'id': 'time_analysis', 'name': 'Time Analysis', 'icon': 'clock'},
            {'id': 'trend_analysis', 'name': 'Trend Analysis', 'icon': 'chart-line'}
        ]
        
        context = {
            'title': 'Comprehensive Delivery Reports',
            'report_types': report_types,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('delivery_analytics/comprehensive_reports.html', **context)
        
    except Exception as e:
        flash(f'Error loading comprehensive reports: {str(e)}', 'error')
        return redirect(url_for('delivery_analytics.dashboard'))

@delivery_analytics_bp.route('/api/delivery_stats')
@login_required
def api_delivery_stats():
    """API endpoint for delivery statistics"""
    try:
        db = get_db()
        
        # Get basic stats
        stats = db.execute(
            """SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_orders,
                COUNT(CASE WHEN status IN ('Processing', 'Shipped', 'Out for Delivery') THEN 1 END) as pending_orders
               FROM orders"""
        ).fetchone()
        
        return jsonify({
            'success': True,
            'data': dict(stats)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)})
