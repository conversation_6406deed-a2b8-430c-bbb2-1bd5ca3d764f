{% extends 'base.html' %}

{% block title %}Delivery Performance KPIs - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar text-primary"></i> Delivery Performance KPIs
        </h1>
        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Success Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if success_rate %}
                                    {{ "%.1f"|format(success_rate.delivered_orders / success_rate.total_orders * 100) }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">On-time Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if ontime_rate %}
                                    {{ "%.1f"|format(ontime_rate.ontime_delivered / ontime_rate.total_delivered * 100) }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Avg Cost</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ avg_delivery_cost or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Active Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ rider_efficiency|length or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-motorcycle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Rider Efficiency</h6>
                </div>
                <div class="card-body">
                    {% if rider_efficiency %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Rider</th>
                                        <th>Total Assignments</th>
                                        <th>Completed</th>
                                        <th>Success Rate</th>
                                        <th>Avg Rating</th>
                                        <th>Avg Delivery Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in rider_efficiency %}
                                    <tr>
                                        <td>{{ rider.name }}</td>
                                        <td>{{ rider.total_assignments }}</td>
                                        <td>{{ rider.completed_deliveries }}</td>
                                        <td>{{ "%.1f"|format(rider.completed_deliveries / rider.total_assignments * 100) }}%</td>
                                        <td>{{ "%.1f"|format(rider.avg_rating or 0) }}</td>
                                        <td>{{ "%.1f"|format(rider.avg_delivery_hours or 0) }}h</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No rider performance data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}