{% extends 'base.html' %}

{% block title %}Riders Dashboard - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-motorcycle text-primary"></i> Riders Management Dashboard
        </h1>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_riders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_riders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Today's Deliveries</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_stats.delivered_orders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Out for Delivery</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_stats.out_for_delivery or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Riders Performance Table -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Performing Riders</h6>
                </div>
                <div class="card-body">
                    {% if riders_performance %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Rider Name</th>
                                        <th>Phone</th>
                                        <th>Vehicle</th>
                                        <th>Total Deliveries</th>
                                        <th>Completed</th>
                                        <th>Success Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in riders_performance %}
                                    <tr>
                                        <td>{{ rider.name }}</td>
                                        <td>{{ rider.phone }}</td>
                                        <td>{{ rider.vehicle_type or 'N/A' }}</td>
                                        <td>{{ rider.total_deliveries }}</td>
                                        <td>{{ rider.completed_deliveries }}</td>
                                        <td>
                                            {% if rider.total_deliveries > 0 %}
                                                {{ "%.1f"|format((rider.completed_deliveries / rider.total_deliveries) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No rider performance data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activities</h6>
                </div>
                <div class="card-body">
                    {% if recent_deliveries %}
                        {% for delivery in recent_deliveries %}
                        <div class="d-flex align-items-center border-bottom py-2">
                            <div class="mr-3">
                                <div class="icon-circle bg-{{ 'success' if delivery.status == 'Delivered' else 'warning' }}">
                                    <i class="fas fa-{{ 'check' if delivery.status == 'Delivered' else 'truck' }} text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-gray-500">Order #{{ delivery.order_id }}</div>
                                <div class="font-weight-bold">{{ delivery.customer_name }}</div>
                                <div class="small">{{ delivery.rider_name or 'Unassigned' }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No recent activities.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-chart-line"></i> Delivery Analytics
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('riders_management') }}" class="btn btn-success btn-block">
                                <i class="fas fa-users-cog"></i> Manage Riders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.real_time_tracking') }}" class="btn btn-info btn-block">
                                <i class="fas fa-map-marker-alt"></i> Real-time Tracking
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.performance_kpis') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-chart-bar"></i> Performance KPIs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}