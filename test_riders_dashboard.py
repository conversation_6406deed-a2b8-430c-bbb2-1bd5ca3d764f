#!/usr/bin/env python3
"""
Test Riders Dashboard Route
"""

import requests

def test_riders_dashboard():
    """Test the riders dashboard route"""
    
    print("🧪 TESTING RIDERS DASHBOARD ROUTE")
    print("=" * 50)
    
    try:
        response = requests.get("http://127.0.0.1:3000/riders/dashboard", timeout=10)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Length: {len(response.text)}")
        
        if response.status_code == 200:
            print("✅ Riders dashboard route is working")
            
            # Check if it contains expected content
            if 'Riders Management Dashboard' in response.text:
                print("✅ Dashboard contains expected title")
            else:
                print("⚠️ Dashboard may be redirecting or missing content")
            
            return True
        else:
            print(f"❌ Riders dashboard route failed with status {response.status_code}")
            print(f"Response content: {response.text[:500]}...")
            return False
    
    except Exception as e:
        print(f"❌ Error testing riders dashboard: {e}")
        return False

def test_base_template_navigation():
    """Test if the base template navigation works"""
    
    print("\n🧪 TESTING BASE TEMPLATE NAVIGATION")
    print("=" * 50)
    
    try:
        # Test main dashboard which includes the navigation
        response = requests.get("http://127.0.0.1:3000/dashboard", timeout=10)
        
        print(f"Dashboard Status Code: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Main dashboard loads successfully")
            
            # Check if navigation contains riders_dashboard link
            if 'riders_dashboard' in response.text:
                print("✅ Navigation contains riders_dashboard reference")
            else:
                print("⚠️ Navigation may not contain riders_dashboard reference")
            
            return True
        else:
            print(f"❌ Main dashboard failed with status {response.status_code}")
            return False
    
    except Exception as e:
        print(f"❌ Error testing navigation: {e}")
        return False

def main():
    """Main test execution"""
    
    print("🚀 RIDERS DASHBOARD ROUTING TEST")
    print("=" * 70)
    
    # Test the specific route
    route_working = test_riders_dashboard()
    
    # Test navigation
    navigation_working = test_base_template_navigation()
    
    print(f"\n🎯 TEST SUMMARY")
    print("=" * 70)
    print(f"Riders Dashboard Route: {'✅ WORKING' if route_working else '❌ FAILED'}")
    print(f"Navigation Template: {'✅ WORKING' if navigation_working else '❌ FAILED'}")
    
    if route_working and navigation_working:
        print(f"\n🎉 RIDERS DASHBOARD ROUTING SUCCESSFUL!")
        print(f"✅ Route function exists and responds correctly")
        print(f"✅ Navigation template loads without errors")
        print(f"✅ No BuildError exceptions detected")
    else:
        print(f"\n⚠️ SOME ISSUES DETECTED")
        if not route_working:
            print(f"❌ Riders dashboard route needs attention")
        if not navigation_working:
            print(f"❌ Navigation template needs attention")
    
    return route_working and navigation_working

if __name__ == "__main__":
    main()
