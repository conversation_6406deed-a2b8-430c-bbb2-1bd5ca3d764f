#!/usr/bin/env python3
"""
Simple Riders Dashboard Rebuild
"""

import os
from datetime import datetime

def create_riders_dashboard_route():
    """Create new riders dashboard route"""
    
    print("🚀 CREATING NEW RIDERS DASHBOARD ROUTE")
    print("=" * 50)
    
    # Simple route code without complex SQL formatting
    new_route_code = '''
@app.route('/riders/dashboard')
@login_required
def riders_dashboard():
    """Professional Riders Management Dashboard"""
    try:
        db = get_db()
        
        # Get basic statistics
        total_riders = db.execute('SELECT COUNT(*) as count FROM riders').fetchone()['count']
        active_riders = db.execute('SELECT COUNT(*) as count FROM riders WHERE is_active = 1 OR is_active IS NULL').fetchone()['count']
        
        # Get riders with performance
        riders_performance = db.execute("""
            SELECT r.rider_id, r.name, r.phone, r.vehicle_type, r.rating,
                   COUNT(o.order_id) as total_deliveries,
                   COUNT(CASE WHEN o.status = 'Delivered' THEN 1 END) as completed_deliveries
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
            GROUP BY r.rider_id
            ORDER BY total_deliveries DESC
            LIMIT 10
        """).fetchall()
        
        # Get recent deliveries
        recent_deliveries = db.execute("""
            SELECT o.order_id, o.customer_name, o.status, o.order_date,
                   r.name as rider_name
            FROM orders o
            LEFT JOIN riders r ON o.rider_id = r.rider_id
            WHERE o.status IN ('Out for Delivery', 'Delivered', 'Shipped')
            ORDER BY o.order_date DESC
            LIMIT 15
        """).fetchall()
        
        # Today's stats
        today = datetime.now().date()
        today_stats = db.execute("""
            SELECT 
                COUNT(*) as total_orders,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as delivered_orders,
                COUNT(CASE WHEN status = 'Out for Delivery' THEN 1 END) as out_for_delivery
            FROM orders
            WHERE DATE(order_date) = ?
        """, (today,)).fetchone()
        
        context = {
            'title': 'Riders Management Dashboard',
            'total_riders': total_riders,
            'active_riders': active_riders,
            'riders_performance': riders_performance,
            'recent_deliveries': recent_deliveries,
            'today_stats': today_stats,
            'current_user': current_user,
            'now': datetime.now()
        }
        
        return render_template('riders/professional_dashboard.html', **context)
        
    except Exception as e:
        flash(f'Error loading riders dashboard: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

'''
    
    # Add to app.py
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find insertion point
        insertion_point = content.find('if __name__ == "__main__":')
        
        if insertion_point != -1:
            new_content = content[:insertion_point] + new_route_code + '\n\n' + content[insertion_point:]
            
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ Added new riders dashboard route")
            return True
        else:
            print("❌ Could not find insertion point")
            return False
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def create_riders_template():
    """Create riders dashboard template"""
    
    print("\n🎨 CREATING RIDERS TEMPLATE")
    print("=" * 50)
    
    os.makedirs('templates/riders', exist_ok=True)
    
    template_content = '''{% extends 'base.html' %}

{% block title %}Riders Dashboard - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-motorcycle text-primary"></i> Riders Management Dashboard
        </h1>
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Dashboard
        </a>
    </div>

    <!-- Statistics Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_riders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-users fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ active_riders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-user-check fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Today's Deliveries</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_stats.delivered_orders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Out for Delivery</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ today_stats.out_for_delivery or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Riders Performance Table -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Performing Riders</h6>
                </div>
                <div class="card-body">
                    {% if riders_performance %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Rider Name</th>
                                        <th>Phone</th>
                                        <th>Vehicle</th>
                                        <th>Total Deliveries</th>
                                        <th>Completed</th>
                                        <th>Success Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in riders_performance %}
                                    <tr>
                                        <td>{{ rider.name }}</td>
                                        <td>{{ rider.phone }}</td>
                                        <td>{{ rider.vehicle_type or 'N/A' }}</td>
                                        <td>{{ rider.total_deliveries }}</td>
                                        <td>{{ rider.completed_deliveries }}</td>
                                        <td>
                                            {% if rider.total_deliveries > 0 %}
                                                {{ "%.1f"|format((rider.completed_deliveries / rider.total_deliveries) * 100) }}%
                                            {% else %}
                                                0%
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No rider performance data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Activities -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activities</h6>
                </div>
                <div class="card-body">
                    {% if recent_deliveries %}
                        {% for delivery in recent_deliveries %}
                        <div class="d-flex align-items-center border-bottom py-2">
                            <div class="mr-3">
                                <div class="icon-circle bg-{{ 'success' if delivery.status == 'Delivered' else 'warning' }}">
                                    <i class="fas fa-{{ 'check' if delivery.status == 'Delivered' else 'truck' }} text-white"></i>
                                </div>
                            </div>
                            <div>
                                <div class="small text-gray-500">Order #{{ delivery.order_id }}</div>
                                <div class="font-weight-bold">{{ delivery.customer_name }}</div>
                                <div class="small">{{ delivery.rider_name or 'Unassigned' }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No recent activities.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-primary btn-block">
                                <i class="fas fa-chart-line"></i> Delivery Analytics
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('riders_management') }}" class="btn btn-success btn-block">
                                <i class="fas fa-users-cog"></i> Manage Riders
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.real_time_tracking') }}" class="btn btn-info btn-block">
                                <i class="fas fa-map-marker-alt"></i> Real-time Tracking
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ url_for('delivery_analytics.performance_kpis') }}" class="btn btn-warning btn-block">
                                <i class="fas fa-chart-bar"></i> Performance KPIs
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}'''
    
    try:
        with open('templates/riders/professional_dashboard.html', 'w', encoding='utf-8') as f:
            f.write(template_content)
        
        print("✅ Created riders template")
        return True
    
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_riders_dashboard():
    """Test the new riders dashboard"""
    
    print("\n🧪 TESTING RIDERS DASHBOARD")
    print("=" * 50)
    
    try:
        import requests
        
        response = requests.get("http://127.0.0.1:3000/riders/dashboard", timeout=10)
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Riders dashboard working")
            return True
        else:
            print(f"❌ Dashboard not working: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Main execution"""
    
    print("🚀 SIMPLE RIDERS DASHBOARD REBUILD")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create route
    route_created = create_riders_dashboard_route()
    
    # Create template
    template_created = create_riders_template()
    
    # Test dashboard
    dashboard_working = test_riders_dashboard()
    
    print(f"\n🎯 REBUILD SUMMARY")
    print("=" * 70)
    print(f"Route created: {'✅ YES' if route_created else '❌ NO'}")
    print(f"Template created: {'✅ YES' if template_created else '❌ NO'}")
    print(f"Dashboard working: {'✅ YES' if dashboard_working else '❌ NO'}")
    
    success = route_created and template_created and dashboard_working
    
    if success:
        print(f"\n🎉 RIDERS DASHBOARD SUCCESSFULLY REBUILT!")
    else:
        print(f"\n⚠️ Some issues occurred")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    main()
