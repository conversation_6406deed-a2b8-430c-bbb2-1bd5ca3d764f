{% extends 'base.html' %}

{% block title %}Delivery Analytics Dashboard - Medivent ERP{% endblock %}

{% block styles %}
{{ super() }}
<style>
    .analytics-card {
        border-radius: 15px;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        border: none;
    }

    .analytics-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        color: white;
        margin-bottom: 15px;
    }

    .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 5px;
        line-height: 1;
    }

    .stat-label {
        color: #7f8c8d;
        font-size: 0.9rem;
        font-weight: 500;
        margin: 0;
    }

    .chart-container {
        position: relative;
        height: 300px;
        margin: 10px 0;
    }

    .delivery-status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
    }

    .status-delivered { background: #d5f4e6; color: #27ae60; }
    .status-out-for-delivery { background: #fff3cd; color: #856404; }
    .status-dispatched { background: #d1ecf1; color: #0c5460; }
    .status-pending { background: #f8d7da; color: #721c24; }

    .rider-card {
        background: white;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 15px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        border-left: 4px solid #007bff;
    }

    .performance-meter {
        height: 8px;
        background: #e9ecef;
        border-radius: 4px;
        overflow: hidden;
    }

    .performance-fill {
        height: 100%;
        border-radius: 4px;
        transition: width 0.3s ease;
    }

    .filter-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck text-primary"></i> Delivery Analytics Dashboard
        </h1>
        <div>
            <a href="{{ url_for('delivery_analytics.real_time_tracking') }}" class="btn btn-primary shadow-sm mr-2">
                <i class="fas fa-map-marker-alt fa-sm text-white-50"></i> Live Tracking
            </a>
            <a href="{{ url_for('delivery_analytics.comprehensive_reports') }}" class="btn btn-info shadow-sm mr-2">
                <i class="fas fa-file-alt fa-sm text-white-50"></i> Reports
            </a>
            <a href="{{ url_for('dashboard') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back
            </a>
        </div>
    </div>

    <!-- Advanced Filters -->
    <div class="filter-section">
        <div class="row">
            <div class="col-md-3">
                <label for="dateRange" class="form-label">Date Range</label>
                <select class="form-control" id="dateRange">
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month" selected>This Month</option>
                    <option value="quarter">This Quarter</option>
                    <option value="custom">Custom Range</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="riderFilter" class="form-label">Rider</label>
                <select class="form-control" id="riderFilter">
                    <option value="">All Riders</option>
                    {% if riders %}
                        {% for rider in riders %}
                        <option value="{{ rider.rider_id }}">{{ rider.name }}</option>
                        {% endfor %}
                    {% endif %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="statusFilter" class="form-label">Delivery Status</label>
                <select class="form-control" id="statusFilter">
                    <option value="">All Statuses</option>
                    <option value="Delivered">Delivered</option>
                    <option value="Out for Delivery">Out for Delivery</option>
                    <option value="Dispatched">Dispatched</option>
                    <option value="Pending">Pending</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="areaFilter" class="form-label">Delivery Area</label>
                <select class="form-control" id="areaFilter">
                    <option value="">All Areas</option>
                    <option value="North">North Zone</option>
                    <option value="South">South Zone</option>
                    <option value="East">East Zone</option>
                    <option value="West">West Zone</option>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-md-12">
                <button class="btn btn-primary" onclick="applyFilters()">
                    <i class="fas fa-filter"></i> Apply Filters
                </button>
                <button class="btn btn-outline-secondary ml-2" onclick="resetFilters()">
                    <i class="fas fa-undo"></i> Reset
                </button>
                <button class="btn btn-success ml-2" onclick="exportData()">
                    <i class="fas fa-download"></i> Export Data
                </button>
            </div>
        </div>
    </div>

    <!-- Key Performance Indicators -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="analytics-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-primary mx-auto">
                        <i class="fas fa-truck"></i>
                    </div>
                    <div class="stat-value" id="totalDeliveries">{{ delivery_stats.total_deliveries or 0 }}</div>
                    <p class="stat-label">Total Deliveries</p>
                    <small class="text-muted">
                        <i class="fas fa-arrow-up text-success"></i>
                        +{{ delivery_stats.delivery_growth or 0 }}% from last month
                    </small>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="analytics-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-success mx-auto">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="stat-value" id="successRate">{{ "%.1f"|format(delivery_stats.success_rate or 0) }}%</div>
                    <p class="stat-label">Success Rate</p>
                    <small class="text-muted">
                        <i class="fas fa-arrow-up text-success"></i>
                        +{{ "%.1f"|format(delivery_stats.success_improvement or 0) }}% improvement
                    </small>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="analytics-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-warning mx-auto">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="stat-value" id="avgDeliveryTime">{{ delivery_stats.avg_delivery_time or 0 }}</div>
                    <p class="stat-label">Avg Delivery Time (hrs)</p>
                    <small class="text-muted">
                        <i class="fas fa-arrow-down text-success"></i>
                        -{{ delivery_stats.time_improvement or 0 }}% faster
                    </small>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="analytics-card">
                <div class="card-body text-center">
                    <div class="stat-icon bg-info mx-auto">
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="stat-value" id="customerSatisfaction">{{ "%.1f"|format(delivery_stats.customer_satisfaction or 4.5) }}</div>
                    <p class="stat-label">Customer Rating</p>
                    <small class="text-muted">
                        <i class="fas fa-star text-warning"></i>
                        Based on {{ delivery_stats.total_ratings or 0 }} reviews
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Analytics -->
    <div class="row">
        <!-- Delivery Trends Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Delivery Trends</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow">
                            <a class="dropdown-item" href="#" onclick="exportChart('deliveryTrends')">Export Chart</a>
                            <a class="dropdown-item" href="#" onclick="refreshChart('deliveryTrends')">Refresh Data</a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="deliveryTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Delivery Status Distribution -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Delivery Status Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="statusDistributionChart"></canvas>
                    </div>
                    <div class="mt-3">
                        {% if status_distribution %}
                            {% for status in status_distribution %}
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="delivery-status-badge status-{{ status.status|lower|replace(' ', '-') }}">
                                    {{ status.status }}
                                </span>
                                <span class="font-weight-bold">{{ status.count }}</span>
                            </div>
                            {% endfor %}
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initializeDeliveryTrendsChart();
    initializeStatusDistributionChart();

    // Auto-refresh data every 30 seconds
    setInterval(refreshDashboardData, 30000);

    console.log('Delivery Analytics Dashboard loaded successfully');
});

function initializeDeliveryTrendsChart() {
    const ctx = document.getElementById('deliveryTrendsChart').getContext('2d');

    // Sample data - replace with actual data from backend
    const deliveryTrendsData = {
        labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
        datasets: [{
            label: 'Deliveries Completed',
            data: [12, 19, 15, 25, 22, 18, 20],
            borderColor: '#007bff',
            backgroundColor: 'rgba(0, 123, 255, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }, {
            label: 'Deliveries Scheduled',
            data: [15, 22, 18, 28, 25, 21, 23],
            borderColor: '#28a745',
            backgroundColor: 'rgba(40, 167, 69, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    };

    new Chart(ctx, {
        type: 'line',
        data: deliveryTrendsData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                }
            },
            elements: {
                point: {
                    radius: 6,
                    hoverRadius: 8
                }
            }
        }
    });
}

function initializeStatusDistributionChart() {
    const ctx = document.getElementById('statusDistributionChart').getContext('2d');

    // Sample data - replace with actual data from backend
    const statusData = {
        labels: ['Delivered', 'Out for Delivery', 'Dispatched', 'Pending'],
        datasets: [{
            data: [65, 20, 10, 5],
            backgroundColor: [
                '#28a745',
                '#ffc107',
                '#17a2b8',
                '#dc3545'
            ],
            borderWidth: 0
        }]
    };

    new Chart(ctx, {
        type: 'doughnut',
        data: statusData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            cutout: '60%'
        }
    });
}

function applyFilters() {
    const dateRange = document.getElementById('dateRange').value;
    const rider = document.getElementById('riderFilter').value;
    const status = document.getElementById('statusFilter').value;
    const area = document.getElementById('areaFilter').value;

    // Show loading indicator
    showLoadingIndicator();

    // Apply filters and refresh data
    const filterParams = {
        dateRange: dateRange,
        rider: rider,
        status: status,
        area: area
    };

    // Simulate API call
    setTimeout(() => {
        hideLoadingIndicator();
        showSuccessMessage('Filters applied successfully');
        refreshDashboardData();
    }, 1000);
}

function resetFilters() {
    document.getElementById('dateRange').value = 'month';
    document.getElementById('riderFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('areaFilter').value = '';

    applyFilters();
}

function exportData() {
    showLoadingIndicator();

    // Simulate export process
    setTimeout(() => {
        hideLoadingIndicator();
        showSuccessMessage('Data exported successfully');

        // Create and download a sample CSV
        const csvContent = "data:text/csv;charset=utf-8,Date,Rider,Status,Customer,Delivery Time\n" +
            "2025-07-22,John Doe,Delivered,ABC Hospital,2.5 hrs\n" +
            "2025-07-22,Jane Smith,Out for Delivery,XYZ Pharmacy,1.2 hrs\n";

        const encodedUri = encodeURI(csvContent);
        const link = document.createElement("a");
        link.setAttribute("href", encodedUri);
        link.setAttribute("download", "delivery_analytics_" + new Date().toISOString().split('T')[0] + ".csv");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }, 1500);
}

function refreshDashboardData() {
    // Refresh KPI values
    updateKPIValues();

    // Refresh charts
    refreshChart('deliveryTrends');
    refreshChart('statusDistribution');
}

function updateKPIValues() {
    // Simulate real-time data updates
    const totalDeliveries = document.getElementById('totalDeliveries');
    const successRate = document.getElementById('successRate');
    const avgDeliveryTime = document.getElementById('avgDeliveryTime');
    const customerSatisfaction = document.getElementById('customerSatisfaction');

    if (totalDeliveries) {
        const currentValue = parseInt(totalDeliveries.textContent);
        totalDeliveries.textContent = currentValue + Math.floor(Math.random() * 3);
    }
}

function initializeMap() {
    const mapContainer = document.getElementById('riderTrackingMap');
    mapContainer.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="text-muted">Initializing live tracking...</p>
        </div>
    `;

    // Simulate map initialization
    setTimeout(() => {
        mapContainer.innerHTML = `
            <div class="text-center p-4">
                <i class="fas fa-map-marked-alt fa-3x text-success mb-3"></i>
                <p class="text-success font-weight-bold">Live tracking active</p>
                <p class="text-muted small">Real-time rider locations will be displayed here</p>
                <div class="mt-3">
                    <span class="badge badge-success mr-2">3 riders active</span>
                    <span class="badge badge-warning mr-2">2 on delivery</span>
                    <span class="badge badge-info">1 returning</span>
                </div>
            </div>
        `;
    }, 2000);
}

function refreshChart(chartType) {
    console.log('Refreshing chart:', chartType);
    // Chart refresh logic would go here
}

function exportChart(chartType) {
    showSuccessMessage('Chart exported successfully');
}

function showLoadingIndicator() {
    // Add loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.className = 'position-fixed w-100 h-100 d-flex align-items-center justify-content-center';
    overlay.style.cssText = 'top: 0; left: 0; background: rgba(255,255,255,0.8); z-index: 9999;';
    overlay.innerHTML = `
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="text-muted">Processing...</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

function hideLoadingIndicator() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

function showSuccessMessage(message) {
    // Create toast notification
    const toast = document.createElement('div');
    toast.className = 'alert alert-success alert-dismissible fade show position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
    toast.innerHTML = `
        <i class="fas fa-check-circle mr-2"></i>
        ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;

    document.body.appendChild(toast);

    // Auto-remove after 3 seconds
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 3000);
}
</script>
{% endblock %}
