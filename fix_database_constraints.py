#!/usr/bin/env python3
"""
Fix Database Foreign Key Constraints
Address the foreign key mismatch issue identified in QA testing
"""

import sqlite3
import os
from datetime import datetime

def fix_foreign_key_constraints():
    """Fix foreign key constraint violations"""
    
    print("🔧 FIXING DATABASE FOREIGN KEY CONSTRAINTS")
    print("=" * 60)
    
    if not os.path.exists('instance/medivent.db'):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check foreign key violations
        print("🔍 Checking foreign key violations...")
        cursor.execute("PRAGMA foreign_key_check")
        violations = cursor.fetchall()
        
        if not violations:
            print("✅ No foreign key violations found")
            conn.close()
            return True
        
        print(f"⚠️ Found {len(violations)} foreign key violations:")
        for violation in violations:
            print(f"   Table: {violation[0]}, Row: {violation[1]}, Parent: {violation[2]}, Key: {violation[3]}")
        
        # Fix division_permissions table foreign key issues
        print("\n🔧 Fixing division_permissions foreign key issues...")
        
        # Check if the problematic records exist
        cursor.execute("SELECT COUNT(*) FROM division_permissions WHERE user_id NOT IN (SELECT user_id FROM users)")
        orphaned_records = cursor.fetchone()[0]
        
        if orphaned_records > 0:
            print(f"   Found {orphaned_records} orphaned division_permissions records")
            
            # Option 1: Delete orphaned records
            cursor.execute("DELETE FROM division_permissions WHERE user_id NOT IN (SELECT user_id FROM users)")
            deleted_count = cursor.rowcount
            print(f"   ✅ Deleted {deleted_count} orphaned records")
        
        # Check for other common foreign key issues
        print("\n🔍 Checking other potential foreign key issues...")
        
        # Check orders table foreign keys
        cursor.execute("SELECT COUNT(*) FROM orders WHERE customer_id NOT IN (SELECT customer_id FROM customers)")
        orphaned_orders = cursor.fetchone()[0]
        if orphaned_orders > 0:
            print(f"   Found {orphaned_orders} orders with invalid customer_id")
            # Fix by setting to a default customer or deleting
            cursor.execute("DELETE FROM orders WHERE customer_id NOT IN (SELECT customer_id FROM customers)")
            print(f"   ✅ Fixed orders with invalid customer references")
        
        # Check order_items table foreign keys
        cursor.execute("SELECT COUNT(*) FROM order_items WHERE order_id NOT IN (SELECT order_id FROM orders)")
        orphaned_items = cursor.fetchone()[0]
        if orphaned_items > 0:
            print(f"   Found {orphaned_items} order items with invalid order_id")
            cursor.execute("DELETE FROM order_items WHERE order_id NOT IN (SELECT order_id FROM orders)")
            print(f"   ✅ Fixed order items with invalid order references")
        
        # Check payments table foreign keys
        cursor.execute("SELECT COUNT(*) FROM payments WHERE order_id NOT IN (SELECT order_id FROM orders) AND order_id IS NOT NULL AND order_id != 0")
        orphaned_payments = cursor.fetchone()[0]
        if orphaned_payments > 0:
            print(f"   Found {orphaned_payments} payments with invalid order_id")
            cursor.execute("UPDATE payments SET order_id = NULL WHERE order_id NOT IN (SELECT order_id FROM orders) AND order_id IS NOT NULL AND order_id != 0")
            print(f"   ✅ Fixed payments with invalid order references")
        
        # Commit all changes
        conn.commit()
        
        # Verify fixes
        print("\n✅ Verifying fixes...")
        cursor.execute("PRAGMA foreign_key_check")
        remaining_violations = cursor.fetchall()
        
        if not remaining_violations:
            print("✅ All foreign key constraints are now valid")
            success = True
        else:
            print(f"⚠️ {len(remaining_violations)} violations remain:")
            for violation in remaining_violations:
                print(f"   Table: {violation[0]}, Row: {violation[1]}, Parent: {violation[2]}, Key: {violation[3]}")
            success = False
        
        conn.close()
        return success
        
    except Exception as e:
        print(f"❌ Error fixing foreign key constraints: {e}")
        return False

def optimize_database_performance():
    """Optimize database performance after fixes"""
    
    print("\n🚀 OPTIMIZING DATABASE PERFORMANCE")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Update database statistics
        print("📊 Updating database statistics...")
        cursor.execute("ANALYZE")
        
        # Rebuild indexes
        print("🔄 Rebuilding indexes...")
        cursor.execute("REINDEX")
        
        # Vacuum database
        print("🧹 Vacuuming database...")
        cursor.execute("VACUUM")
        
        print("✅ Database optimization completed")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error optimizing database: {e}")
        return False

def verify_database_integrity():
    """Final verification of database integrity"""
    
    print("\n🔍 FINAL DATABASE INTEGRITY VERIFICATION")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Check integrity
        cursor.execute("PRAGMA integrity_check")
        integrity_result = cursor.fetchone()[0]
        
        if integrity_result == 'ok':
            print("✅ Database integrity check: PASSED")
            integrity_ok = True
        else:
            print(f"❌ Database integrity issues: {integrity_result}")
            integrity_ok = False
        
        # Check foreign keys
        cursor.execute("PRAGMA foreign_key_check")
        fk_violations = cursor.fetchall()
        
        if not fk_violations:
            print("✅ Foreign key constraints: VALID")
            fk_ok = True
        else:
            print(f"❌ Foreign key violations: {len(fk_violations)}")
            fk_ok = False
        
        # Get database statistics
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM orders")
        order_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM customers")
        customer_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM products")
        product_count = cursor.fetchone()[0]
        
        print(f"\n📊 Database Statistics:")
        print(f"   Tables: {table_count}")
        print(f"   Orders: {order_count}")
        print(f"   Customers: {customer_count}")
        print(f"   Products: {product_count}")
        
        conn.close()
        
        return integrity_ok and fk_ok
        
    except Exception as e:
        print(f"❌ Error verifying database integrity: {e}")
        return False

def main():
    """Main execution"""
    print("🚀 DATABASE CONSTRAINT FIXING")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Fix foreign key constraints
    constraints_fixed = fix_foreign_key_constraints()
    
    # Optimize database performance
    performance_optimized = optimize_database_performance()
    
    # Final verification
    integrity_verified = verify_database_integrity()
    
    print(f"\n🎯 DATABASE FIX SUMMARY")
    print("=" * 70)
    print(f"Foreign key constraints fixed: {'✅ YES' if constraints_fixed else '❌ NO'}")
    print(f"Performance optimized: {'✅ YES' if performance_optimized else '❌ NO'}")
    print(f"Integrity verified: {'✅ YES' if integrity_verified else '❌ NO'}")
    
    if constraints_fixed and performance_optimized and integrity_verified:
        print(f"\n🎉 DATABASE ISSUES RESOLVED!")
        print(f"✅ Database is now ready for production use")
    else:
        print(f"\n⚠️ Some database issues remain")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return constraints_fixed and performance_optimized and integrity_verified

if __name__ == "__main__":
    main()
