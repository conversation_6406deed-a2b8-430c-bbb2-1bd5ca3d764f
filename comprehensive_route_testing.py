#!/usr/bin/env python3
"""
Comprehensive Route Testing
Test all fixed routes and generate detailed verification report
"""

import requests
import time
from datetime import datetime
import json

class ComprehensiveRouteTester:
    """Comprehensive route testing class"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:3000"
        self.test_results = {
            'timestamp': datetime.now().isoformat(),
            'total_routes_tested': 0,
            'successful_routes': 0,
            'failed_routes': 0,
            'route_details': {},
            'summary': {}
        }
    
    def test_route(self, route_path, expected_status=200, description=""):
        """Test a single route and record results"""
        
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}{route_path}", timeout=10)
            response_time = time.time() - start_time
            
            # Determine if route is working
            is_working = False
            status_description = ""
            
            if response.status_code == 200:
                if 'login' in response.text.lower():
                    is_working = True
                    status_description = "Requires authentication (working)"
                elif 'error' in response.text.lower() or 'exception' in response.text.lower():
                    is_working = False
                    status_description = "Contains errors"
                else:
                    is_working = True
                    status_description = "Fully accessible"
            elif response.status_code == 302:
                is_working = True
                status_description = "Redirected (likely auth required)"
            elif response.status_code == 404:
                is_working = False
                status_description = "Not found"
            elif response.status_code == 500:
                is_working = False
                status_description = "Server error"
            else:
                is_working = False
                status_description = f"Unexpected status: {response.status_code}"
            
            # Record results
            self.test_results['route_details'][route_path] = {
                'status_code': response.status_code,
                'response_time': round(response_time, 3),
                'is_working': is_working,
                'status_description': status_description,
                'description': description,
                'content_length': len(response.text),
                'tested_at': datetime.now().isoformat()
            }
            
            self.test_results['total_routes_tested'] += 1
            if is_working:
                self.test_results['successful_routes'] += 1
            else:
                self.test_results['failed_routes'] += 1
            
            # Print result
            status_icon = "✅" if is_working else "❌"
            print(f"{status_icon} {route_path} ({response.status_code}) - {status_description}")
            
            return is_working
        
        except Exception as e:
            # Record error
            self.test_results['route_details'][route_path] = {
                'status_code': 'ERROR',
                'response_time': 0,
                'is_working': False,
                'status_description': f"Connection error: {str(e)}",
                'description': description,
                'content_length': 0,
                'tested_at': datetime.now().isoformat()
            }
            
            self.test_results['total_routes_tested'] += 1
            self.test_results['failed_routes'] += 1
            
            print(f"❌ {route_path} - Connection error: {str(e)}")
            return False
    
    def test_all_fixed_routes(self):
        """Test all routes that were fixed"""
        
        print("🚀 COMPREHENSIVE ROUTE TESTING")
        print("=" * 70)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Test main dashboard and core routes
        print("\n📊 CORE ROUTES")
        print("-" * 30)
        self.test_route("/", description="Home page")
        self.test_route("/dashboard", description="Main dashboard")
        self.test_route("/login", description="Login page")
        
        # Test fixed finance routes
        print("\n💰 FINANCE ROUTES (FIXED)")
        print("-" * 30)
        self.test_route("/finance/dashboard", description="Finance dashboard with fixed navigation links")
        self.test_route("/finance/comprehensive-reports", description="Comprehensive finance reports (rebuilt)")
        self.test_route("/finance/pending-invoices", description="Pending invoices management")
        self.test_route("/finance/customer-ledger", description="Customer ledger")
        self.test_route("/finance/payment-collection", description="Payment collection")
        
        # Test rebuilt riders routes
        print("\n🏍️ RIDERS ROUTES (REBUILT)")
        print("-" * 30)
        self.test_route("/riders/dashboard", description="Riders dashboard (completely rebuilt)")
        self.test_route("/riders", description="Riders management")
        self.test_route("/riders/register", description="Rider registration")
        
        # Test delivery analytics routes
        print("\n📈 DELIVERY ANALYTICS ROUTES")
        print("-" * 30)
        self.test_route("/delivery_analytics/", description="Delivery analytics dashboard")
        self.test_route("/delivery_analytics/real_time_tracking", description="Real-time tracking")
        self.test_route("/delivery_analytics/performance_kpis", description="Performance KPIs")
        self.test_route("/delivery_analytics/comprehensive_reports", description="Comprehensive delivery reports")
        
        # Test advanced payment routes
        print("\n💳 ADVANCED PAYMENT ROUTES")
        print("-" * 30)
        self.test_route("/advanced_payment/", description="Advanced payment dashboard")
        self.test_route("/advanced_payment/bulk_processing", description="Bulk payment processing")
        self.test_route("/advanced_payment/automated_matching", description="Automated payment matching")
        self.test_route("/advanced_payment/reconciliation", description="Payment reconciliation")
        self.test_route("/advanced_payment/analytics", description="Payment analytics")
        
        # Test sales analytics routes
        print("\n📊 SALES ANALYTICS ROUTES")
        print("-" * 30)
        self.test_route("/sales_analytics/", description="Sales analytics dashboard")
        self.test_route("/sales_analytics/salesperson_ledger", description="Salesperson ledger")
        self.test_route("/sales_analytics/team_performance", description="Team performance")
        self.test_route("/sales_analytics/division_ledger", description="Division ledger")
        self.test_route("/sales_analytics/division_analysis", description="Division analysis")
        
        # Test other important routes
        print("\n🔧 OTHER IMPORTANT ROUTES")
        print("-" * 30)
        self.test_route("/orders", description="Orders management")
        self.test_route("/products", description="Products management")
        self.test_route("/customers", description="Customers management")
        self.test_route("/inventory", description="Inventory management")
        self.test_route("/reports", description="Reports dashboard")
        
        # Test API endpoints
        print("\n🔌 API ENDPOINTS")
        print("-" * 30)
        self.test_route("/finance/api/stats", description="Finance API stats")
        self.test_route("/advanced_payment/api/payment_stats", description="Payment stats API")
        
    def generate_summary(self):
        """Generate test summary"""
        
        total = self.test_results['total_routes_tested']
        successful = self.test_results['successful_routes']
        failed = self.test_results['failed_routes']
        success_rate = (successful / total * 100) if total > 0 else 0
        
        self.test_results['summary'] = {
            'total_routes': total,
            'successful_routes': successful,
            'failed_routes': failed,
            'success_rate': round(success_rate, 1),
            'test_duration': 'completed',
            'overall_status': 'PASS' if success_rate >= 90 else 'FAIL'
        }
        
        print(f"\n🎯 COMPREHENSIVE TESTING SUMMARY")
        print("=" * 70)
        print(f"Total routes tested: {total}")
        print(f"Successful routes: {successful}")
        print(f"Failed routes: {failed}")
        print(f"Success rate: {success_rate:.1f}%")
        print(f"Overall status: {self.test_results['summary']['overall_status']}")
        
        if failed > 0:
            print(f"\n❌ FAILED ROUTES:")
            for route, details in self.test_results['route_details'].items():
                if not details['is_working']:
                    print(f"   • {route}: {details['status_description']}")
        
        print(f"\n✅ SUCCESSFUL ROUTES:")
        successful_count = 0
        for route, details in self.test_results['route_details'].items():
            if details['is_working']:
                successful_count += 1
                if successful_count <= 5:  # Show first 5
                    print(f"   • {route}: {details['status_description']}")
        
        if successful_count > 5:
            print(f"   ... and {successful_count - 5} more successful routes")
    
    def save_detailed_report(self):
        """Save detailed test report to file"""
        
        report_filename = f"comprehensive_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_filename, 'w') as f:
                json.dump(self.test_results, f, indent=2)
            
            print(f"\n💾 Detailed report saved: {report_filename}")
            return report_filename
        
        except Exception as e:
            print(f"❌ Error saving report: {e}")
            return None
    
    def run_comprehensive_test(self):
        """Run complete comprehensive test suite"""
        
        # Test all routes
        self.test_all_fixed_routes()
        
        # Generate summary
        self.generate_summary()
        
        # Save detailed report
        report_file = self.save_detailed_report()
        
        print(f"\n🏁 COMPREHENSIVE TESTING COMPLETED")
        print("=" * 70)
        print(f"Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return self.test_results['summary']['overall_status'] == 'PASS'

def main():
    """Main execution"""
    
    tester = ComprehensiveRouteTester()
    success = tester.run_comprehensive_test()
    
    return success

if __name__ == "__main__":
    main()
