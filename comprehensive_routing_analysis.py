#!/usr/bin/env python3
"""
Comprehensive Routing Analysis and Conflict Resolution
Deep investigation of all Flask routes and template references
"""

import os
import re
from datetime import datetime
import json

class RoutingAnalyzer:
    """Comprehensive routing analyzer for Flask application"""
    
    def __init__(self):
        self.analysis_results = {
            'route_functions': {},
            'template_references': {},
            'conflicts': [],
            'missing_routes': [],
            'duplicate_routes': [],
            'recommendations': []
        }
    
    def analyze_all_route_functions(self):
        """Analyze all route functions in the application"""
        
        print("🔍 ANALYZING ALL ROUTE FUNCTIONS")
        print("=" * 50)
        
        # Files to analyze
        files_to_analyze = ['app.py']
        
        # Add route files
        if os.path.exists('routes'):
            for file in os.listdir('routes'):
                if file.endswith('.py') and not file.startswith('__'):
                    files_to_analyze.append(f'routes/{file}')
        
        all_functions = {}
        rider_related_functions = {}
        dashboard_functions = {}
        
        for file_path in files_to_analyze:
            try:
                print(f"\n📄 Analyzing: {file_path}")
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all route decorators and their functions
                route_pattern = r'@(?:app|[a-zA-Z_]+_bp)\.route\([\'"]([^\'"]+)[\'"]\)[^@]*?def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
                matches = re.findall(route_pattern, content, re.DOTALL)
                
                file_functions = {}
                for route_path, function_name in matches:
                    file_functions[function_name] = {
                        'route_path': route_path,
                        'file': file_path
                    }
                    
                    # Track rider-related functions
                    if 'rider' in function_name.lower():
                        rider_related_functions[function_name] = {
                            'route_path': route_path,
                            'file': file_path
                        }
                    
                    # Track dashboard functions
                    if 'dashboard' in function_name.lower():
                        dashboard_functions[function_name] = {
                            'route_path': route_path,
                            'file': file_path
                        }
                
                all_functions.update(file_functions)
                self.analysis_results['route_functions'][file_path] = file_functions
                
                print(f"   Found {len(file_functions)} route functions")
                if rider_related_functions:
                    print(f"   Rider-related functions: {list(rider_related_functions.keys())}")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {file_path}: {e}")
        
        print(f"\n📊 SUMMARY:")
        print(f"   Total route functions: {len(all_functions)}")
        print(f"   Rider-related functions: {len(rider_related_functions)}")
        print(f"   Dashboard functions: {len(dashboard_functions)}")
        
        # Store for conflict analysis
        self.all_functions = all_functions
        self.rider_functions = rider_related_functions
        self.dashboard_functions = dashboard_functions
        
        return all_functions
    
    def analyze_template_references(self):
        """Analyze all url_for references in templates"""
        
        print("\n🎨 ANALYZING TEMPLATE REFERENCES")
        print("=" * 50)
        
        template_files = []
        
        # Find all HTML files
        if os.path.exists('templates'):
            for root, dirs, files in os.walk('templates'):
                for file in files:
                    if file.endswith('.html'):
                        template_files.append(os.path.join(root, file))
        
        all_url_for_calls = {}
        problematic_calls = []
        
        for template_file in template_files:
            try:
                print(f"\n📄 Analyzing: {template_file}")
                
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all url_for calls
                url_for_pattern = r"url_for\(['\"]([^'\"]+)['\"]"
                matches = re.findall(url_for_pattern, content)
                
                file_references = {}
                for match in matches:
                    if match not in file_references:
                        file_references[match] = 0
                    file_references[match] += 1
                
                all_url_for_calls[template_file] = file_references
                self.analysis_results['template_references'][template_file] = file_references
                
                print(f"   Found {len(file_references)} unique url_for calls")
                
                # Check for problematic calls
                for call in file_references:
                    if call not in self.all_functions:
                        problematic_calls.append({
                            'template': template_file,
                            'call': call,
                            'count': file_references[call]
                        })
                        print(f"   ⚠️ Missing route: {call}")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {template_file}: {e}")
        
        self.problematic_calls = problematic_calls
        return all_url_for_calls
    
    def identify_conflicts_and_duplicates(self):
        """Identify routing conflicts and duplicates"""
        
        print("\n⚠️ IDENTIFYING CONFLICTS AND DUPLICATES")
        print("=" * 50)
        
        # Check for duplicate function names
        function_counts = {}
        for file_path, functions in self.analysis_results['route_functions'].items():
            for func_name in functions:
                if func_name not in function_counts:
                    function_counts[func_name] = []
                function_counts[func_name].append(file_path)
        
        duplicates = {name: files for name, files in function_counts.items() if len(files) > 1}
        
        if duplicates:
            print("🔍 DUPLICATE FUNCTION NAMES FOUND:")
            for func_name, files in duplicates.items():
                print(f"   ❌ {func_name} found in: {files}")
                self.analysis_results['duplicate_routes'].append({
                    'function': func_name,
                    'files': files
                })
        
        # Check for similar route paths
        route_paths = {}
        for file_path, functions in self.analysis_results['route_functions'].items():
            for func_name, func_info in functions.items():
                route_path = func_info['route_path']
                if route_path not in route_paths:
                    route_paths[route_path] = []
                route_paths[route_path].append((func_name, file_path))
        
        path_duplicates = {path: funcs for path, funcs in route_paths.items() if len(funcs) > 1}
        
        if path_duplicates:
            print("\n🔍 DUPLICATE ROUTE PATHS FOUND:")
            for route_path, functions in path_duplicates.items():
                print(f"   ❌ {route_path} used by: {functions}")
        
        # Check for missing routes referenced in templates
        missing_routes = []
        for call_info in self.problematic_calls:
            missing_routes.append(call_info['call'])
        
        self.analysis_results['missing_routes'] = list(set(missing_routes))
        
        if missing_routes:
            print(f"\n🔍 MISSING ROUTES REFERENCED IN TEMPLATES:")
            for route in set(missing_routes):
                print(f"   ❌ {route}")
        
        return duplicates, path_duplicates, missing_routes
    
    def generate_fix_recommendations(self):
        """Generate specific fix recommendations"""
        
        print("\n💡 GENERATING FIX RECOMMENDATIONS")
        print("=" * 50)
        
        recommendations = []
        
        # Handle duplicate functions
        for duplicate in self.analysis_results['duplicate_routes']:
            func_name = duplicate['function']
            files = duplicate['files']
            
            if func_name in ['rider_dashboard', 'riders_dashboard']:
                recommendations.append({
                    'type': 'remove_duplicate',
                    'action': f'Remove duplicate {func_name} functions, keep only one',
                    'details': f'Found in files: {files}',
                    'priority': 'HIGH'
                })
        
        # Handle missing routes
        for missing_route in self.analysis_results['missing_routes']:
            if 'rider' in missing_route and 'dashboard' in missing_route:
                recommendations.append({
                    'type': 'fix_template_reference',
                    'action': f'Update template references from {missing_route} to correct function name',
                    'priority': 'HIGH'
                })
        
        # Specific fix for the reported error
        recommendations.append({
            'type': 'critical_fix',
            'action': 'Fix riders_dashboard vs rider_dashboard conflict',
            'details': 'Ensure templates reference the correct function name',
            'priority': 'CRITICAL'
        })
        
        self.analysis_results['recommendations'] = recommendations
        
        print("📋 RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. [{rec['priority']}] {rec['action']}")
            if 'details' in rec:
                print(f"      Details: {rec['details']}")
        
        return recommendations
    
    def save_analysis_report(self):
        """Save comprehensive analysis report"""
        
        report_file = f"routing_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w') as f:
                json.dump(self.analysis_results, f, indent=2)
            
            print(f"\n💾 Analysis report saved: {report_file}")
            return report_file
        
        except Exception as e:
            print(f"❌ Error saving report: {e}")
            return None
    
    def run_comprehensive_analysis(self):
        """Run complete routing analysis"""
        
        print("🚀 COMPREHENSIVE ROUTING ANALYSIS")
        print("=" * 70)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run analysis steps
        self.analyze_all_route_functions()
        self.analyze_template_references()
        duplicates, path_duplicates, missing_routes = self.identify_conflicts_and_duplicates()
        recommendations = self.generate_fix_recommendations()
        report_file = self.save_analysis_report()
        
        print(f"\n🎯 ANALYSIS SUMMARY")
        print("=" * 70)
        print(f"Total route functions: {len(self.all_functions)}")
        print(f"Duplicate functions: {len(duplicates)}")
        print(f"Missing routes: {len(missing_routes)}")
        print(f"Fix recommendations: {len(recommendations)}")
        
        critical_issues = len([r for r in recommendations if r['priority'] == 'CRITICAL'])
        high_issues = len([r for r in recommendations if r['priority'] == 'HIGH'])
        
        print(f"Critical issues: {critical_issues}")
        print(f"High priority issues: {high_issues}")
        
        if critical_issues > 0 or high_issues > 0:
            print(f"\n⚠️ IMMEDIATE ACTION REQUIRED")
            print(f"Critical routing conflicts must be resolved")
        else:
            print(f"\n✅ NO CRITICAL ROUTING ISSUES FOUND")
        
        print(f"🏁 Analysis completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return self.analysis_results

def main():
    """Main execution"""
    analyzer = RoutingAnalyzer()
    results = analyzer.run_comprehensive_analysis()
    return results

if __name__ == "__main__":
    main()
