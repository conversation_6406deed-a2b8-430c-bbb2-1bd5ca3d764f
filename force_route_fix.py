#!/usr/bin/env python3
"""
Force Route Fix - Definitive solution for BuildError
"""

import os
import sys
import subprocess
import time

def check_flask_process():
    """Check if Flask is running and get process info"""
    
    print("🔍 CHECKING FLASK PROCESS")
    print("=" * 50)
    
    try:
        # Check for Python processes running Flask
        result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                              capture_output=True, text=True, shell=True)
        
        if 'python.exe' in result.stdout:
            print("✅ Python processes found running")
            print("📋 Active Python processes:")
            lines = result.stdout.split('\n')
            for line in lines:
                if 'python.exe' in line:
                    print(f"   {line.strip()}")
        else:
            print("❌ No Python processes found")
        
        return 'python.exe' in result.stdout
    
    except Exception as e:
        print(f"❌ Error checking processes: {e}")
        return False

def verify_route_function():
    """Verify the riders_dashboard function exists and is correct"""
    
    print("\n🔍 VERIFYING ROUTE FUNCTION")
    print("=" * 50)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the exact function
        if 'def riders_dashboard():' in content:
            print("✅ riders_dashboard() function found in app.py")
            
            # Check for the route decorator
            if "@app.route('/riders/dashboard')" in content:
                print("✅ @app.route('/riders/dashboard') decorator found")
                
                # Check if function is complete
                function_start = content.find('def riders_dashboard():')
                if function_start != -1:
                    # Find the next function or end of file
                    next_function = content.find('\ndef ', function_start + 1)
                    if next_function == -1:
                        next_function = len(content)
                    
                    function_content = content[function_start:next_function]
                    
                    if 'return render_template' in function_content:
                        print("✅ Function has return statement")
                        print("✅ riders_dashboard function is complete and correct")
                        return True
                    else:
                        print("❌ Function missing return statement")
                        return False
            else:
                print("❌ Route decorator not found")
                return False
        else:
            print("❌ riders_dashboard function not found")
            return False
    
    except Exception as e:
        print(f"❌ Error verifying function: {e}")
        return False

def verify_template_reference():
    """Verify template reference is correct"""
    
    print("\n🔍 VERIFYING TEMPLATE REFERENCE")
    print("=" * 50)
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find line 659
        lines = content.split('\n')
        if len(lines) > 658:
            line_659 = lines[658]  # 0-indexed
            print(f"Line 659: {line_659.strip()}")
            
            if "url_for('riders_dashboard')" in line_659:
                print("✅ Template correctly references 'riders_dashboard'")
                return True
            else:
                print("❌ Template reference incorrect")
                return False
        else:
            print("❌ Template file too short")
            return False
    
    except Exception as e:
        print(f"❌ Error verifying template: {e}")
        return False

def create_minimal_test_route():
    """Create a minimal test to verify Flask routing works"""
    
    print("\n🧪 CREATING MINIMAL TEST")
    print("=" * 50)
    
    test_content = '''
import sys
import os
sys.path.insert(0, os.getcwd())

from flask import Flask, url_for
app = Flask(__name__)

@app.route('/riders/dashboard')
def riders_dashboard():
    return "Test route works"

@app.route('/test')
def test_url_generation():
    try:
        url = url_for('riders_dashboard')
        return f"URL generation successful: {url}"
    except Exception as e:
        return f"URL generation failed: {str(e)}"

if __name__ == "__main__":
    with app.app_context():
        try:
            url = url_for('riders_dashboard')
            print(f"✅ URL generation works: {url}")
        except Exception as e:
            print(f"❌ URL generation failed: {e}")
'''
    
    try:
        with open('test_route.py', 'w') as f:
            f.write(test_content)
        
        print("✅ Test file created: test_route.py")
        
        # Run the test
        result = subprocess.run([sys.executable, 'test_route.py'], 
                              capture_output=True, text=True, cwd=os.getcwd())
        
        print("📋 Test output:")
        print(result.stdout)
        if result.stderr:
            print("❌ Test errors:")
            print(result.stderr)
        
        # Clean up
        if os.path.exists('test_route.py'):
            os.remove('test_route.py')
        
        return 'URL generation works' in result.stdout
    
    except Exception as e:
        print(f"❌ Error running test: {e}")
        return False

def provide_restart_instructions():
    """Provide clear restart instructions"""
    
    print("\n🔄 FLASK SERVER RESTART INSTRUCTIONS")
    print("=" * 50)
    
    print("The BuildError persists because Flask is running with cached route registry.")
    print("Follow these steps to resolve:")
    print()
    print("1. 🛑 STOP the current Flask server:")
    print("   - Press Ctrl+C in the terminal running Flask")
    print("   - Or close the terminal/command prompt")
    print("   - Wait 5 seconds for complete shutdown")
    print()
    print("2. 🧹 CLEAR Python cache (optional but recommended):")
    print("   - Delete __pycache__ folders:")
    print("     rmdir /s __pycache__")
    print("     rmdir /s routes\\__pycache__")
    print()
    print("3. 🚀 RESTART Flask server:")
    print("   - Open new terminal/command prompt")
    print("   - Navigate to project directory")
    print("   - Run: python app.py")
    print()
    print("4. ✅ VERIFY the fix:")
    print("   - Wait for 'Running on http://127.0.0.1:3000'")
    print("   - Open browser to http://127.0.0.1:3000/dashboard")
    print("   - BuildError should be completely resolved")
    print()
    print("🎯 CRITICAL: The route exists and is correct. The issue is Flask caching.")

def main():
    """Main execution for definitive BuildError fix"""
    
    print("🚀 DEFINITIVE BUILDERROR RESOLUTION")
    print("=" * 70)
    print("Analyzing and providing complete solution...")
    
    # Check current state
    flask_running = check_flask_process()
    route_correct = verify_route_function()
    template_correct = verify_template_reference()
    routing_works = create_minimal_test_route()
    
    print(f"\n🎯 ANALYSIS SUMMARY")
    print("=" * 70)
    print(f"Flask process running: {'✅ YES' if flask_running else '❌ NO'}")
    print(f"Route function correct: {'✅ YES' if route_correct else '❌ NO'}")
    print(f"Template reference correct: {'✅ YES' if template_correct else '❌ NO'}")
    print(f"URL generation works: {'✅ YES' if routing_works else '❌ NO'}")
    
    if route_correct and template_correct and routing_works:
        print(f"\n🎉 DIAGNOSIS: FLASK SERVER RESTART REQUIRED")
        print(f"✅ All code is correct - the issue is Flask caching")
        print(f"✅ Route function exists and is properly defined")
        print(f"✅ Template reference is correct")
        print(f"✅ URL generation works in isolation")
        print(f"⚠️ Flask server is running with old route registry")
        
        provide_restart_instructions()
        
        print(f"\n💡 ALTERNATIVE QUICK FIX:")
        print(f"If restart doesn't work, the issue might be:")
        print(f"1. Multiple Flask instances running")
        print(f"2. Import conflicts in route registration")
        print(f"3. Blueprint registration issues")
        
        return True
    else:
        print(f"\n❌ CODE ISSUES DETECTED")
        print(f"Fix the following issues before restarting:")
        if not route_correct:
            print(f"   - Fix riders_dashboard function in app.py")
        if not template_correct:
            print(f"   - Fix template reference in base.html")
        if not routing_works:
            print(f"   - Fix URL generation issues")
        
        return False

if __name__ == "__main__":
    main()
