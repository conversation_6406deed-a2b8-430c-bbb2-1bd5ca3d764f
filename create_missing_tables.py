#!/usr/bin/env python3
"""
Create Missing Database Tables for API Functionality
"""

import sqlite3
from datetime import datetime

def create_missing_tables():
    """Create missing database tables for API functionality"""
    
    # Connect to database
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    print("🗄️ Creating missing database tables...")
    
    try:
        # Create stock_movements table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS stock_movements (
                movement_id TEXT PRIMARY KEY,
                inventory_id TEXT NOT NULL,
                movement_type TEXT NOT NULL CHECK (movement_type IN ('in', 'out', 'transfer', 'adjustment')),
                quantity_change INTEGER NOT NULL,
                previous_quantity INTEGER NOT NULL,
                new_quantity INTEGER NOT NULL,
                reason TEXT NOT NULL,
                reference_number TEXT,
                created_by TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (inventory_id) REFERENCES inventory (inventory_id)
            )
        ''')
        print("✅ Created stock_movements table")
        
        # Create warehouses table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS warehouses (
                warehouse_id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                location TEXT,
                manager TEXT,
                capacity INTEGER,
                status TEXT DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        print("✅ Created warehouses table")
        
        # Check if warehouses table has data and insert default if needed
        try:
            cursor.execute('SELECT COUNT(*) FROM warehouses')
            if cursor.fetchone()[0] == 0:
                # Check table structure first
                cursor.execute("PRAGMA table_info(warehouses)")
                columns = [column[1] for column in cursor.fetchall()]

                if 'location' in columns:
                    cursor.execute('''
                        INSERT INTO warehouses (warehouse_id, name, location, manager, capacity, status)
                        VALUES ('WH001', 'Main Warehouse', 'Central Location', 'System Admin', 10000, 'active')
                    ''')
                else:
                    cursor.execute('''
                        INSERT INTO warehouses (warehouse_id, name)
                        VALUES ('WH001', 'Main Warehouse')
                    ''')
                print("✅ Inserted default warehouse")
        except sqlite3.Error as e:
            print(f"⚠️ Warehouse insertion issue: {e}")
        
        # Create activity_logs table if it doesn't exist
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT NOT NULL,
                action TEXT NOT NULL,
                details TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                ip_address TEXT
            )
        ''')
        print("✅ Created activity_logs table")
        
        # Update inventory table to include warehouse_id if missing
        cursor.execute("PRAGMA table_info(inventory)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'warehouse_id' not in columns:
            cursor.execute('ALTER TABLE inventory ADD COLUMN warehouse_id TEXT DEFAULT "WH001"')
            print("✅ Added warehouse_id to inventory table")
        
        if 'last_updated' not in columns:
            cursor.execute('ALTER TABLE inventory ADD COLUMN last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            print("✅ Added last_updated to inventory table")
        
        # Update customers table to include additional fields if missing
        cursor.execute("PRAGMA table_info(customers)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'credit_limit' not in columns:
            cursor.execute('ALTER TABLE customers ADD COLUMN credit_limit REAL DEFAULT 0')
            print("✅ Added credit_limit to customers table")
        
        if 'outstanding_balance' not in columns:
            cursor.execute('ALTER TABLE customers ADD COLUMN outstanding_balance REAL DEFAULT 0')
            print("✅ Added outstanding_balance to customers table")
        
        if 'created_at' not in columns:
            cursor.execute('ALTER TABLE customers ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            print("✅ Added created_at to customers table")
        
        if 'updated_at' not in columns:
            cursor.execute('ALTER TABLE customers ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            print("✅ Added updated_at to customers table")
        
        # Update users table to include additional fields if missing
        cursor.execute("PRAGMA table_info(users)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'last_login' not in columns:
            cursor.execute('ALTER TABLE users ADD COLUMN last_login TIMESTAMP')
            print("✅ Added last_login to users table")
        
        if 'created_at' not in columns:
            cursor.execute('ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            print("✅ Added created_at to users table")
        
        if 'updated_at' not in columns:
            cursor.execute('ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP')
            print("✅ Added updated_at to users table")
        
        # Create indexes for better performance
        indexes = [
            ('idx_stock_movements_inventory', 'stock_movements', 'inventory_id'),
            ('idx_stock_movements_created', 'stock_movements', 'created_at'),
            ('idx_customers_email', 'customers', 'email'),
            ('idx_customers_phone', 'customers', 'phone'),
            ('idx_customers_city', 'customers', 'city'),
            ('idx_users_username', 'users', 'username'),
            ('idx_users_email', 'users', 'email'),
            ('idx_users_role', 'users', 'role'),
            ('idx_activity_logs_username', 'activity_logs', 'username'),
            ('idx_activity_logs_timestamp', 'activity_logs', 'timestamp'),
            ('idx_inventory_warehouse', 'inventory', 'warehouse_id'),
            ('idx_inventory_product', 'inventory', 'product_id')
        ]
        
        for index_name, table_name, column_name in indexes:
            try:
                cursor.execute(f'CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({column_name})')
                print(f"✅ Created index {index_name}")
            except sqlite3.Error as e:
                print(f"⚠️ Index {index_name} already exists or error: {e}")
        
        # Commit all changes
        conn.commit()
        print("\n✅ All database tables and indexes created successfully!")
        
        # Display table summary
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        print(f"\n📊 Database now contains {len(tables)} tables:")
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"   • {table[0]}: {count} records")
        
        return True
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def test_api_tables():
    """Test that all required tables exist and have correct structure"""
    print("\n🧪 Testing API table structure...")
    
    conn = sqlite3.connect('instance/medivent.db')
    cursor = conn.cursor()
    
    required_tables = [
        'customers', 'inventory', 'products', 'orders', 'users',
        'warehouses', 'stock_movements', 'activity_logs'
    ]
    
    all_good = True
    
    for table in required_tables:
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"✅ {table}: {count} records")
        except sqlite3.Error as e:
            print(f"❌ {table}: Error - {e}")
            all_good = False
    
    conn.close()
    
    if all_good:
        print("✅ All required tables are present and accessible!")
    else:
        print("❌ Some tables are missing or have issues!")
    
    return all_good

if __name__ == "__main__":
    print("🚀 CREATING MISSING DATABASE TABLES FOR API FUNCTIONALITY")
    print("=" * 70)
    
    success = create_missing_tables()
    
    if success:
        test_api_tables()
        print("\n🎉 Database setup completed successfully!")
        print("📡 API endpoints are now ready to use!")
    else:
        print("\n❌ Database setup failed!")
        print("🔧 Please check the error messages above and try again.")
