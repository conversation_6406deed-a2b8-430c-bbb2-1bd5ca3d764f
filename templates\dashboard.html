{% extends 'base.html' %}

{% block title %}Dashboard - Medivent Pharmaceuticals ERP{% endblock %}

{% block styles %}
{{ super() }}
<style>
    /* Order workflow progress bar styling */
    .progress-bar {
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        transition: all 0.3s ease;
        cursor: pointer;
        color: white;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        position: relative;
        overflow: hidden;
        font-size: 0.9rem;
        border-right: 1px solid rgba(255, 255, 255, 0.3);
    }

    .progress-bar:last-child {
        border-right: none;
    }

    .progress-bar:hover {
        opacity: 0.9;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        z-index: 10;
    }

    .progress-bar:active {
        transform: translateY(1px);
    }

    .progress {
        overflow: visible;
        height: 60px !important;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        background-color: #f8f9fa;
        padding: 0;
        border: 1px solid #dee2e6;
        display: flex;
        flex-wrap: nowrap;
    }

    /* Status count styling */
    .status-count {
        display: block;
        font-size: 0.8rem;
        margin-top: 3px;
        opacity: 0.9;
    }

    /* Status label styling */
    .status-label {
        display: block;
        font-weight: bold;
    }

    /* Fix for status display */
    .progress-bar {
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 5px 0;
        min-width: 100px; /* Ensure minimum width for readability */
        height: 60px;
        line-height: 1.2;
    }

    /* Make sure text is centered and visible */
    .progress-bar .status-label {
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 2px;
        color: white;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    }

    .progress-bar .status-count {
        font-size: 12px;
        text-align: center;
        color: white;
        text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    }

    /* Add tooltip-like effect on hover */
    .progress-bar:after {
        content: "View Orders";
        position: absolute;
        bottom: -30px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s;
        pointer-events: none;
        white-space: nowrap;
    }

    .progress-bar:hover:after {
        opacity: 1;
    }

    /* Custom colors for better visibility */
    .bg-placed {
        background-color: #ffc107;
        border: 1px solid #e0a800;
    }
    .bg-approved {
        background-color: #17a2b8;
        border: 1px solid #138496;
    }
    .bg-processing {
        background-color: #007bff;
        border: 1px solid #0069d9;
    }
    .bg-ready {
        background-color: #6c757d;
        border: 1px solid #5a6268;
    }
    .bg-dispatched {
        background-color: #343a40;
        border: 1px solid #23272b;
    }
    .bg-delivered {
        background-color: #28a745;
        border: 1px solid #218838;
    }
    .bg-cancelled {
        background-color: #dc3545;
        border: 1px solid #c82333;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title">Welcome, {{ current_user.full_name or current_user.username }}</h2>
                    <p class="card-text">Welcome to Medivent Pharmaceuticals ERP System. Here's an overview of your business.</p>
                    {% if not has_dashboard_permission %}
                    <div class="alert alert-danger mt-3">
                        Access denied. You do not have permission to access this resource.
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    {% if has_dashboard_permission %}
    <!-- Stats Cards -->
    <div class="row mb-4">
        {% if has_orders_widget %}
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Total Orders</h6>
                            <h2 class="mb-0" id="orders-count">{{ orders_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-shopping-cart fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{{ url_for('orders') }}" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Pending Approvals</h6>
                            <h2 class="mb-0" id="pending-approvals">{{ pending_approvals|default(0) }}</h2>
                        </div>
                        <i class="fas fa-clock fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="{{ url_for('workflow', status='Placed') }}" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>
        {% endif %}

        {% if has_inventory_widget %}
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Products</h6>
                            <h2 class="mb-0" id="products-count">{{ products_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-pills fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="#" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title">Low Stock Items</h6>
                            <h2 class="mb-0" id="low-stock-count">{{ low_stock_count|default(0) }}</h2>
                        </div>
                        <i class="fas fa-exclamation-triangle fa-3x opacity-50"></i>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-0">
                    <a href="#" class="text-white">View Details <i class="fas fa-arrow-right ml-1"></i></a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Analytics and Management Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3 text-gray-800">
                <i class="fas fa-chart-line text-primary"></i> Analytics & Advanced Management
            </h4>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Delivery Analytics
                            </div>
                            <div class="text-gray-900 small">Real-time tracking, performance KPIs, and comprehensive delivery reports</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-chart-line"></i> Open Dashboard
                        </a>
                        <a href="{{ url_for('delivery_analytics.real_time_tracking') }}" class="btn btn-outline-primary btn-sm ml-1">
                            <i class="fas fa-map-marker-alt"></i> Live Tracking
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Advanced Payment Management
                            </div>
                            <div class="text-gray-900 small">Bulk processing, automated matching, reconciliation, and payment analytics</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('advanced_payment.dashboard') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-dollar-sign"></i> Payment Hub
                        </a>
                        <a href="{{ url_for('advanced_payment.bulk_processing') }}" class="btn btn-outline-success btn-sm ml-1">
                            <i class="fas fa-layer-group"></i> Bulk Process
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Sales Analytics
                            </div>
                            <div class="text-gray-900 small">Team performance analysis, division insights, and salesperson ledgers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('sales_analytics.dashboard') }}" class="btn btn-info btn-sm">
                            <i class="fas fa-users"></i> Sales Dashboard
                        </a>
                        <a href="{{ url_for('sales_analytics.team_performance') }}" class="btn btn-outline-info btn-sm ml-1">
                            <i class="fas fa-trophy"></i> Performance
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Finance Management -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="mb-3 text-gray-800">
                <i class="fas fa-calculator text-success"></i> Enhanced Finance Management
            </h4>
        </div>

        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Finance Dashboard
                            </div>
                            <div class="text-gray-900 small">Comprehensive financial overview, payment collection, and customer ledgers</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-pie fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('finance_dashboard') }}" class="btn btn-warning btn-sm">
                            <i class="fas fa-tachometer-alt"></i> Finance Dashboard
                        </a>
                        <a href="{{ url_for('comprehensive_finance_reports') }}" class="btn btn-outline-warning btn-sm ml-1">
                            <i class="fas fa-file-alt"></i> Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                Riders Management
                            </div>
                            <div class="text-gray-900 small">Professional riders dashboard with performance tracking and delivery management</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-motorcycle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ url_for('riders_dashboard') }}" class="btn btn-danger btn-sm">
                            <i class="fas fa-motorcycle"></i> Riders Dashboard
                        </a>
                        <a href="{{ url_for('riders') }}" class="btn btn-outline-danger btn-sm ml-1">
                            <i class="fas fa-users-cog"></i> Manage
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {% if has_workflow_widget %}
    <!-- Order Workflow Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Order Workflow</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="progress">
                                <a href="{{ url_for('workflow', status='Placed') }}" class="progress-bar bg-placed" role="progressbar"
                                   style="width: {{ status_percentages.get('Placed', 0)|default(14)|round(1) }}%; text-decoration: none;"
                                   aria-valuenow="{{ status_counts.get('Placed', 0)|default(0) }}" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Placed</span>
                                    <span class="status-count">({{ status_counts.get('Placed', 0)|default(0) }})</span>
                                </a>
                                <a href="{{ url_for('workflow', status='Approved') }}" class="progress-bar bg-approved" role="progressbar"
                                   style="width: {{ status_percentages.get('Approved', 0)|default(14)|round(1) }}%; text-decoration: none;"
                                   aria-valuenow="{{ status_counts.get('Approved', 0)|default(0) }}" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Approved</span>
                                    <span class="status-count">({{ status_counts.get('Approved', 0)|default(0) }})</span>
                                </a>
                                <a href="{{ url_for('workflow', status='Processing') }}" class="progress-bar bg-processing" role="progressbar"
                                   style="width: {{ status_percentages.get('Processing', 0)|default(14)|round(1) }}%; text-decoration: none;"
                                   aria-valuenow="{{ status_counts.get('Processing', 0)|default(0) }}" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Processing</span>
                                    <span class="status-count">({{ status_counts.get('Processing', 0)|default(0) }})</span>
                                </a>
                                <a href="{{ url_for('workflow', status='Ready for Pickup') }}" class="progress-bar bg-ready" role="progressbar"
                                   style="width: {{ status_percentages.get('Ready for Pickup', 0)|default(14)|round(1) }}%; text-decoration: none;"
                                   aria-valuenow="{{ status_counts.get('Ready for Pickup', 0)|default(0) }}" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Ready</span>
                                    <span class="status-count">({{ status_counts.get('Ready for Pickup', 0)|default(0) }})</span>
                                </a>
                                <a href="{{ url_for('workflow', status='Dispatched') }}" class="progress-bar bg-dispatched" role="progressbar"
                                   style="width: {{ status_percentages.get('Dispatched', 0)|default(14)|round(1) }}%; text-decoration: none;"
                                   aria-valuenow="{{ status_counts.get('Dispatched', 0)|default(0) }}" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Dispatched</span>
                                    <span class="status-count">({{ status_counts.get('Dispatched', 0)|default(0) }})</span>
                                </a>
                                <a href="{{ url_for('workflow', status='Delivered') }}" class="progress-bar bg-delivered" role="progressbar"
                                   style="width: {{ status_percentages.get('Delivered', 0)|default(14)|round(1) }}%; text-decoration: none;"
                                   aria-valuenow="{{ status_counts.get('Delivered', 0)|default(0) }}" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Delivered</span>
                                    <span class="status-count">({{ status_counts.get('Delivered', 0)|default(0) }})</span>
                                </a>
                                <a href="{{ url_for('workflow', status='Cancelled') }}" class="progress-bar bg-cancelled" role="progressbar"
                                   style="width: {{ status_percentages.get('Cancelled', 0)|default(14)|round(1) }}%; text-decoration: none;"
                                   aria-valuenow="{{ status_counts.get('Cancelled', 0)|default(0) }}" aria-valuemin="0" aria-valuemax="100">
                                    <span class="status-label">Cancelled</span>
                                    <span class="status-count">({{ status_counts.get('Cancelled', 0)|default(0) }})</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12 text-center">
                            <a href="{{ url_for('workflow') }}" class="btn btn-primary">Manage Order Workflow</a>
                            <a href="{{ url_for('orders') }}" class="btn btn-outline-primary ml-2">View All Orders</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('new_order') }}" class="btn btn-outline-primary btn-block py-3">
                                <i class="fas fa-plus-circle mb-2 fa-2x"></i><br>
                                Place New Order
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('workflow', status='Placed') }}" class="btn btn-outline-warning btn-block py-3">
                                <i class="fas fa-check-circle mb-2 fa-2x"></i><br>
                                Approve Orders
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('workflow', status='Approved') }}" class="btn btn-outline-info btn-block py-3">
                                <i class="fas fa-truck mb-2 fa-2x"></i><br>
                                Dispatch Orders
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('workflow', status='Dispatched') }}" class="btn btn-outline-success btn-block py-3">
                                <i class="fas fa-box-open mb-2 fa-2x"></i><br>
                                Deliver Orders
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- Executive Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-gradient-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="totalRevenueCard">Rs.0</h4>
                            <p class="mb-0">Total Revenue</p>
                            <small><i class="fas fa-info-circle"></i> No revenue data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="totalOrdersCard">0</h4>
                            <p class="mb-0">Total Orders</p>
                            <small><i class="fas fa-info-circle"></i> No orders data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shopping-cart fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="avgOrderValueCard">Rs.0</h4>
                            <p class="mb-0">Avg Order Value</p>
                            <small><i class="fas fa-info-circle"></i> No order value data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calculator fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-gradient-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0" id="activeCustomersCard">0</h4>
                            <p class="mb-0">Active Customers</p>
                            <small><i class="fas fa-info-circle"></i> No customer data available</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Analytics Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="mb-0">🏥 Medivent Pharmaceuticals - Advanced Business Intelligence</h5>
                    <small>Real-time analytics - No data available (empty database)</small>
                </div>
                <div class="card-body">
                    <!-- Analytics Navigation Tabs -->
                    <ul class="nav nav-tabs nav-tabs-custom" id="analyticsTab" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="executive-tab" data-toggle="tab" href="#executive-dashboard" role="tab">
                                <i class="fas fa-crown"></i> Executive Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="sales-tab" data-toggle="tab" href="#sales-analytics" role="tab">
                                <i class="fas fa-chart-line"></i> Sales Analytics
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="division-tab" data-toggle="tab" href="#division-analytics" role="tab">
                                <i class="fas fa-building"></i> Division Performance
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="inventory-tab" data-toggle="tab" href="#inventory-analytics" role="tab">
                                <i class="fas fa-boxes"></i> Inventory Intelligence
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="financial-tab" data-toggle="tab" href="#financial-analytics" role="tab">
                                <i class="fas fa-dollar-sign"></i> Financial Intelligence
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" id="operational-tab" data-toggle="tab" href="#operational-analytics" role="tab">
                                <i class="fas fa-cogs"></i> Operations
                            </a>
                        </li>
                    </ul>

                    <div class="tab-content mt-3" id="analyticsTabContent">
                        <!-- Executive Overview Tab -->
                        <div class="tab-pane fade show active" id="executive-dashboard" role="tabpanel">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📈 Revenue Trend Analysis - No Data Available</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveRevenueChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🏢 Division Revenue Share</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveDivisionChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">👥 Top Sales Agents Performance</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveAgentsChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Order Status Distribution (0 Orders)</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="executiveStatusChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🎯 Key Performance Indicators</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-primary text-white rounded">
                                                        <h4 class="mb-1" id="kpiRevenue">Rs.0</h4>
                                                        <small>Total Revenue</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineRevenue" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-success text-white rounded">
                                                        <h4 class="mb-1" id="kpiOrders">0</h4>
                                                        <small>Total Orders</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineOrders" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-warning text-white rounded">
                                                        <h4 class="mb-1" id="kpiProducts">0</h4>
                                                        <small>Active Products</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineProducts" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-info text-white rounded">
                                                        <h4 class="mb-1" id="kpiCustomers">0</h4>
                                                        <small>Customers</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineCustomers" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-danger text-white rounded">
                                                        <h4 class="mb-1" id="kpiInventory">0</h4>
                                                        <small>Inventory Items</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineInventory" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2">
                                                    <div class="text-center p-3 bg-dark text-white rounded">
                                                        <h4 class="mb-1" id="kpiDivisions">0</h4>
                                                        <small>Divisions</small>
                                                        <div class="mt-2">
                                                            <canvas id="sparklineDivisions" width="80" height="30"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sales Analytics Tab -->
                        <div class="tab-pane fade" id="sales-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📈 Monthly Sales Trend</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="monthlySalesChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🎯 Sales by Agent</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="salesAgentChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Order Status Distribution</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="orderStatusChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📅 Daily Order Volume</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="dailyOrderChart" height="250"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Division Performance Tab -->
                        <div class="tab-pane fade" id="division-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">🏢 Revenue by Division</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="divisionRevenueChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📦 Orders by Division</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="divisionOrdersChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📈 Division Performance Comparison</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="divisionComparisonChart" height="400"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Inventory Analytics Tab -->
                        <div class="tab-pane fade" id="inventory-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📦 Stock Levels by Warehouse</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="warehouseStockChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">⚠️ Expiry Analysis</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="expiryAnalysisChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Top Products by Stock Movement</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="stockMovementChart" height="400"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Financial Analytics Tab -->
                        <div class="tab-pane fade" id="financial-analytics" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">💰 Revenue Growth Trend</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="revenueGrowthChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">💳 Payment Methods</h6>
                                        </div>
                                        <div class="card-body">
                                            <canvas id="paymentMethodChart" height="300"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="mb-0">📊 Financial KPIs Overview</h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-primary" id="totalRevenue">Rs.0</h4>
                                                        <small>Total Revenue</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-success" id="avgOrderValue">Rs.0</h4>
                                                        <small>Avg Order Value</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-info" id="monthlyGrowth">+12.5%</h4>
                                                        <small>Monthly Growth</small>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="text-center p-3 bg-light rounded">
                                                        <h4 class="text-warning" id="activeCustomers">344</h4>
                                                        <small>Active Customers</small>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Real-time Metrics Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">⚡ Real-time Business Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-primary mb-1" id="todayOrders">0</h3>
                                <small class="text-muted">Today's Orders</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-success mb-1" id="todayRevenue">Rs.0</h3>
                                <small class="text-muted">Today's Revenue</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-warning mb-1" id="pendingOrders">0</h3>
                                <small class="text-muted">Pending Orders</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-info mb-1" id="lowStockItems">0</h3>
                                <small class="text-muted">Low Stock Items</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-danger mb-1" id="expiringItems">0</h3>
                                <small class="text-muted">Expiring Soon</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center p-3 border rounded">
                                <h3 class="text-dark mb-1" id="activeUsers">1</h3>
                                <small class="text-muted">Active Users</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Chart.js Library -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<!-- Custom Dashboard Styles -->
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.nav-tabs-custom .nav-link {
    border: none;
    border-radius: 25px;
    margin-right: 10px;
    background: #f8f9fa;
    color: #495057;
    transition: all 0.3s ease;
}

.nav-tabs-custom .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.nav-tabs-custom .nav-link:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: none;
    padding: 1.25rem;
}

.opacity-75 {
    opacity: 0.75;
}

.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.metric-card {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    border-left: 4px solid #667eea;
}

.metric-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.chart-container {
    position: relative;
    height: 300px;
    margin: 10px 0;
}

.kpi-sparkline {
    height: 30px;
    width: 80px;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.dashboard-title {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2rem;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 0.875rem;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}
</style>

<script>
// Enhanced Dashboard Charts with Real Data
document.addEventListener('DOMContentLoaded', function() {
    // Chart.js default configuration
    Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
    Chart.defaults.font.size = 12;
    Chart.defaults.color = '#666';

    // Color schemes
    const colors = {
        primary: '#007bff',
        success: '#28a745',
        warning: '#ffc107',
        danger: '#dc3545',
        info: '#17a2b8',
        light: '#f8f9fa',
        dark: '#343a40'
    };

    const divisionColors = [
        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
    ];

    // Get real data from backend
    const analyticsData = {{ analytics_data|tojson }};

    // Executive Dashboard Charts

    // Executive Revenue Trend Chart
    const executiveRevenueCtx = document.getElementById('executiveRevenueChart');
    if (executiveRevenueCtx && analyticsData.monthly_sales) {
        const monthlyData = analyticsData.monthly_sales;
        const labels = monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
        });
        const revenues = monthlyData.map(item => (item.revenue / 1000000).toFixed(1));
        const orders = monthlyData.map(item => item.order_count);

        new Chart(executiveRevenueCtx, {
            type: 'line',
            data: {
                labels: labels.length > 0 ? labels : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Revenue (Rs. Millions)',
                    data: revenues.length > 0 ? revenues : [95, 105, 115, 98, 125, 135, 142, 138, 155, 148, 162, 175],
                    borderColor: colors.primary,
                    backgroundColor: colors.primary + '20',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 10,
                    yAxisID: 'y'
                }, {
                    label: 'Orders',
                    data: orders.length > 0 ? orders : [450, 520, 380, 480, 550, 280, 150, 420, 380, 520, 480, 650],
                    borderColor: colors.success,
                    backgroundColor: colors.success + '20',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 4,
                    pointHoverRadius: 8,
                    yAxisID: 'y1'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                if (context.datasetIndex === 0) {
                                    return 'Revenue: Rs.' + (context.parsed.y * 1000000).toLocaleString();
                                } else {
                                    return 'Orders: ' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        beginAtZero: true,
                        grid: {
                            color: '#e9ecef'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rs.' + value + 'M';
                            }
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        beginAtZero: true,
                        grid: {
                            drawOnChartArea: false,
                        },
                        ticks: {
                            callback: function(value) {
                                return value + ' orders';
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: '#e9ecef'
                        }
                    }
                }
            }
        });
    }

    // Executive Division Chart
    const executiveDivisionCtx = document.getElementById('executiveDivisionChart');
    if (executiveDivisionCtx && analyticsData.division_performance) {
        const divisionData = analyticsData.division_performance.slice(0, 8); // Top 8 divisions
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionRevenues = divisionData.map(item => (item.revenue / 1000000).toFixed(1));

        // Only render chart if we have real data
        if (divisionLabels.length > 0 && divisionRevenues.length > 0) {
            new Chart(executiveDivisionCtx, {
                type: 'doughnut',
                data: {
                    labels: divisionLabels,
                    datasets: [{
                        data: divisionRevenues,
                        backgroundColor: [
                            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                        ],
                        borderWidth: 3,
                        borderColor: '#fff',
                        hoverBorderWidth: 5
                    }]
                },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': Rs.' + (value * 1000000).toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            });
        } else {
            // Show empty state message
            executiveDivisionCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><br>No division data available</div>';
        }
    }

    // Executive Agents Chart
    const executiveAgentsCtx = document.getElementById('executiveAgentsChart');
    if (executiveAgentsCtx && analyticsData.sales_by_agent) {
        const agentData = analyticsData.sales_by_agent.slice(0, 10); // Top 10 agents
        const agentLabels = agentData.map(item => item.sales_agent || 'Unknown');
        const agentSales = agentData.map(item => (item.total_sales / 1000000).toFixed(1));

        new Chart(executiveAgentsCtx, {
            type: 'bar',
            data: {
                labels: agentLabels.length > 0 ? agentLabels : [],
                datasets: [{
                    label: 'Sales (Rs. Millions)',
                    data: agentSales.length > 0 ? agentSales : [],
                    backgroundColor: colors.primary + '80',
                    borderColor: colors.primary,
                    borderWidth: 1,
                    borderRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                indexAxis: 'y',
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Sales: Rs.' + (context.parsed.x * 1000000).toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        grid: {
                            color: '#e9ecef'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rs.' + value + 'M';
                            }
                        }
                    },
                    y: {
                        grid: {
                            color: '#e9ecef'
                        }
                    }
                }
            }
        });
    }

    // Executive Status Chart
    const executiveStatusCtx = document.getElementById('executiveStatusChart');
    if (executiveStatusCtx) {
        // Use real status data from backend
        const statusData = analyticsData.status_counts || [];
        const statusLabels = ['Delivered', 'Dispatched', 'Processing', 'Approved', 'Placed', 'Cancelled'];

        // Only render chart if we have real data
        if (statusData.length > 0) {
            new Chart(executiveStatusCtx, {
                type: 'pie',
                data: {
                    labels: statusLabels,
                    datasets: [{
                        data: statusData,
                    backgroundColor: [
                        colors.success,
                        colors.dark,
                        colors.primary,
                        colors.info,
                        colors.warning,
                        colors.danger
                    ],
                    borderWidth: 3,
                    borderColor: '#fff',
                    hoverBorderWidth: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': ' + value.toLocaleString() + ' orders (' + percentage + '%)';
                            }
                        }
                    }
                }
            });
        } else {
            // Show empty state message
            executiveStatusCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><br>No order status data available</div>';
        }
    }

    // Sparkline Charts for KPIs
    function createSparkline(canvasId, data, color) {
        const ctx = document.getElementById(canvasId);
        if (ctx) {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['', '', '', '', '', '', ''],
                    datasets: [{
                        data: data,
                        borderColor: color,
                        backgroundColor: color + '20',
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: false,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: {
                        point: { radius: 0 }
                    }
                }
            });
        }
    }

    // Create sparklines
    createSparkline('sparklineRevenue', [95, 105, 115, 98, 125, 135, 142], '#fff');
    createSparkline('sparklineOrders', [450, 520, 380, 480, 550, 280, 650], '#fff');
    createSparkline('sparklineProducts', [65, 66, 67, 67, 67, 67, 67], '#fff');
    createSparkline('sparklineCustomers', [320, 325, 330, 335, 340, 345, 347], '#fff');
    createSparkline('sparklineInventory', [220, 225, 228, 230, 232, 232, 232], '#fff');
    createSparkline('sparklineDivisions', [6, 7, 7, 8, 8, 8, 8], '#fff');

    // Monthly Sales Trend Chart with Real Data
    const monthlySalesCtx = document.getElementById('monthlySalesChart');
    if (monthlySalesCtx && analyticsData.monthly_sales) {
        const monthlyData = analyticsData.monthly_sales;
        const labels = monthlyData.map(item => {
            const date = new Date(item.month + '-01');
            return date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
        });
        const revenues = monthlyData.map(item => (item.revenue / 1000000).toFixed(1)); // Convert to millions

        new Chart(monthlySalesCtx, {
            type: 'line',
            data: {
                labels: labels.length > 0 ? labels : ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                datasets: [{
                    label: 'Revenue (Rs. Millions)',
                    data: revenues.length > 0 ? revenues : [95, 105, 115, 98, 125, 135],
                    borderColor: colors.primary,
                    backgroundColor: colors.primary + '20',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return 'Revenue: Rs.' + (context.parsed.y * 1000000).toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: '#e9ecef'
                        },
                        ticks: {
                            callback: function(value) {
                                return 'Rs.' + value + 'M';
                            }
                        }
                    },
                    x: {
                        grid: {
                            color: '#e9ecef'
                        }
                    }
                }
            }
        });
    }

    // Sales by Agent Chart with Real Data
    const salesAgentCtx = document.getElementById('salesAgentChart');
    if (salesAgentCtx && analyticsData.sales_by_agent) {
        const agentData = analyticsData.sales_by_agent.slice(0, 5); // Top 5 agents
        const agentLabels = agentData.map(item => item.sales_agent || 'Unknown');
        const agentSales = agentData.map(item => (item.total_sales / 1000000).toFixed(1)); // Convert to millions

        // Only render chart if we have real data
        if (agentLabels.length > 0 && agentSales.length > 0) {
            new Chart(salesAgentCtx, {
                type: 'doughnut',
                data: {
                    labels: agentLabels,
                    datasets: [{
                        data: agentSales,
                        backgroundColor: divisionColors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return context.label + ': Rs.' + (value * 1000000).toLocaleString() + ' (' + percentage + '%)';
                            }
                        }
                    }
                });
        } else if (salesAgentCtx) {
            // Show empty state message
            salesAgentCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-user-tie fa-3x mb-3"></i><br>No sales agent data available</div>';
        }
    }

    // Order Status Distribution Chart - Only show if we have real data
    const orderStatusCtx = document.getElementById('orderStatusChart');
    if (orderStatusCtx && analyticsData.status_counts && analyticsData.status_counts.length > 0) {
        const statusLabels = ['Delivered', 'Dispatched', 'Processing', 'Approved', 'Placed', 'Cancelled'];

        new Chart(orderStatusCtx, {
            type: 'pie',
            data: {
                labels: statusLabels,
                datasets: [{
                    data: analyticsData.status_counts,
                    backgroundColor: [
                        colors.success,
                        colors.dark,
                        colors.primary,
                        colors.info,
                        colors.warning,
                        colors.danger
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            fontSize: 10
                        }
                    }
                }
            });
    } else if (orderStatusCtx) {
        // Show empty state message
        orderStatusCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-3x mb-3"></i><br>No order status data available</div>';
    }

    // Daily Order Volume Chart - Only show if we have real data
    const dailyOrderCtx = document.getElementById('dailyOrderChart');
    if (dailyOrderCtx && analyticsData.daily_orders && analyticsData.daily_orders.length > 0) {
        const dailyLabels = analyticsData.daily_orders.map(item => item.day || 'Unknown');
        const dailyData = analyticsData.daily_orders.map(item => item.order_count || 0);

        new Chart(dailyOrderCtx, {
            type: 'bar',
            data: {
                labels: dailyLabels,
                datasets: [{
                    label: 'Orders',
                    data: dailyData,
                    backgroundColor: colors.primary + '80',
                    borderColor: colors.primary,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            });
    } else if (dailyOrderCtx) {
        // Show empty state message
        dailyOrderCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-bar fa-3x mb-3"></i><br>No daily order data available</div>';
    }

    // Division Revenue Chart with Real Data
    const divisionRevenueCtx = document.getElementById('divisionRevenueChart');
    if (divisionRevenueCtx && analyticsData.division_performance) {
        const divisionData = analyticsData.division_performance;
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionRevenues = divisionData.map(item => (item.revenue / 1000000).toFixed(1)); // Convert to millions

        // Only render chart if we have real data
        if (divisionLabels.length > 0 && divisionRevenues.length > 0) {
            new Chart(divisionRevenueCtx, {
                type: 'polarArea',
                data: {
                    labels: divisionLabels,
                    datasets: [{
                        data: divisionRevenues,
                        backgroundColor: divisionColors.map(color => color + '80'),
                        borderColor: divisionColors,
                        borderWidth: 2
                    }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': Rs.' + (context.parsed * 1000000).toLocaleString();
                            }
                        }
                    }
                }
            });
        } else {
            // Show empty state message
            divisionRevenueCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-area fa-3x mb-3"></i><br>No division revenue data available</div>';
        }
    }

    // Division Orders Chart - Only show if we have real data
    const divisionOrdersCtx = document.getElementById('divisionOrdersChart');
    if (divisionOrdersCtx && analyticsData.division_performance && analyticsData.division_performance.length > 0) {
        const divisionData = analyticsData.division_performance;
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionOrders = divisionData.map(item => item.order_count || 0);

        new Chart(divisionOrdersCtx, {
            type: 'radar',
            data: {
                labels: divisionLabels,
                datasets: [{
                    label: 'Orders',
                    data: divisionOrders,
                    backgroundColor: colors.primary + '20',
                    borderColor: colors.primary,
                    borderWidth: 2,
                    pointBackgroundColor: colors.primary,
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: colors.primary
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true
                    }
                }
            });
    } else if (divisionOrdersCtx) {
        // Show empty state message
        divisionOrdersCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-radar fa-3x mb-3"></i><br>No division order data available</div>';
    }

    // Division Comparison Chart - Only show if we have real data
    const divisionComparisonCtx = document.getElementById('divisionComparisonChart');
    if (divisionComparisonCtx && analyticsData.division_performance && analyticsData.division_performance.length > 0) {
        const divisionData = analyticsData.division_performance;
        const divisionLabels = divisionData.map(item => item.division || 'Unknown');
        const divisionRevenues = divisionData.map(item => (item.revenue / 1000000).toFixed(1));
        const divisionOrders = divisionData.map(item => Math.round(item.order_count / 100));

        new Chart(divisionComparisonCtx, {
            type: 'bar',
            data: {
                labels: divisionLabels,
                datasets: [{
                    label: 'Revenue (Rs. Millions)',
                    data: divisionRevenues,
                    backgroundColor: divisionColors.map(color => color + '80'),
                    borderColor: divisionColors,
                    borderWidth: 1
                }, {
                    label: 'Orders (Hundreds)',
                    data: divisionOrders,
                    backgroundColor: colors.success + '80',
                    borderColor: colors.success,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            });
    } else if (divisionComparisonCtx) {
        // Show empty state message
        divisionComparisonCtx.parentElement.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-bar fa-3x mb-3"></i><br>No division comparison data available</div>';
    }

    // Warehouse Stock Chart
    const warehouseStockCtx = document.getElementById('warehouseStockChart');
    if (warehouseStockCtx) {
        new Chart(warehouseStockCtx, {
            type: 'bar',
            data: {
                labels: ['Karachi WH', 'Lahore WH'],
                datasets: [{
                    label: 'Stock Quantity',
                    data: [15420, 8935],
                    backgroundColor: [colors.primary + '80', colors.success + '80'],
                    borderColor: [colors.primary, colors.success],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Expiry Analysis Chart
    const expiryAnalysisCtx = document.getElementById('expiryAnalysisChart');
    if (expiryAnalysisCtx) {
        new Chart(expiryAnalysisCtx, {
            type: 'doughnut',
            data: {
                labels: ['Valid (>6 months)', 'Expiring Soon (3-6 months)', 'Critical (<3 months)', 'Expired'],
                datasets: [{
                    data: [75, 15, 8, 2],
                    backgroundColor: [colors.success, colors.warning, colors.danger, '#6c757d'],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Stock Movement Chart
    const stockMovementCtx = document.getElementById('stockMovementChart');
    if (stockMovementCtx) {
        new Chart(stockMovementCtx, {
            type: 'line',
            data: {
                labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                datasets: [{
                    label: 'Stock In',
                    data: [1200, 1500, 1100, 1800],
                    borderColor: colors.success,
                    backgroundColor: colors.success + '20',
                    fill: false,
                    tension: 0.4
                }, {
                    label: 'Stock Out',
                    data: [800, 1200, 950, 1400],
                    borderColor: colors.danger,
                    backgroundColor: colors.danger + '20',
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // Revenue Growth Chart
    const revenueGrowthCtx = document.getElementById('revenueGrowthChart');
    if (revenueGrowthCtx) {
        new Chart(revenueGrowthCtx, {
            type: 'line',
            data: {
                labels: ['Q1 2024', 'Q2 2024', 'Q3 2024', 'Q4 2024', 'Q1 2025'],
                datasets: [{
                    label: 'Revenue Growth %',
                    data: [8.5, 12.3, 15.7, 18.2, 22.1],
                    borderColor: colors.success,
                    backgroundColor: colors.success + '20',
                    fill: true,
                    tension: 0.4,
                    pointRadius: 6,
                    pointHoverRadius: 10
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    }
                }
            }
        });
    }

    // Payment Methods Chart
    const paymentMethodCtx = document.getElementById('paymentMethodChart');
    if (paymentMethodCtx) {
        new Chart(paymentMethodCtx, {
            type: 'doughnut',
            data: {
                labels: ['Cash', 'Credit', 'Bank Transfer', 'Cheque'],
                datasets: [{
                    data: [45, 30, 20, 5],
                    backgroundColor: [colors.success, colors.primary, colors.info, colors.warning],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // Real-time metrics update using API
    function updateRealTimeMetrics() {
        fetch('/api/dashboard/analytics')
            .then(response => response.json())
            .then(data => {
                // Update real-time metrics with actual data
                document.getElementById('todayOrders').textContent = data.today_orders || 0;
                document.getElementById('todayRevenue').textContent = data.today_revenue || 'Rs.0';
                document.getElementById('pendingOrders').textContent = data.pending_orders || 0;
                document.getElementById('lowStockItems').textContent = data.low_stock_items || 0;
                document.getElementById('expiringItems').textContent = data.expiring_items || 0;
                document.getElementById('activeUsers').textContent = data.active_users || 1;
            })
            .catch(error => {
                console.log('Error fetching real-time data:', error);
                // Fallback to default values
                document.getElementById('todayOrders').textContent = '0';
                document.getElementById('todayRevenue').textContent = 'Rs.0';
                document.getElementById('pendingOrders').textContent = '0';
                document.getElementById('lowStockItems').textContent = '0';
                document.getElementById('expiringItems').textContent = '0';
                document.getElementById('activeUsers').textContent = '1';
            });
    }

    // Initial load and update every 30 seconds
    updateRealTimeMetrics();
    setInterval(updateRealTimeMetrics, 30000);

    // Update financial KPIs with real data
    if (analyticsData.division_performance && analyticsData.division_performance.length > 0) {
        const totalRevenue = analyticsData.division_performance.reduce((sum, div) => sum + div.revenue, 0);
        const totalOrders = analyticsData.division_performance.reduce((sum, div) => sum + div.order_count, 0);
        const avgOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;

        document.getElementById('totalRevenue').textContent = 'Rs.' + (totalRevenue / 1000000).toFixed(1) + 'M';
        document.getElementById('avgOrderValue').textContent = 'Rs.' + avgOrderValue.toLocaleString();
    }
});
</script>
{% endblock %}
