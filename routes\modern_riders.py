"""
Modern Rider Management Blueprint 2025
Enhanced rider dashboard and tracking with modern architecture
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from utils.db import get_db
import json
from datetime import datetime, date, timedelta
import sqlite3

# Create blueprint
riders_bp = Blueprint('riders', __name__, url_prefix='/riders')

@riders_bp.route('/')
@riders_bp.route('/dashboard')
@login_required
def dashboard():
    """Modern Rider Dashboard with Enhanced Analytics"""
    try:
        db = get_db()
        
        # Get rider overview statistics
        rider_stats = db.execute('''
            SELECT 
                COUNT(*) as total_riders,
                COUNT(CASE WHEN status = 'active' THEN 1 END) as active_riders,
                COUNT(CASE WHEN is_available = 1 THEN 1 END) as available_riders,
                AVG(rating) as avg_rating,
                SUM(total_deliveries) as total_deliveries,
                SUM(successful_deliveries) as successful_deliveries
            FROM riders
        ''').fetchone()
        
        # Get top performing riders
        top_riders = db.execute('''
            SELECT rider_id, name, rating, total_deliveries, successful_deliveries,
                   performance_stats, status, city, vehicle_type
            FROM riders 
            WHERE status = 'active'
            ORDER BY rating DESC, successful_deliveries DESC
            LIMIT 10
        ''').fetchall()
        
        # Parse performance stats for top riders
        enhanced_riders = []
        for rider in top_riders:
            rider_dict = dict(rider)
            if rider_dict['performance_stats']:
                try:
                    rider_dict['performance_data'] = json.loads(rider_dict['performance_stats'])
                except:
                    rider_dict['performance_data'] = {}
            else:
                rider_dict['performance_data'] = {}
            enhanced_riders.append(rider_dict)
        
        # Get recent performance logs
        recent_performance = db.execute('''
            SELECT rpl.*, r.name as rider_name
            FROM rider_performance_logs rpl
            JOIN riders r ON rpl.rider_id = r.rider_id
            ORDER BY rpl.date DESC
            LIMIT 20
        ''').fetchall()
        
        # Get city-wise distribution
        city_distribution = db.execute('''
            SELECT city, COUNT(*) as rider_count,
                   AVG(rating) as avg_city_rating,
                   SUM(total_deliveries) as city_deliveries
            FROM riders
            WHERE status = 'active'
            GROUP BY city
            ORDER BY rider_count DESC
        ''').fetchall()
        
        return render_template('riders/modern_dashboard.html',
                             rider_stats=rider_stats,
                             top_riders=enhanced_riders,
                             recent_performance=recent_performance,
                             city_distribution=city_distribution,
                             current_date=datetime.now())
        
    except Exception as e:
        print(f"Rider dashboard error: {e}")
        flash(f'Error loading rider dashboard: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@riders_bp.route('/tracking')
@login_required
def tracking():
    """Advanced Rider Tracking with Real-time Updates"""
    try:
        db = get_db()
        
        # Get all riders with their current status and location
        riders_status = db.execute('''
            SELECT r.*,
                   COUNT(o.order_id) as active_orders,
                   GROUP_CONCAT(o.order_id) as order_ids,
                   o.customer_address as current_delivery_address
            FROM riders r
            LEFT JOIN orders o ON r.rider_id = o.rider_id
                AND o.status IN ('Dispatched', 'Out for Delivery')
            GROUP BY r.rider_id
            ORDER BY r.is_available DESC, r.status, r.name
        ''').fetchall()
        
        # Enhanced riders with performance stats
        enhanced_riders = []
        for rider in riders_status:
            rider_dict = dict(rider)
            
            # Parse performance stats
            if rider_dict['performance_stats']:
                try:
                    performance_data = json.loads(rider_dict['performance_stats'])
                    rider_dict['performance_data'] = performance_data
                except:
                    rider_dict['performance_data'] = {
                        'avg_delivery_time': 0,
                        'on_time_percentage': 0,
                        'customer_satisfaction': 0,
                        'fuel_efficiency': 0,
                        'monthly_earnings': 0
                    }
            else:
                rider_dict['performance_data'] = {
                    'avg_delivery_time': 25,
                    'on_time_percentage': 95.0,
                    'customer_satisfaction': rider_dict.get('rating', 0),
                    'fuel_efficiency': 35,
                    'monthly_earnings': 40000
                }
            
            enhanced_riders.append(rider_dict)
        
        # Get delivery statistics for today
        today_stats = db.execute('''
            SELECT
                COUNT(*) as total_deliveries_today,
                COUNT(CASE WHEN status = 'Delivered' THEN 1 END) as completed_today,
                COUNT(CASE WHEN status IN ('Dispatched', 'Out for Delivery') THEN 1 END) as pending_today,
                COUNT(CASE WHEN status = 'Failed' THEN 1 END) as failed_today
            FROM orders
            WHERE DATE(order_date) = DATE('now')
            AND rider_id IS NOT NULL
        ''').fetchone()
        
        # Get performance summary
        performance_summary = {
            'total_active_riders': len([r for r in enhanced_riders if r['status'] == 'active']),
            'available_riders': len([r for r in enhanced_riders if r['is_available']]),
            'riders_on_delivery': len([r for r in enhanced_riders if r['active_orders'] > 0]),
            'avg_rating': sum(r['rating'] for r in enhanced_riders) / len(enhanced_riders) if enhanced_riders else 0
        }
        
        return render_template('riders/modern_tracking.html',
                             riders_status=enhanced_riders,
                             today_stats=today_stats,
                             performance_summary=performance_summary,
                             now=datetime.now())
        
    except Exception as e:
        print(f"Rider tracking error: {e}")
        flash(f'Error loading rider tracking: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))

@riders_bp.route('/api/rider/<rider_id>/performance')
@login_required
def api_rider_performance(rider_id):
    """API endpoint for rider performance data"""
    try:
        db = get_db()
        
        # Get rider basic info
        rider = db.execute('''
            SELECT rider_id, name, rating, total_deliveries, successful_deliveries,
                   performance_stats, status, city
            FROM riders 
            WHERE rider_id = ?
        ''', (rider_id,)).fetchone()
        
        if not rider:
            return jsonify({'error': 'Rider not found'}), 404
        
        # Get performance logs for last 30 days
        performance_logs = db.execute('''
            SELECT date, deliveries_completed, deliveries_failed, 
                   average_delivery_time_minutes, customer_ratings_avg,
                   earnings, total_distance_km
            FROM rider_performance_logs
            WHERE rider_id = ? AND date >= date('now', '-30 days')
            ORDER BY date DESC
        ''', (rider_id,)).fetchall()
        
        # Parse performance stats
        performance_data = {}
        if rider['performance_stats']:
            try:
                performance_data = json.loads(rider['performance_stats'])
            except:
                pass
        
        return jsonify({
            'rider': dict(rider),
            'performance_data': performance_data,
            'performance_logs': [dict(log) for log in performance_logs],
            'summary': {
                'total_logs': len(performance_logs),
                'avg_daily_deliveries': sum(log['deliveries_completed'] for log in performance_logs) / len(performance_logs) if performance_logs else 0,
                'success_rate': (rider['successful_deliveries'] / rider['total_deliveries'] * 100) if rider['total_deliveries'] > 0 else 0
            }
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@riders_bp.route('/api/update_location', methods=['POST'])
@login_required
def api_update_location():
    """API endpoint to update rider location"""
    try:
        data = request.get_json()
        rider_id = data.get('rider_id')
        location = data.get('location')
        is_available = data.get('is_available', True)
        
        if not rider_id or not location:
            return jsonify({'error': 'Rider ID and location required'}), 400
        
        db = get_db()
        
        # Update rider location and availability
        db.execute('''
            UPDATE riders 
            SET current_location = ?, 
                is_available = ?,
                last_location_update = CURRENT_TIMESTAMP,
                updated_at = CURRENT_TIMESTAMP
            WHERE rider_id = ?
        ''', (location, is_available, rider_id))
        
        db.commit()
        
        return jsonify({
            'success': True,
            'message': 'Location updated successfully',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@riders_bp.route('/analytics')
@login_required
def analytics():
    """Advanced Rider Analytics Dashboard"""
    try:
        db = get_db()
        
        # Get comprehensive analytics data
        analytics_data = {
            'performance_trends': [],
            'city_comparison': [],
            'rating_distribution': [],
            'delivery_efficiency': []
        }
        
        # Performance trends (last 30 days)
        trends = db.execute('''
            SELECT date, 
                   SUM(deliveries_completed) as total_deliveries,
                   AVG(customer_ratings_avg) as avg_rating,
                   AVG(average_delivery_time_minutes) as avg_time,
                   SUM(earnings) as total_earnings
            FROM rider_performance_logs
            WHERE date >= date('now', '-30 days')
            GROUP BY date
            ORDER BY date
        ''').fetchall()
        
        analytics_data['performance_trends'] = [dict(trend) for trend in trends]
        
        # City comparison
        city_stats = db.execute('''
            SELECT COALESCE(r.city, "Not specified"),
                   COUNT(r.rider_id) as rider_count,
                   AVG(COALESCE(r.rating, 4.0)) as avg_rating,
                   SUM(r.total_deliveries) as total_deliveries,
                   AVG(rpl.earnings) as avg_earnings
            FROM riders r
            LEFT JOIN rider_performance_logs rpl ON r.rider_id = rpl.rider_id
            WHERE r.status = 'active'
            GROUP BY COALESCE(r.city, "Not specified")
            ORDER BY rider_count DESC
        ''').fetchall()
        
        analytics_data['city_comparison'] = [dict(city) for city in city_stats]
        
        # Rating distribution
        rating_dist = db.execute('''
            SELECT 
                CASE 
                    WHEN rating >= 4.5 THEN '4.5-5.0'
                    WHEN rating >= 4.0 THEN '4.0-4.4'
                    WHEN rating >= 3.5 THEN '3.5-3.9'
                    WHEN rating >= 3.0 THEN '3.0-3.4'
                    ELSE 'Below 3.0'
                END as rating_range,
                COUNT(*) as rider_count
            FROM riders
            WHERE status = 'active'
            GROUP BY rating_range
            ORDER BY rating_range DESC
        ''').fetchall()
        
        analytics_data['rating_distribution'] = [dict(rating) for rating in rating_dist]
        
        return render_template('riders/modern_analytics.html',
                             analytics_data=analytics_data,
                             current_date=datetime.now())
        
    except Exception as e:
        print(f"Rider analytics error: {e}")
        flash(f'Error loading rider analytics: {str(e)}', 'danger')
        return redirect(url_for('riders.dashboard'))

@riders_bp.route('/api/rider/<rider_id>/performance')
@login_required
def rider_performance_api(rider_id):
    """API endpoint for rider performance data (used by tracking page)"""
    try:
        db = get_db()

        # Get rider basic information
        rider = db.execute('''
            SELECT rider_id, name, email, phone, city, status, rating,
                   total_deliveries, successful_deliveries, vehicle_type,
                   license_number, current_location, created_at
            FROM riders
            WHERE rider_id = ?
        ''', (rider_id,)).fetchone()

        if not rider:
            return jsonify({
                'error': 'Rider not found',
                'rider': {},
                'summary': {}
            }), 404

        # Convert to dict for safe access
        rider_dict = dict(rider)

        # Get performance statistics
        performance_stats = db.execute('''
            SELECT
                COUNT(*) as total_orders,
                SUM(CASE WHEN status = 'Delivered' THEN 1 ELSE 0 END) as delivered_orders,
                AVG(CASE WHEN delivery_rating IS NOT NULL THEN delivery_rating ELSE 0 END) as avg_rating,
                SUM(order_amount) as total_revenue,
                COUNT(CASE WHEN DATE(order_date) = DATE('now') THEN 1 END) as today_deliveries
            FROM orders
            WHERE rider_id = ?
        ''', (rider_id,)).fetchone()

        if performance_stats:
            stats_dict = dict(performance_stats)
            total_orders = stats_dict.get('total_orders', 0) or 0
            delivered_orders = stats_dict.get('delivered_orders', 0) or 0
            success_rate = (delivered_orders / total_orders * 100) if total_orders > 0 else 0
            avg_daily_deliveries = delivered_orders / 30 if delivered_orders > 0 else 0  # Rough estimate
        else:
            success_rate = 0
            avg_daily_deliveries = 0
            total_orders = 0

        # Get recent performance logs count
        logs_count = db.execute('''
            SELECT COUNT(*) as log_count
            FROM orders
            WHERE rider_id = ? AND order_date >= date('now', '-30 days')
        ''', (rider_id,)).fetchone()

        total_logs = logs_count['log_count'] if logs_count else 0

        # Prepare response data
        response_data = {
            'rider': {
                'rider_id': rider_dict.get('rider_id', ''),
                'name': rider_dict.get('name', ''),
                'city': rider_dict.get('city', ''),
                'rating': rider_dict.get('rating', 0.0) or 0.0,
                'total_deliveries': rider_dict.get('total_deliveries', 0) or 0,
                'status': rider_dict.get('status', ''),
                'vehicle_type': rider_dict.get('vehicle_type', ''),
                'current_location': rider_dict.get('current_location', '')
            },
            'summary': {
                'success_rate': success_rate,
                'avg_daily_deliveries': avg_daily_deliveries,
                'total_logs': total_logs,
                'total_orders': total_orders,
                'delivered_orders': delivered_orders
            }
        }

        return jsonify(response_data)

    except Exception as e:
        print(f"Rider performance API error: {e}")
        return jsonify({'error': f'Error loading rider performance: {str(e)}'}), 500

# Error handlers for the blueprint
@riders_bp.errorhandler(404)
def not_found_error(error):
    return render_template('errors/404.html'), 404

@riders_bp.errorhandler(500)
def internal_error(error):
    return render_template('errors/500.html'), 500
