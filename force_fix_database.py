#!/usr/bin/env python3
"""
Force Fix Database Issues
Disable foreign keys temporarily to fix constraint violations
"""

import sqlite3
import os
from datetime import datetime

def force_fix_database():
    """Force fix database by temporarily disabling foreign keys"""
    
    print("🔧 FORCE FIXING DATABASE ISSUES")
    print("=" * 60)
    
    if not os.path.exists('instance/medivent.db'):
        print("❌ Database file not found!")
        return False
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Disable foreign key constraints temporarily
        print("🔓 Disabling foreign key constraints...")
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # Check and fix division_permissions table
        print("🔍 Checking division_permissions table...")
        
        # Get all records with invalid user_id references
        cursor.execute("""
            SELECT dp.* FROM division_permissions dp 
            LEFT JOIN users u ON dp.user_id = u.user_id 
            WHERE u.user_id IS NULL
        """)
        invalid_records = cursor.fetchall()
        
        if invalid_records:
            print(f"   Found {len(invalid_records)} invalid records")
            
            # Delete invalid records
            cursor.execute("""
                DELETE FROM division_permissions 
                WHERE user_id NOT IN (SELECT user_id FROM users WHERE user_id IS NOT NULL)
            """)
            deleted_count = cursor.rowcount
            print(f"   ✅ Deleted {deleted_count} invalid records")
        else:
            print("   ✅ No invalid records found")
        
        # Check and fix other potential issues
        print("\n🔍 Checking other table relationships...")
        
        # Fix orders with invalid customer_id
        cursor.execute("""
            SELECT COUNT(*) FROM orders 
            WHERE customer_id IS NOT NULL 
            AND customer_id NOT IN (SELECT customer_id FROM customers WHERE customer_id IS NOT NULL)
        """)
        invalid_orders = cursor.fetchone()[0]
        
        if invalid_orders > 0:
            print(f"   Found {invalid_orders} orders with invalid customer_id")
            # Set invalid customer_id to NULL instead of deleting
            cursor.execute("""
                UPDATE orders 
                SET customer_id = NULL 
                WHERE customer_id IS NOT NULL 
                AND customer_id NOT IN (SELECT customer_id FROM customers WHERE customer_id IS NOT NULL)
            """)
            print(f"   ✅ Fixed orders with invalid customer references")
        
        # Fix order_items with invalid order_id
        cursor.execute("""
            SELECT COUNT(*) FROM order_items 
            WHERE order_id NOT IN (SELECT order_id FROM orders WHERE order_id IS NOT NULL)
        """)
        invalid_items = cursor.fetchone()[0]
        
        if invalid_items > 0:
            print(f"   Found {invalid_items} order_items with invalid order_id")
            cursor.execute("""
                DELETE FROM order_items 
                WHERE order_id NOT IN (SELECT order_id FROM orders WHERE order_id IS NOT NULL)
            """)
            print(f"   ✅ Fixed order_items with invalid order references")
        
        # Fix payments with invalid order_id
        cursor.execute("""
            SELECT COUNT(*) FROM payments 
            WHERE order_id IS NOT NULL 
            AND order_id != 0 
            AND order_id NOT IN (SELECT order_id FROM orders WHERE order_id IS NOT NULL)
        """)
        invalid_payments = cursor.fetchone()[0]
        
        if invalid_payments > 0:
            print(f"   Found {invalid_payments} payments with invalid order_id")
            cursor.execute("""
                UPDATE payments 
                SET order_id = NULL 
                WHERE order_id IS NOT NULL 
                AND order_id != 0 
                AND order_id NOT IN (SELECT order_id FROM orders WHERE order_id IS NOT NULL)
            """)
            print(f"   ✅ Fixed payments with invalid order references")
        
        # Commit changes
        conn.commit()
        
        # Re-enable foreign key constraints
        print("\n🔒 Re-enabling foreign key constraints...")
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # Final verification
        print("✅ Verifying fixes...")
        cursor.execute("PRAGMA foreign_key_check")
        violations = cursor.fetchall()
        
        if not violations:
            print("✅ All foreign key constraints are now valid")
            success = True
        else:
            print(f"⚠️ {len(violations)} violations still remain")
            for violation in violations[:5]:  # Show first 5
                print(f"   Table: {violation[0]}, Row: {violation[1]}")
            success = False
        
        # Database integrity check
        cursor.execute("PRAGMA integrity_check")
        integrity = cursor.fetchone()[0]
        
        if integrity == 'ok':
            print("✅ Database integrity: PASSED")
        else:
            print(f"❌ Database integrity: {integrity}")
            success = False
        
        conn.close()
        return success
        
    except Exception as e:
        print(f"❌ Error force fixing database: {e}")
        return False

def create_database_health_report():
    """Create a comprehensive database health report"""
    
    print("\n📊 CREATING DATABASE HEALTH REPORT")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        # Get table statistics
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
        
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'total_tables': len(tables),
            'table_stats': {}
        }
        
        print(f"📋 Analyzing {len(tables)} tables...")
        
        for table in tables:
            try:
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                # Get column info
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                health_report['table_stats'][table] = {
                    'row_count': row_count,
                    'column_count': len(columns)
                }
                
                print(f"   ✅ {table}: {row_count} rows, {len(columns)} columns")
                
            except Exception as e:
                print(f"   ❌ {table}: Error - {e}")
                health_report['table_stats'][table] = {
                    'error': str(e)
                }
        
        # Save health report
        import json
        with open('database_health_report.json', 'w') as f:
            json.dump(health_report, f, indent=2)
        
        print(f"💾 Health report saved: database_health_report.json")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating health report: {e}")
        return False

def main():
    """Main execution"""
    print("🚀 FORCE DATABASE FIXING")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Force fix database issues
    database_fixed = force_fix_database()
    
    # Create health report
    health_report_created = create_database_health_report()
    
    print(f"\n🎯 FORCE FIX SUMMARY")
    print("=" * 70)
    print(f"Database issues fixed: {'✅ YES' if database_fixed else '❌ NO'}")
    print(f"Health report created: {'✅ YES' if health_report_created else '❌ NO'}")
    
    if database_fixed:
        print(f"\n🎉 DATABASE SUCCESSFULLY FIXED!")
        print(f"✅ All foreign key constraints are now valid")
        print(f"✅ Database is ready for production use")
    else:
        print(f"\n⚠️ Some database issues may remain")
        print(f"💡 Consider manual review of remaining violations")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return database_fixed

if __name__ == "__main__":
    main()
