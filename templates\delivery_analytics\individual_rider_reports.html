{% extends 'base.html' %}

{% block title %}Individual Rider Reports - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-user-chart text-primary"></i> Individual Rider Performance Reports
        </h1>
        <div>
            <button class="btn btn-success shadow-sm mr-2" onclick="exportRiderReport()">
                <i class="fas fa-download fa-sm text-white-50"></i> Export Report
            </button>
            <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Report Filters</h6>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('delivery_analytics.individual_rider_reports') }}">
                <div class="row">
                    <div class="col-md-4">
                        <label for="rider_id" class="form-label">Select Rider</label>
                        <select class="form-control" id="rider_id" name="rider_id" required>
                            <option value="">Choose a rider...</option>
                            {% for rider in all_riders %}
                            <option value="{{ rider.rider_id }}" {% if rider.rider_id == selected_rider %}selected{% endif %}>
                                {{ rider.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label for="date_range" class="form-label">Date Range</label>
                        <select class="form-control" id="date_range" name="date_range">
                            <option value="7" {% if date_range == '7' %}selected{% endif %}>Last 7 days</option>
                            <option value="30" {% if date_range == '30' %}selected{% endif %}>Last 30 days</option>
                            <option value="90" {% if date_range == '90' %}selected{% endif %}>Last 3 months</option>
                            <option value="180" {% if date_range == '180' %}selected{% endif %}>Last 6 months</option>
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Generate Report
                            </button>
                            <button type="button" class="btn btn-outline-secondary ml-2" onclick="resetFilters()">
                                <i class="fas fa-undo"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    {% if rider_performance %}
    <!-- Rider Performance Summary -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Deliveries</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ rider_performance.total_deliveries }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-truck fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Completed</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ rider_performance.completed_deliveries }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Success Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ "%.1f"|format(rider_performance.success_rate) }}%</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Current Deliveries</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ rider_performance.current_deliveries }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shipping-fast fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rider Information -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Rider Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>Name:</strong> {{ rider_performance.name }}
                        </div>
                        <div class="col-sm-6">
                            <strong>Phone:</strong> {{ rider_performance.phone }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Vehicle:</strong> {{ rider_performance.vehicle_type or 'Not specified' }}
                        </div>
                        <div class="col-sm-6">
                            <strong>Rating:</strong> 
                            <span class="badge badge-{{ 'success' if (rider_performance.rating or 0) >= 4 else 'warning' if (rider_performance.rating or 0) >= 3 else 'danger' }}">
                                {{ "%.1f"|format(rider_performance.rating or 0) }} ⭐
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Report Period</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <strong>From:</strong> {{ start_date.strftime('%B %d, %Y') }}
                        </div>
                        <div class="col-sm-6">
                            <strong>To:</strong> {{ end_date.strftime('%B %d, %Y') }}
                        </div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-sm-6">
                            <strong>Period:</strong> {{ date_range }} days
                        </div>
                        <div class="col-sm-6">
                            <strong>Generated:</strong> {{ now.strftime('%B %d, %Y at %I:%M %p') }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delivery History -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                Delivery History ({{ delivery_history|length }} records)
            </h6>
        </div>
        <div class="card-body">
            {% if delivery_history %}
                <div class="table-responsive">
                    <table class="table table-bordered" id="deliveryHistoryTable">
                        <thead>
                            <tr>
                                <th>Order ID</th>
                                <th>Customer</th>
                                <th>Institution Type</th>
                                <th>Mobile Number</th>
                                <th>Status</th>
                                <th>Order Value</th>
                                <th>Date</th>
                                <th>Address</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for delivery in delivery_history %}
                            <tr>
                                <td>{{ delivery.order_id }}</td>
                                <td>{{ delivery.customer_name }}</td>
                                <td>
                                    <span class="badge badge-info">{{ delivery.institution_type or 'N/A' }}</span>
                                </td>
                                <td>{{ delivery.mobile_number or 'N/A' }}</td>
                                <td>
                                    <span class="badge badge-{{ 'success' if delivery.status == 'Delivered' else 'warning' if delivery.status == 'Out for Delivery' else 'info' }}">
                                        {{ delivery.status }}
                                    </span>
                                </td>
                                <td>₹{{ "%.2f"|format(delivery.order_value) }}</td>
                                <td>{{ delivery.order_date }}</td>
                                <td>{{ delivery.delivery_address[:50] }}{% if delivery.delivery_address|length > 50 %}...{% endif %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No delivery history found for the selected period.
                </div>
            {% endif %}
        </div>
    </div>
    {% else %}
        <div class="card shadow mb-4">
            <div class="card-body text-center">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Select a rider to view performance report</h5>
                <p class="text-muted">Choose a rider from the dropdown above and click "Generate Report" to view detailed performance metrics and delivery history.</p>
            </div>
        </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script>
function exportRiderReport() {
    {% if rider_performance %}
    // Create CSV content
    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Rider Performance Report\n";
    csvContent += "Rider Name,{{ rider_performance.name }}\n";
    csvContent += "Phone,{{ rider_performance.phone }}\n";
    csvContent += "Total Deliveries,{{ rider_performance.total_deliveries }}\n";
    csvContent += "Completed Deliveries,{{ rider_performance.completed_deliveries }}\n";
    csvContent += "Success Rate,{{ '%.1f'|format(rider_performance.success_rate) }}%\n";
    csvContent += "Report Period,{{ start_date }} to {{ end_date }}\n\n";
    
    csvContent += "Order ID,Customer,Institution Type,Mobile,Status,Order Value,Date,Address\n";
    {% for delivery in delivery_history %}
    csvContent += "{{ delivery.order_id }},{{ delivery.customer_name }},{{ delivery.institution_type or 'N/A' }},{{ delivery.mobile_number or 'N/A' }},{{ delivery.status }},{{ '%.2f'|format(delivery.order_value) }},{{ delivery.order_date }},{{ delivery.delivery_address|replace(',', ';') }}\n";
    {% endfor %}
    
    // Download CSV
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "rider_report_{{ rider_performance.name|replace(' ', '_') }}_{{ start_date }}.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    {% else %}
    alert('Please select a rider and generate a report first.');
    {% endif %}
}

function resetFilters() {
    document.getElementById('rider_id').value = '';
    document.getElementById('date_range').value = '30';
}

// Initialize DataTable if delivery history exists
{% if delivery_history %}
$(document).ready(function() {
    $('#deliveryHistoryTable').DataTable({
        "pageLength": 25,
        "order": [[ 6, "desc" ]],
        "responsive": true
    });
});
{% endif %}
</script>
{% endblock %}
