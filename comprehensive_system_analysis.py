#!/usr/bin/env python3
"""
Comprehensive System Analysis for Medivent ERP
Deep investigation of routes, templates, database, and navigation links
"""

import os
import re
import sqlite3
from datetime import datetime
import requests

class SystemAnalyzer:
    """Comprehensive system analyzer for ERP issues"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:3000"
        self.analysis_results = {
            'routes_analysis': {},
            'templates_analysis': {},
            'navigation_links': {},
            'database_issues': {},
            'broken_routes': [],
            'missing_templates': [],
            'href_issues': []
        }
    
    def analyze_route_files(self):
        """Analyze all Python route files"""
        
        print("🔍 ANALYZING ROUTE FILES")
        print("=" * 50)
        
        route_files = []
        
        # Find all Python files in routes directory
        if os.path.exists('routes'):
            for file in os.listdir('routes'):
                if file.endswith('.py') and not file.startswith('__'):
                    route_files.append(f'routes/{file}')
        
        # Also check app.py for routes
        if os.path.exists('app.py'):
            route_files.append('app.py')
        
        for route_file in route_files:
            try:
                print(f"\n📄 Analyzing: {route_file}")
                
                with open(route_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all route definitions
                route_patterns = re.findall(r"@app\.route\(['\"]([^'\"]+)['\"]", content)
                blueprint_patterns = re.findall(r"@[^.]+\.route\(['\"]([^'\"]+)['\"]", content)
                
                all_routes = route_patterns + blueprint_patterns
                
                # Find function definitions
                function_patterns = re.findall(r"def ([a-zA-Z_][a-zA-Z0-9_]*)\(", content)
                
                # Find template references
                template_patterns = re.findall(r"render_template\(['\"]([^'\"]+)['\"]", content)
                
                self.analysis_results['routes_analysis'][route_file] = {
                    'routes': all_routes,
                    'functions': function_patterns,
                    'templates': template_patterns,
                    'line_count': len(content.split('\n'))
                }
                
                print(f"   Routes found: {len(all_routes)}")
                print(f"   Functions found: {len(function_patterns)}")
                print(f"   Templates referenced: {len(template_patterns)}")
                
                # Check for specific problematic patterns
                if 'not all arguments converted during string formatting' in content:
                    print(f"   ⚠️ String formatting issue detected")
                
                if 'riders/dashboard' in content:
                    print(f"   🔍 Riders dashboard route found")
                
                if 'finance/comprehensive-reports' in content:
                    print(f"   🔍 Finance comprehensive reports route found")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {route_file}: {e}")
                self.analysis_results['routes_analysis'][route_file] = {'error': str(e)}
    
    def analyze_template_files(self):
        """Analyze all HTML template files"""
        
        print("\n🎨 ANALYZING TEMPLATE FILES")
        print("=" * 50)
        
        template_files = []
        
        # Find all HTML files in templates directory
        if os.path.exists('templates'):
            for root, dirs, files in os.walk('templates'):
                for file in files:
                    if file.endswith('.html'):
                        template_files.append(os.path.join(root, file))
        
        for template_file in template_files:
            try:
                print(f"\n📄 Analyzing: {template_file}")
                
                with open(template_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Find all href links
                href_patterns = re.findall(r'href=["\']([^"\']+)["\']', content)
                
                # Find url_for patterns
                url_for_patterns = re.findall(r"url_for\(['\"]([^'\"]+)['\"]", content)
                
                # Find template extends/includes
                extends_patterns = re.findall(r"{%\s*extends\s+['\"]([^'\"]+)['\"]", content)
                include_patterns = re.findall(r"{%\s*include\s+['\"]([^'\"]+)['\"]", content)
                
                self.analysis_results['templates_analysis'][template_file] = {
                    'href_links': href_patterns,
                    'url_for_calls': url_for_patterns,
                    'extends': extends_patterns,
                    'includes': include_patterns,
                    'line_count': len(content.split('\n'))
                }
                
                print(f"   Href links found: {len(href_patterns)}")
                print(f"   url_for calls found: {len(url_for_patterns)}")
                
                # Check for specific problematic templates
                if 'finance/dashboard' in template_file:
                    print(f"   🔍 Finance dashboard template found")
                    print(f"   Href links: {href_patterns[:5]}...")  # Show first 5
                
                if 'riders/dashboard' in template_file:
                    print(f"   🔍 Riders dashboard template found")
                
            except Exception as e:
                print(f"   ❌ Error analyzing {template_file}: {e}")
                self.analysis_results['templates_analysis'][template_file] = {'error': str(e)}
    
    def test_route_accessibility(self):
        """Test accessibility of all known routes"""
        
        print("\n🌐 TESTING ROUTE ACCESSIBILITY")
        print("=" * 50)
        
        # Known problematic routes
        test_routes = [
            '/finance/dashboard',
            '/riders/dashboard', 
            '/finance/comprehensive-reports',
            '/delivery_analytics/',
            '/advanced_payment/',
            '/sales_analytics/',
            '/admin/bulk_operations',
            '/admin/system_health'
        ]
        
        for route in test_routes:
            try:
                print(f"\n🔍 Testing: {route}")
                response = requests.get(f"{self.base_url}{route}", timeout=10)
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code == 200:
                    # Check if it's a login redirect
                    if 'login' in response.text.lower():
                        print(f"   🔐 Requires authentication (expected)")
                        status = 'AUTH_REQUIRED'
                    else:
                        print(f"   ✅ Accessible")
                        status = 'SUCCESS'
                elif response.status_code == 404:
                    print(f"   ❌ Not found")
                    status = 'NOT_FOUND'
                    self.analysis_results['broken_routes'].append(route)
                elif response.status_code == 500:
                    print(f"   💥 Server error")
                    status = 'SERVER_ERROR'
                    self.analysis_results['broken_routes'].append(route)
                else:
                    print(f"   ⚠️ Status: {response.status_code}")
                    status = f'HTTP_{response.status_code}'
                
                self.analysis_results['navigation_links'][route] = {
                    'status_code': response.status_code,
                    'status': status,
                    'accessible': response.status_code in [200, 302]
                }
                
            except requests.exceptions.ConnectionError:
                print(f"   ❌ Connection error - server may be down")
                self.analysis_results['navigation_links'][route] = {
                    'status': 'CONNECTION_ERROR',
                    'accessible': False
                }
            except Exception as e:
                print(f"   ❌ Error: {e}")
                self.analysis_results['navigation_links'][route] = {
                    'status': 'ERROR',
                    'error': str(e),
                    'accessible': False
                }
    
    def analyze_database_schema(self):
        """Analyze database schema and relationships"""
        
        print("\n🗄️ ANALYZING DATABASE SCHEMA")
        print("=" * 50)
        
        if not os.path.exists('instance/medivent.db'):
            print("❌ Database file not found!")
            return
        
        try:
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
            tables = [row[0] for row in cursor.fetchall() if not row[0].startswith('sqlite_')]
            
            print(f"📊 Found {len(tables)} tables")
            
            # Check for specific tables related to problematic routes
            finance_tables = [t for t in tables if 'payment' in t.lower() or 'finance' in t.lower() or 'invoice' in t.lower()]
            rider_tables = [t for t in tables if 'rider' in t.lower() or 'delivery' in t.lower()]
            
            print(f"💰 Finance-related tables: {finance_tables}")
            print(f"🏍️ Rider-related tables: {rider_tables}")
            
            # Check foreign key constraints
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = cursor.fetchall()
            
            if fk_violations:
                print(f"⚠️ Foreign key violations: {len(fk_violations)}")
                self.analysis_results['database_issues']['foreign_key_violations'] = len(fk_violations)
            else:
                print("✅ No foreign key violations")
            
            # Check table row counts for key tables
            key_tables = ['users', 'customers', 'orders', 'payments', 'riders', 'products']
            for table in key_tables:
                if table in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"   {table}: {count} rows")
            
            conn.close()
            
        except Exception as e:
            print(f"❌ Database analysis error: {e}")
            self.analysis_results['database_issues']['error'] = str(e)
    
    def find_string_formatting_errors(self):
        """Find string formatting errors in the codebase"""
        
        print("\n🔍 SEARCHING FOR STRING FORMATTING ERRORS")
        print("=" * 50)
        
        # Search in all Python files
        python_files = []
        
        # Add app.py
        if os.path.exists('app.py'):
            python_files.append('app.py')
        
        # Add route files
        if os.path.exists('routes'):
            for file in os.listdir('routes'):
                if file.endswith('.py'):
                    python_files.append(f'routes/{file}')
        
        formatting_errors = []
        
        for file_path in python_files:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                for line_num, line in enumerate(lines, 1):
                    # Look for problematic string formatting patterns
                    if '%' in line and ('format' in line or 'str(' in line):
                        # Check for potential formatting issues
                        if line.count('%') != line.count('%s') + line.count('%d') + line.count('%f'):
                            formatting_errors.append({
                                'file': file_path,
                                'line': line_num,
                                'content': line.strip()
                            })
                    
                    # Look for specific error messages
                    if 'not all arguments converted during string formatting' in line:
                        formatting_errors.append({
                            'file': file_path,
                            'line': line_num,
                            'content': line.strip(),
                            'type': 'error_message'
                        })
                    
                    # Look for rider performance related formatting
                    if 'rider' in line.lower() and 'performance' in line.lower() and '%' in line:
                        formatting_errors.append({
                            'file': file_path,
                            'line': line_num,
                            'content': line.strip(),
                            'type': 'rider_performance'
                        })
                
            except Exception as e:
                print(f"   ❌ Error reading {file_path}: {e}")
        
        if formatting_errors:
            print(f"⚠️ Found {len(formatting_errors)} potential string formatting issues:")
            for error in formatting_errors:
                print(f"   📄 {error['file']}:{error['line']} - {error['content'][:80]}...")
        else:
            print("✅ No obvious string formatting errors found")
        
        self.analysis_results['string_formatting_errors'] = formatting_errors
    
    def generate_analysis_report(self):
        """Generate comprehensive analysis report"""
        
        print("\n📋 GENERATING ANALYSIS REPORT")
        print("=" * 50)
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_route_files': len(self.analysis_results['routes_analysis']),
                'total_template_files': len(self.analysis_results['templates_analysis']),
                'broken_routes': len(self.analysis_results['broken_routes']),
                'string_formatting_errors': len(self.analysis_results.get('string_formatting_errors', [])),
                'database_issues': len(self.analysis_results['database_issues'])
            },
            'detailed_results': self.analysis_results
        }
        
        # Save report
        import json
        report_file = f"system_analysis_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"💾 Analysis report saved: {report_file}")
        
        # Print summary
        print(f"\n📊 ANALYSIS SUMMARY:")
        print(f"   Route files analyzed: {report['summary']['total_route_files']}")
        print(f"   Template files analyzed: {report['summary']['total_template_files']}")
        print(f"   Broken routes found: {report['summary']['broken_routes']}")
        print(f"   String formatting errors: {report['summary']['string_formatting_errors']}")
        print(f"   Database issues: {report['summary']['database_issues']}")
        
        return report
    
    def run_comprehensive_analysis(self):
        """Run complete system analysis"""
        
        print("🚀 COMPREHENSIVE SYSTEM ANALYSIS")
        print("=" * 70)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Run all analysis components
        self.analyze_route_files()
        self.analyze_template_files()
        self.test_route_accessibility()
        self.analyze_database_schema()
        self.find_string_formatting_errors()
        
        # Generate final report
        report = self.generate_analysis_report()
        
        print(f"\n🎯 ANALYSIS COMPLETED")
        print("=" * 70)
        print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return report

def main():
    """Main analysis execution"""
    analyzer = SystemAnalyzer()
    report = analyzer.run_comprehensive_analysis()
    return report

if __name__ == "__main__":
    main()
