#!/usr/bin/env python3
"""
Quick Flask Test - Check if app can start
"""

import sys
import traceback

print("Testing Flask app startup...")

try:
    print("1. Importing app module...")
    import app
    print("✅ App module imported successfully")
    
    print("2. Checking Flask app object...")
    flask_app = app.app
    print(f"✅ Flask app object: {flask_app}")
    
    print("3. Checking routes...")
    routes = []
    for rule in flask_app.url_map.iter_rules():
        if 'riders_dashboard' in rule.endpoint:
            routes.append(f"{rule.endpoint} -> {rule.rule}")
    
    if routes:
        print("✅ riders_dashboard route found:")
        for route in routes:
            print(f"   {route}")
    else:
        print("❌ riders_dashboard route NOT found")
    
    print("4. Testing URL generation...")
    with flask_app.app_context():
        try:
            from flask import url_for
            url = url_for('riders_dashboard')
            print(f"✅ URL generation successful: {url}")
        except Exception as e:
            print(f"❌ URL generation failed: {e}")
    
    print("5. Starting Flask server...")
    flask_app.run(host='127.0.0.1', port=3000, debug=False)

except Exception as e:
    print(f"❌ Error: {e}")
    print("Full traceback:")
    traceback.print_exc()
