#!/usr/bin/env python3
"""
Comprehensive Quality Assurance and Testing System
Test all new APIs, validate database operations, ensure responsive design, and verify authentication
"""

import requests
import sqlite3
import os
from datetime import datetime
import json

class ComprehensiveQATester:
    """Comprehensive QA testing system"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:3000"
        self.test_results = {
            'api_tests': [],
            'database_tests': [],
            'authentication_tests': [],
            'route_tests': [],
            'file_upload_tests': []
        }
        self.session = requests.Session()
    
    def test_new_api_endpoints(self):
        """Test all new API endpoints with proper error handling"""
        
        print("🧪 TESTING NEW API ENDPOINTS")
        print("=" * 50)
        
        # New API endpoints to test
        api_endpoints = [
            # Original API endpoints
            ('GET', '/api/customers', 'Get customers list'),
            ('GET', '/api/inventory', 'Get inventory list'),
            ('GET', '/api/reports', 'Get available reports'),
            ('GET', '/api/users', 'Get users list'),
            ('POST', '/api/reports/sales_summary', 'Generate sales summary report'),
            ('POST', '/api/reports/inventory_status', 'Generate inventory status report'),
            
            # New analytics API endpoints
            ('GET', '/delivery_analytics/api/delivery_stats', 'Delivery statistics API'),
            ('GET', '/advanced_payment/api/payment_stats', 'Payment statistics API'),
            ('GET', '/sales_analytics/api/sales_trends', 'Sales trends API'),
            ('GET', '/sales_analytics/api/division_performance', 'Division performance API'),
        ]
        
        success_count = 0
        total_count = len(api_endpoints)
        
        for method, endpoint, description in api_endpoints:
            try:
                print(f"\\n📡 Testing {method} {endpoint}")
                print(f"   Description: {description}")
                
                if method == 'GET':
                    response = self.session.get(f"{self.base_url}{endpoint}", timeout=10)
                else:  # POST
                    response = self.session.post(f"{self.base_url}{endpoint}", 
                                               json={}, timeout=10)
                
                print(f"   Status: {response.status_code}")
                
                if response.status_code in [200, 201]:
                    # Check if it's JSON response
                    content_type = response.headers.get('content-type', '')
                    if 'application/json' in content_type:
                        try:
                            data = response.json()
                            if 'success' in data or 'data' in data:
                                print(f"   ✅ SUCCESS - Valid API Response")
                                success_count += 1
                                self.test_results['api_tests'].append({
                                    'endpoint': endpoint,
                                    'method': method,
                                    'status': 'PASS',
                                    'response_type': 'JSON'
                                })
                            else:
                                print(f"   ⚠️  Unexpected JSON structure")
                                self.test_results['api_tests'].append({
                                    'endpoint': endpoint,
                                    'method': method,
                                    'status': 'PARTIAL',
                                    'issue': 'Unexpected JSON structure'
                                })
                        except json.JSONDecodeError:
                            print(f"   ⚠️  Invalid JSON response")
                            self.test_results['api_tests'].append({
                                'endpoint': endpoint,
                                'method': method,
                                'status': 'FAIL',
                                'issue': 'Invalid JSON'
                            })
                    else:
                        # HTML response (likely login redirect)
                        if 'login' in response.text.lower():
                            print(f"   🔐 AUTHENTICATION REQUIRED (expected)")
                            success_count += 1
                            self.test_results['api_tests'].append({
                                'endpoint': endpoint,
                                'method': method,
                                'status': 'PASS',
                                'response_type': 'AUTH_REQUIRED'
                            })
                        else:
                            print(f"   ⚠️  Unexpected HTML response")
                            self.test_results['api_tests'].append({
                                'endpoint': endpoint,
                                'method': method,
                                'status': 'PARTIAL',
                                'issue': 'Unexpected HTML'
                            })
                            
                elif response.status_code == 401:
                    print(f"   🔐 AUTHENTICATION REQUIRED")
                    success_count += 1
                    self.test_results['api_tests'].append({
                        'endpoint': endpoint,
                        'method': method,
                        'status': 'PASS',
                        'response_type': 'AUTH_REQUIRED'
                    })
                elif response.status_code == 404:
                    print(f"   ❌ NOT FOUND")
                    self.test_results['api_tests'].append({
                        'endpoint': endpoint,
                        'method': method,
                        'status': 'FAIL',
                        'issue': 'Endpoint not found'
                    })
                else:
                    print(f"   ❌ ERROR: {response.status_code}")
                    self.test_results['api_tests'].append({
                        'endpoint': endpoint,
                        'method': method,
                        'status': 'FAIL',
                        'issue': f'HTTP {response.status_code}'
                    })
                    
            except requests.exceptions.ConnectionError:
                print(f"   ❌ CONNECTION ERROR")
                self.test_results['api_tests'].append({
                    'endpoint': endpoint,
                    'method': method,
                    'status': 'FAIL',
                    'issue': 'Connection error'
                })
            except Exception as e:
                print(f"   ❌ EXCEPTION: {e}")
                self.test_results['api_tests'].append({
                    'endpoint': endpoint,
                    'method': method,
                    'status': 'FAIL',
                    'issue': str(e)
                })
        
        print(f"\\n📊 API Testing Summary:")
        print(f"   Total endpoints: {total_count}")
        print(f"   Successful: {success_count}")
        print(f"   Success rate: {(success_count/total_count)*100:.1f}%")
        
        return success_count, total_count
    
    def test_database_operations(self):
        """Validate database operations with transaction safety"""
        
        print("\\n🗄️ TESTING DATABASE OPERATIONS")
        print("=" * 50)
        
        if not os.path.exists('instance/medivent.db'):
            print("❌ Database file not found!")
            return False
        
        try:
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            # Test database integrity
            print("🔍 Testing database integrity...")
            cursor.execute("PRAGMA integrity_check")
            integrity_result = cursor.fetchone()[0]
            
            if integrity_result == 'ok':
                print("✅ Database integrity check passed")
                self.test_results['database_tests'].append({
                    'test': 'integrity_check',
                    'status': 'PASS'
                })
            else:
                print(f"❌ Database integrity issues: {integrity_result}")
                self.test_results['database_tests'].append({
                    'test': 'integrity_check',
                    'status': 'FAIL',
                    'issue': integrity_result
                })
            
            # Test foreign key constraints
            print("🔗 Testing foreign key constraints...")
            cursor.execute("PRAGMA foreign_key_check")
            fk_violations = cursor.fetchall()
            
            if not fk_violations:
                print("✅ Foreign key constraints are valid")
                self.test_results['database_tests'].append({
                    'test': 'foreign_key_check',
                    'status': 'PASS'
                })
            else:
                print(f"❌ Foreign key violations found: {len(fk_violations)}")
                self.test_results['database_tests'].append({
                    'test': 'foreign_key_check',
                    'status': 'FAIL',
                    'issue': f'{len(fk_violations)} violations'
                })
            
            # Test critical tables exist
            print("📋 Testing critical tables...")
            critical_tables = ['users', 'customers', 'products', 'orders', 'payments', 'divisions']
            missing_tables = []
            
            for table in critical_tables:
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if not cursor.fetchone():
                    missing_tables.append(table)
            
            if not missing_tables:
                print("✅ All critical tables exist")
                self.test_results['database_tests'].append({
                    'test': 'critical_tables',
                    'status': 'PASS'
                })
            else:
                print(f"❌ Missing critical tables: {missing_tables}")
                self.test_results['database_tests'].append({
                    'test': 'critical_tables',
                    'status': 'FAIL',
                    'issue': f'Missing: {missing_tables}'
                })
            
            # Test transaction safety with rollback
            print("🔄 Testing transaction safety...")
            try:
                cursor.execute("BEGIN TRANSACTION")
                cursor.execute("INSERT INTO activity_logs (action, details) VALUES (?, ?)", 
                             ('TEST_TRANSACTION', 'QA Testing transaction safety'))
                cursor.execute("ROLLBACK")
                print("✅ Transaction rollback successful")
                self.test_results['database_tests'].append({
                    'test': 'transaction_safety',
                    'status': 'PASS'
                })
            except Exception as e:
                print(f"❌ Transaction test failed: {e}")
                self.test_results['database_tests'].append({
                    'test': 'transaction_safety',
                    'status': 'FAIL',
                    'issue': str(e)
                })
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"❌ Database testing error: {e}")
            self.test_results['database_tests'].append({
                'test': 'general',
                'status': 'FAIL',
                'issue': str(e)
            })
            return False
    
    def test_route_accessibility(self):
        """Test all new routes return proper responses"""
        
        print("\\n🛣️ TESTING ROUTE ACCESSIBILITY")
        print("=" * 50)
        
        # New routes to test
        new_routes = [
            '/delivery_analytics/',
            '/delivery_analytics/real_time_tracking',
            '/delivery_analytics/performance_kpis',
            '/delivery_analytics/comprehensive_reports',
            '/advanced_payment/',
            '/advanced_payment/bulk_processing',
            '/advanced_payment/automated_matching',
            '/advanced_payment/reconciliation',
            '/advanced_payment/analytics',
            '/sales_analytics/',
            '/sales_analytics/salesperson_ledger',
            '/sales_analytics/team_performance',
            '/sales_analytics/division_ledger',
            '/sales_analytics/division_analysis',
            '/admin/bulk_operations',
            '/admin/data_export',
            '/admin/system_health',
            '/reports/advanced/index',
            '/reports/sales/index'
        ]
        
        accessible_count = 0
        total_routes = len(new_routes)
        
        for route in new_routes:
            try:
                print(f"🔍 Testing route: {route}")
                response = self.session.get(f"{self.base_url}{route}", timeout=10)
                
                if response.status_code == 200:
                    if 'login' in response.text.lower():
                        print(f"   🔐 Requires authentication (expected)")
                        accessible_count += 1
                        self.test_results['route_tests'].append({
                            'route': route,
                            'status': 'PASS',
                            'response_type': 'AUTH_REQUIRED'
                        })
                    else:
                        print(f"   ✅ Accessible")
                        accessible_count += 1
                        self.test_results['route_tests'].append({
                            'route': route,
                            'status': 'PASS',
                            'response_type': 'SUCCESS'
                        })
                elif response.status_code == 302:
                    print(f"   🔄 Redirected (likely to login)")
                    accessible_count += 1
                    self.test_results['route_tests'].append({
                        'route': route,
                        'status': 'PASS',
                        'response_type': 'REDIRECT'
                    })
                elif response.status_code == 404:
                    print(f"   ❌ Not found")
                    self.test_results['route_tests'].append({
                        'route': route,
                        'status': 'FAIL',
                        'issue': 'Route not found'
                    })
                else:
                    print(f"   ⚠️  Status: {response.status_code}")
                    self.test_results['route_tests'].append({
                        'route': route,
                        'status': 'PARTIAL',
                        'issue': f'HTTP {response.status_code}'
                    })
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                self.test_results['route_tests'].append({
                    'route': route,
                    'status': 'FAIL',
                    'issue': str(e)
                })
        
        print(f"\\n📊 Route Testing Summary:")
        print(f"   Total routes: {total_routes}")
        print(f"   Accessible: {accessible_count}")
        print(f"   Success rate: {(accessible_count/total_routes)*100:.1f}%")
        
        return accessible_count, total_routes
    
    def test_authentication_security(self):
        """Test authentication and authorization for sensitive operations"""
        
        print("\\n🔐 TESTING AUTHENTICATION & SECURITY")
        print("=" * 50)
        
        # Test protected endpoints without authentication
        protected_endpoints = [
            '/api/users',
            '/admin/bulk_operations',
            '/admin/system_health',
            '/advanced_payment/bulk_processing',
            '/sales_analytics/division_ledger'
        ]
        
        protected_count = 0
        total_protected = len(protected_endpoints)
        
        # Create a new session without authentication
        unauth_session = requests.Session()
        
        for endpoint in protected_endpoints:
            try:
                print(f"🔒 Testing protection: {endpoint}")
                response = unauth_session.get(f"{self.base_url}{endpoint}", timeout=10)
                
                if response.status_code in [401, 403]:
                    print(f"   ✅ Properly protected (401/403)")
                    protected_count += 1
                    self.test_results['authentication_tests'].append({
                        'endpoint': endpoint,
                        'status': 'PASS',
                        'protection': 'PROPER_AUTH'
                    })
                elif response.status_code == 302:
                    print(f"   ✅ Redirected to login")
                    protected_count += 1
                    self.test_results['authentication_tests'].append({
                        'endpoint': endpoint,
                        'status': 'PASS',
                        'protection': 'LOGIN_REDIRECT'
                    })
                elif 'login' in response.text.lower():
                    print(f"   ✅ Shows login page")
                    protected_count += 1
                    self.test_results['authentication_tests'].append({
                        'endpoint': endpoint,
                        'status': 'PASS',
                        'protection': 'LOGIN_PAGE'
                    })
                else:
                    print(f"   ❌ Not properly protected")
                    self.test_results['authentication_tests'].append({
                        'endpoint': endpoint,
                        'status': 'FAIL',
                        'issue': 'No authentication required'
                    })
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                self.test_results['authentication_tests'].append({
                    'endpoint': endpoint,
                    'status': 'FAIL',
                    'issue': str(e)
                })
        
        print(f"\\n📊 Authentication Testing Summary:")
        print(f"   Total protected endpoints: {total_protected}")
        print(f"   Properly protected: {protected_count}")
        print(f"   Security rate: {(protected_count/total_protected)*100:.1f}%")
        
        return protected_count, total_protected
    
    def generate_qa_report(self):
        """Generate comprehensive QA test report"""
        
        print("\\n📋 GENERATING QA TEST REPORT")
        print("=" * 50)
        
        # Calculate overall statistics
        total_tests = (len(self.test_results['api_tests']) + 
                      len(self.test_results['database_tests']) + 
                      len(self.test_results['route_tests']) + 
                      len(self.test_results['authentication_tests']))
        
        passed_tests = sum(1 for category in self.test_results.values() 
                          for test in category if test.get('status') == 'PASS')
        
        # Generate report
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'passed_tests': passed_tests,
                'success_rate': (passed_tests / total_tests * 100) if total_tests > 0 else 0
            },
            'detailed_results': self.test_results
        }
        
        # Save report
        report_file = f"qa_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"💾 QA report saved: {report_file}")
        print(f"📊 Overall success rate: {report['summary']['success_rate']:.1f}%")
        
        return report
    
    def run_comprehensive_qa(self):
        """Run complete QA testing suite"""
        
        print("🚀 COMPREHENSIVE QA TESTING SUITE")
        print("=" * 70)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Test server connectivity first
        try:
            response = requests.get(self.base_url, timeout=5)
            if response.status_code == 200:
                print("✅ Server is accessible")
            else:
                print(f"⚠️ Server responded with status {response.status_code}")
        except:
            print("❌ Server is not accessible - some tests will fail")
        
        # Run all test suites
        api_success, api_total = self.test_new_api_endpoints()
        db_success = self.test_database_operations()
        route_success, route_total = self.test_route_accessibility()
        auth_success, auth_total = self.test_authentication_security()
        
        # Generate final report
        report = self.generate_qa_report()
        
        print(f"\\n🎯 COMPREHENSIVE QA SUMMARY")
        print("=" * 70)
        print(f"API Tests: {api_success}/{api_total} passed")
        print(f"Database Tests: {'PASS' if db_success else 'FAIL'}")
        print(f"Route Tests: {route_success}/{route_total} passed")
        print(f"Authentication Tests: {auth_success}/{auth_total} passed")
        print(f"Overall Success Rate: {report['summary']['success_rate']:.1f}%")
        
        if report['summary']['success_rate'] >= 90:
            print("\\n🎉 EXCELLENT - System is ready for production!")
        elif report['summary']['success_rate'] >= 75:
            print("\\n✅ GOOD - Minor issues to address")
        else:
            print("\\n⚠️ NEEDS ATTENTION - Several issues found")
        
        print(f"\\n🏁 QA testing completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return report

def main():
    """Main QA testing execution"""
    tester = ComprehensiveQATester()
    report = tester.run_comprehensive_qa()
    return report

if __name__ == "__main__":
    main()
