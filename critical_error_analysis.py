#!/usr/bin/env python3
"""
CRITICAL ERP SYSTEM ERROR ANALYSIS
Comprehensive analysis and resolution of database and formatting errors
"""

import sqlite3
import os
import sys
import traceback
import re
from datetime import datetime

def get_database_connection():
    """Get database connection"""
    try:
        db_path = 'instance/medivent.db'
        if not os.path.exists(db_path):
            print(f"❌ Database not found at {db_path}")
            return None
        
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn
    except Exception as e:
        print(f"❌ Database connection error: {e}")
        return None

def analyze_database_schema():
    """Analyze current database schema for missing columns"""
    print("🔍 ANALYZING DATABASE SCHEMA")
    print("=" * 50)
    
    conn = get_database_connection()
    if not conn:
        return False
    
    try:
        # Check orders table schema
        print("\n📋 ORDERS TABLE SCHEMA:")
        orders_columns = conn.execute("PRAGMA table_info(orders)").fetchall()
        orders_column_names = [col[1] for col in orders_columns]
        
        for col in orders_columns:
            print(f"   {col[1]:<20} {col[2]:<15} {'NOT NULL' if col[3] else 'NULL'}")
        
        # Check if delivery_address exists
        has_delivery_address = 'delivery_address' in orders_column_names
        print(f"\n✅ delivery_address column: {'EXISTS' if has_delivery_address else '❌ MISSING'}")
        
        # Check riders table schema
        print("\n📋 RIDERS TABLE SCHEMA:")
        riders_columns = conn.execute("PRAGMA table_info(riders)").fetchall()
        riders_column_names = [col[1] for col in riders_columns]
        
        for col in riders_columns:
            print(f"   {col[1]:<20} {col[2]:<15} {'NOT NULL' if col[3] else 'NULL'}")
        
        # Check if is_active exists
        has_is_active = 'is_active' in riders_column_names
        print(f"\n✅ is_active column: {'EXISTS' if has_is_active else '❌ MISSING'}")
        
        # Summary
        print(f"\n📊 SCHEMA ANALYSIS SUMMARY:")
        print(f"Orders table columns: {len(orders_column_names)}")
        print(f"Riders table columns: {len(riders_column_names)}")
        print(f"Missing columns found: {2 - (has_delivery_address + has_is_active)}")
        
        return {
            'orders_has_delivery_address': has_delivery_address,
            'riders_has_is_active': has_is_active,
            'orders_columns': orders_column_names,
            'riders_columns': riders_column_names
        }
        
    except Exception as e:
        print(f"❌ Error analyzing schema: {e}")
        return False
    finally:
        conn.close()

def find_string_formatting_errors():
    """Find specific string formatting errors in the codebase"""
    print("\n🔍 ANALYZING STRING FORMATTING ERRORS")
    print("=" * 50)
    
    # Files to analyze
    files_to_check = [
        'routes/sales_analytics.py',
        'routes/delivery_analytics.py',
        'routes/modern_riders.py',
        'sales_division_analytics.py'
    ]
    
    formatting_issues = []
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            print(f"⚠️ File not found: {file_path}")
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            print(f"\n📄 Analyzing {file_path}:")
            
            for line_num, line in enumerate(lines, 1):
                line_content = line.strip()
                
                # Look for potential string formatting issues
                if '%' in line_content and ('team' in line_content.lower() or 'performance' in line_content.lower()):
                    # Check for % formatting patterns
                    percent_count = line_content.count('%s') + line_content.count('%d') + line_content.count('%f')
                    if percent_count > 0:
                        formatting_issues.append({
                            'file': file_path,
                            'line': line_num,
                            'content': line_content,
                            'type': 'percent_formatting',
                            'placeholders': percent_count
                        })
                        print(f"   ⚠️ Line {line_num}: Potential % formatting issue")
                        print(f"      {line_content[:80]}...")
                
                # Look for flash messages with formatting
                if 'flash(' in line_content and ('team' in line_content.lower() or 'performance' in line_content.lower()):
                    formatting_issues.append({
                        'file': file_path,
                        'line': line_num,
                        'content': line_content,
                        'type': 'flash_message'
                    })
                    print(f"   📢 Line {line_num}: Flash message found")
                    print(f"      {line_content[:80]}...")
                
                # Look for f-string or .format() issues
                if ('{' in line_content and '}' in line_content and 
                    ('team' in line_content.lower() or 'performance' in line_content.lower())):
                    # Count braces
                    open_braces = line_content.count('{')
                    close_braces = line_content.count('}')
                    if open_braces != close_braces:
                        formatting_issues.append({
                            'file': file_path,
                            'line': line_num,
                            'content': line_content,
                            'type': 'brace_mismatch',
                            'open_braces': open_braces,
                            'close_braces': close_braces
                        })
                        print(f"   ❌ Line {line_num}: Brace mismatch")
                        print(f"      {line_content[:80]}...")
        
        except Exception as e:
            print(f"❌ Error analyzing {file_path}: {e}")
    
    print(f"\n📊 FORMATTING ANALYSIS SUMMARY:")
    print(f"Files analyzed: {len([f for f in files_to_check if os.path.exists(f)])}")
    print(f"Potential issues found: {len(formatting_issues)}")
    
    return formatting_issues

def find_problematic_sql_queries():
    """Find SQL queries that reference missing columns"""
    print("\n🔍 ANALYZING PROBLEMATIC SQL QUERIES")
    print("=" * 50)
    
    # Files to check for SQL queries
    files_to_check = [
        'routes/sales_analytics.py',
        'routes/delivery_analytics.py',
        'routes/modern_riders.py',
        'delivery_analytics_system.py'
    ]
    
    problematic_queries = []
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"\n📄 Analyzing SQL in {file_path}:")
            
            # Look for queries with missing columns
            missing_column_patterns = [
                (r'o\.delivery_address', 'orders.delivery_address'),
                (r'r\.is_active', 'riders.is_active'),
            ]
            
            for pattern, description in missing_column_patterns:
                matches = re.finditer(pattern, content, re.IGNORECASE)
                for match in matches:
                    # Find line number
                    line_num = content[:match.start()].count('\n') + 1
                    
                    # Get context around the match
                    lines = content.split('\n')
                    context_start = max(0, line_num - 3)
                    context_end = min(len(lines), line_num + 2)
                    context = '\n'.join(lines[context_start:context_end])
                    
                    problematic_queries.append({
                        'file': file_path,
                        'line': line_num,
                        'pattern': pattern,
                        'description': description,
                        'context': context
                    })
                    
                    print(f"   ❌ Line {line_num}: References {description}")
        
        except Exception as e:
            print(f"❌ Error analyzing SQL in {file_path}: {e}")
    
    print(f"\n📊 SQL ANALYSIS SUMMARY:")
    print(f"Problematic queries found: {len(problematic_queries)}")
    
    return problematic_queries

def generate_fix_plan(schema_analysis, formatting_issues, sql_issues):
    """Generate comprehensive fix plan"""
    print("\n📋 GENERATING COMPREHENSIVE FIX PLAN")
    print("=" * 50)
    
    fix_plan = {
        'database_fixes': [],
        'sql_fixes': [],
        'formatting_fixes': [],
        'priority': 'HIGH'
    }
    
    # Database fixes needed
    if not schema_analysis.get('orders_has_delivery_address', True):
        fix_plan['database_fixes'].append({
            'table': 'orders',
            'column': 'delivery_address',
            'type': 'TEXT',
            'action': 'ADD_COLUMN'
        })
    
    if not schema_analysis.get('riders_has_is_active', True):
        fix_plan['database_fixes'].append({
            'table': 'riders',
            'column': 'is_active',
            'type': 'INTEGER DEFAULT 1',
            'action': 'ADD_COLUMN'
        })
    
    # SQL fixes needed
    for issue in sql_issues:
        fix_plan['sql_fixes'].append({
            'file': issue['file'],
            'line': issue['line'],
            'pattern': issue['pattern'],
            'description': issue['description']
        })
    
    # Formatting fixes needed
    for issue in formatting_issues:
        fix_plan['formatting_fixes'].append({
            'file': issue['file'],
            'line': issue['line'],
            'type': issue['type'],
            'content': issue['content']
        })
    
    # Print fix plan
    print(f"🔧 DATABASE FIXES REQUIRED: {len(fix_plan['database_fixes'])}")
    for fix in fix_plan['database_fixes']:
        print(f"   • Add {fix['table']}.{fix['column']} ({fix['type']})")
    
    print(f"\n🔧 SQL FIXES REQUIRED: {len(fix_plan['sql_fixes'])}")
    for fix in fix_plan['sql_fixes']:
        print(f"   • {fix['file']}:{fix['line']} - {fix['description']}")
    
    print(f"\n🔧 FORMATTING FIXES REQUIRED: {len(fix_plan['formatting_fixes'])}")
    for fix in fix_plan['formatting_fixes']:
        print(f"   • {fix['file']}:{fix['line']} - {fix['type']}")
    
    return fix_plan

def main():
    """Main analysis execution"""
    print("🚨 CRITICAL ERP SYSTEM ERROR ANALYSIS")
    print("=" * 70)
    print(f"Started at: {datetime.now()}")
    
    # Step 1: Analyze database schema
    schema_analysis = analyze_database_schema()
    if not schema_analysis:
        print("❌ Cannot proceed without database analysis")
        return False
    
    # Step 2: Find string formatting errors
    formatting_issues = find_string_formatting_errors()
    
    # Step 3: Find problematic SQL queries
    sql_issues = find_problematic_sql_queries()
    
    # Step 4: Generate comprehensive fix plan
    fix_plan = generate_fix_plan(schema_analysis, formatting_issues, sql_issues)
    
    print(f"\n🎯 ANALYSIS COMPLETE")
    print("=" * 70)
    print(f"Database issues: {len(fix_plan['database_fixes'])}")
    print(f"SQL issues: {len(fix_plan['sql_fixes'])}")
    print(f"Formatting issues: {len(fix_plan['formatting_fixes'])}")
    print(f"Total issues: {len(fix_plan['database_fixes']) + len(fix_plan['sql_fixes']) + len(fix_plan['formatting_fixes'])}")
    
    if len(fix_plan['database_fixes']) + len(fix_plan['sql_fixes']) + len(fix_plan['formatting_fixes']) > 0:
        print(f"\n🚀 READY TO IMPLEMENT FIXES")
        return True
    else:
        print(f"\n✅ NO CRITICAL ISSUES FOUND")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
