#!/usr/bin/env python3
"""
Comprehensive System Verification
Test all critical routes and functionality after routing fixes
"""

import requests
import sqlite3
import os
from datetime import datetime

class SystemVerifier:
    """Comprehensive system verification"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:3000"
        self.results = {
            'routing_tests': {},
            'database_tests': {},
            'template_tests': {},
            'analytics_tests': {},
            'overall_status': 'UNKNOWN'
        }
    
    def test_critical_routes(self):
        """Test all critical routes for proper functionality"""
        
        print("🔍 TESTING CRITICAL ROUTES")
        print("=" * 50)
        
        critical_routes = [
            ('/dashboard', 'Main Dashboard'),
            ('/riders/dashboard', 'Riders Dashboard'),
            ('/riders', 'Riders Management'),
            ('/delivery_analytics/', 'Delivery Analytics'),
            ('/sales_analytics/', 'Sales Analytics'),
            ('/finance/dashboard', 'Finance Dashboard'),
            ('/finance/comprehensive-reports', 'Finance Reports'),
            ('/advanced_payment/', 'Advanced Payment'),
            ('/products', 'Products Management'),
            ('/customers', 'Customer Management'),
            ('/orders', 'Order Management')
        ]
        
        working_routes = 0
        total_routes = len(critical_routes)
        
        for route, description in critical_routes:
            try:
                response = requests.get(f"{self.base_url}{route}", timeout=10)
                
                if response.status_code == 200:
                    print(f"✅ {route} - {description}: Working")
                    self.results['routing_tests'][route] = 'PASS'
                    working_routes += 1
                elif response.status_code == 302:
                    print(f"🔄 {route} - {description}: Redirecting (likely auth required)")
                    self.results['routing_tests'][route] = 'REDIRECT'
                    working_routes += 1  # Redirects are acceptable for auth-protected routes
                else:
                    print(f"❌ {route} - {description}: Status {response.status_code}")
                    self.results['routing_tests'][route] = f'FAIL_{response.status_code}'
            
            except Exception as e:
                print(f"❌ {route} - {description}: Error {e}")
                self.results['routing_tests'][route] = f'ERROR_{str(e)[:50]}'
        
        success_rate = (working_routes / total_routes) * 100
        print(f"\n📊 Route Success Rate: {success_rate:.1f}% ({working_routes}/{total_routes})")
        
        return success_rate >= 90
    
    def test_database_integrity(self):
        """Test database integrity and column fixes"""
        
        print("\n🗄️ TESTING DATABASE INTEGRITY")
        print("=" * 50)
        
        if not os.path.exists('instance/medivent.db'):
            print("❌ Database file not found!")
            self.results['database_tests']['file_exists'] = 'FAIL'
            return False
        
        try:
            conn = sqlite3.connect('instance/medivent.db')
            cursor = conn.cursor()
            
            # Test critical columns that were added/fixed
            column_tests = [
                ("orders", "total_amount", "SELECT total_amount FROM orders LIMIT 1"),
                ("divisions", "manager_name", "SELECT manager_name FROM divisions LIMIT 1"),
                ("customers", "mobile_number", "SELECT mobile_number FROM customers LIMIT 1"),
                ("customers", "institution_type", "SELECT institution_type FROM customers LIMIT 1"),
                ("riders", "rating", "SELECT rating FROM riders LIMIT 1")
            ]
            
            passed_tests = 0
            
            for table, column, test_query in column_tests:
                try:
                    cursor.execute(test_query)
                    print(f"✅ {table}.{column}: Column exists and accessible")
                    self.results['database_tests'][f'{table}_{column}'] = 'PASS'
                    passed_tests += 1
                except sqlite3.OperationalError as e:
                    print(f"❌ {table}.{column}: {e}")
                    self.results['database_tests'][f'{table}_{column}'] = f'FAIL_{str(e)[:50]}'
            
            # Test complex queries that were failing
            complex_queries = [
                ("Orders with total_amount", """
                    SELECT COUNT(*), SUM(COALESCE(total_amount, order_amount, 0)) 
                    FROM orders WHERE status != 'Cancelled'
                """),
                ("Divisions with managers", """
                    SELECT COUNT(*) FROM divisions WHERE manager_name IS NOT NULL
                """),
                ("Customer analytics", """
                    SELECT institution_type, COUNT(*) 
                    FROM customers 
                    WHERE institution_type IS NOT NULL 
                    GROUP BY institution_type
                """)
            ]
            
            for test_name, query in complex_queries:
                try:
                    cursor.execute(query)
                    result = cursor.fetchall()
                    print(f"✅ {test_name}: Query executed successfully")
                    self.results['database_tests'][test_name.lower().replace(' ', '_')] = 'PASS'
                    passed_tests += 1
                except Exception as e:
                    print(f"❌ {test_name}: {e}")
                    self.results['database_tests'][test_name.lower().replace(' ', '_')] = f'FAIL_{str(e)[:50]}'
            
            conn.close()
            
            total_tests = len(column_tests) + len(complex_queries)
            success_rate = (passed_tests / total_tests) * 100
            print(f"\n📊 Database Test Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")
            
            return success_rate >= 90
        
        except Exception as e:
            print(f"❌ Database connection error: {e}")
            self.results['database_tests']['connection'] = f'FAIL_{str(e)[:50]}'
            return False
    
    def test_template_rendering(self):
        """Test template rendering and navigation"""
        
        print("\n🎨 TESTING TEMPLATE RENDERING")
        print("=" * 50)
        
        # Test key templates that were enhanced
        template_routes = [
            ('/dashboard', 'Main Dashboard Template'),
            ('/riders/dashboard', 'Riders Dashboard Template'),
            ('/delivery_analytics/', 'Delivery Analytics Template')
        ]
        
        working_templates = 0
        
        for route, description in template_routes:
            try:
                response = requests.get(f"{self.base_url}{route}", timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check for key template elements
                    has_navigation = 'nav-link' in content
                    has_content = len(content) > 1000
                    has_bootstrap = 'bootstrap' in content.lower()
                    
                    if has_navigation and has_content and has_bootstrap:
                        print(f"✅ {description}: Template renders correctly")
                        self.results['template_tests'][route] = 'PASS'
                        working_templates += 1
                    else:
                        print(f"⚠️ {description}: Template renders but may be incomplete")
                        self.results['template_tests'][route] = 'PARTIAL'
                        working_templates += 0.5
                
                elif response.status_code == 302:
                    print(f"🔄 {description}: Redirecting (auth required)")
                    self.results['template_tests'][route] = 'REDIRECT'
                    working_templates += 1
                else:
                    print(f"❌ {description}: Status {response.status_code}")
                    self.results['template_tests'][route] = f'FAIL_{response.status_code}'
            
            except Exception as e:
                print(f"❌ {description}: Error {e}")
                self.results['template_tests'][route] = f'ERROR_{str(e)[:50]}'
        
        success_rate = (working_templates / len(template_routes)) * 100
        print(f"\n📊 Template Success Rate: {success_rate:.1f}% ({working_templates}/{len(template_routes)})")
        
        return success_rate >= 80
    
    def test_analytics_functionality(self):
        """Test analytics and reporting functionality"""
        
        print("\n📊 TESTING ANALYTICS FUNCTIONALITY")
        print("=" * 50)
        
        analytics_routes = [
            ('/delivery_analytics/', 'Delivery Analytics Dashboard'),
            ('/sales_analytics/', 'Sales Analytics Dashboard'),
            ('/finance/comprehensive-reports', 'Finance Comprehensive Reports'),
            ('/advanced_payment/', 'Advanced Payment Analytics')
        ]
        
        working_analytics = 0
        
        for route, description in analytics_routes:
            try:
                response = requests.get(f"{self.base_url}{route}", timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check for analytics-specific elements
                    has_charts = 'chart' in content.lower()
                    has_analytics = 'analytics' in content.lower()
                    has_dashboard = 'dashboard' in content.lower()
                    
                    if has_charts or has_analytics or has_dashboard:
                        print(f"✅ {description}: Analytics features detected")
                        self.results['analytics_tests'][route] = 'PASS'
                        working_analytics += 1
                    else:
                        print(f"⚠️ {description}: Basic functionality only")
                        self.results['analytics_tests'][route] = 'BASIC'
                        working_analytics += 0.5
                
                elif response.status_code == 302:
                    print(f"🔄 {description}: Redirecting (auth required)")
                    self.results['analytics_tests'][route] = 'REDIRECT'
                    working_analytics += 1
                else:
                    print(f"❌ {description}: Status {response.status_code}")
                    self.results['analytics_tests'][route] = f'FAIL_{response.status_code}'
            
            except Exception as e:
                print(f"❌ {description}: Error {e}")
                self.results['analytics_tests'][route] = f'ERROR_{str(e)[:50]}'
        
        success_rate = (working_analytics / len(analytics_routes)) * 100
        print(f"\n📊 Analytics Success Rate: {success_rate:.1f}% ({working_analytics}/{len(analytics_routes)})")
        
        return success_rate >= 75
    
    def generate_comprehensive_report(self):
        """Generate comprehensive verification report"""
        
        print("\n📋 GENERATING COMPREHENSIVE VERIFICATION REPORT")
        print("=" * 70)
        
        # Run all tests
        routing_ok = self.test_critical_routes()
        database_ok = self.test_database_integrity()
        templates_ok = self.test_template_rendering()
        analytics_ok = self.test_analytics_functionality()
        
        # Calculate overall score
        test_results = [routing_ok, database_ok, templates_ok, analytics_ok]
        overall_score = sum(test_results) / len(test_results) * 100
        
        print(f"\n🎯 COMPREHENSIVE VERIFICATION SUMMARY")
        print("=" * 70)
        print(f"Critical Routes: {'✅ PASS' if routing_ok else '❌ FAIL'}")
        print(f"Database Integrity: {'✅ PASS' if database_ok else '❌ FAIL'}")
        print(f"Template Rendering: {'✅ PASS' if templates_ok else '❌ FAIL'}")
        print(f"Analytics Functionality: {'✅ PASS' if analytics_ok else '❌ FAIL'}")
        
        print(f"\n📊 OVERALL SYSTEM SCORE: {overall_score:.1f}%")
        
        if overall_score >= 90:
            self.results['overall_status'] = 'EXCELLENT'
            print(f"\n🎉 SYSTEM STATUS: EXCELLENT")
            print(f"✅ All critical systems operational")
            print(f"✅ Flask routing errors resolved")
            print(f"✅ Database column issues fixed")
            print(f"✅ Enhanced analytics working")
            print(f"✅ Templates rendering correctly")
            print(f"🚀 SYSTEM READY FOR PRODUCTION USE")
        
        elif overall_score >= 75:
            self.results['overall_status'] = 'GOOD'
            print(f"\n✅ SYSTEM STATUS: GOOD")
            print(f"Most systems operational, minor issues may remain")
        
        elif overall_score >= 60:
            self.results['overall_status'] = 'FAIR'
            print(f"\n⚠️ SYSTEM STATUS: FAIR")
            print(f"Core functionality working, some enhancements needed")
        
        else:
            self.results['overall_status'] = 'NEEDS_ATTENTION'
            print(f"\n❌ SYSTEM STATUS: NEEDS ATTENTION")
            print(f"Critical issues require immediate resolution")
        
        print(f"\n💡 NEXT STEPS:")
        print(f"1. Login to test authenticated features")
        print(f"2. Navigate through enhanced analytics dashboards")
        print(f"3. Test rider management functionality")
        print(f"4. Verify all navigation links work correctly")
        print(f"5. Generate reports to test export functionality")
        
        print(f"\n🏁 Verification completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return overall_score >= 75

def main():
    """Main verification execution"""
    
    print("🚀 COMPREHENSIVE MEDIVENT ERP SYSTEM VERIFICATION")
    print("=" * 70)
    print(f"Verification Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("Testing all systems after routing fixes...")
    
    verifier = SystemVerifier()
    success = verifier.generate_comprehensive_report()
    
    return success

if __name__ == "__main__":
    main()
