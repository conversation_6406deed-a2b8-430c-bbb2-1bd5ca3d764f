#!/usr/bin/env python3
"""
Targeted Database Column Fixes
Fix specific missing columns and update SQL queries accordingly
"""

import sqlite3
import os
from datetime import datetime

def fix_database_columns_targeted():
    """Fix missing database columns with targeted approach"""
    
    print("🔧 TARGETED DATABASE COLUMN FIXES")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        fixes_applied = 0
        
        # 1. Fix orders table - add total_amount column
        try:
            cursor.execute("SELECT total_amount FROM orders LIMIT 1")
            print("✅ orders.total_amount already exists")
        except sqlite3.OperationalError:
            print("🔧 Adding total_amount column to orders table")
            cursor.execute("ALTER TABLE orders ADD COLUMN total_amount DECIMAL(10,2)")
            
            # Update total_amount based on order_amount
            cursor.execute("""
                UPDATE orders 
                SET total_amount = COALESCE(order_amount, 0)
                WHERE total_amount IS NULL
            """)
            fixes_applied += 1
            print("✅ Added total_amount column to orders")
        
        # 2. Fix divisions table - add manager_name column
        try:
            cursor.execute("SELECT manager_name FROM divisions LIMIT 1")
            print("✅ divisions.manager_name already exists")
        except sqlite3.OperationalError:
            print("🔧 Adding manager_name column to divisions table")
            cursor.execute("ALTER TABLE divisions ADD COLUMN manager_name VARCHAR(100)")
            
            # Set default manager names based on division name
            cursor.execute("""
                UPDATE divisions 
                SET manager_name = 'Manager - ' || COALESCE(name, 'Division')
                WHERE manager_name IS NULL
            """)
            fixes_applied += 1
            print("✅ Added manager_name column to divisions")
        
        # 3. Fix customers table - add mobile_number column
        try:
            cursor.execute("SELECT mobile_number FROM customers LIMIT 1")
            print("✅ customers.mobile_number already exists")
        except sqlite3.OperationalError:
            print("🔧 Adding mobile_number column to customers table")
            cursor.execute("ALTER TABLE customers ADD COLUMN mobile_number VARCHAR(15)")
            
            # Copy from phone column
            cursor.execute("""
                UPDATE customers 
                SET mobile_number = phone
                WHERE mobile_number IS NULL AND phone IS NOT NULL
            """)
            fixes_applied += 1
            print("✅ Added mobile_number column to customers")
        
        # 4. Fix customers table - add institution_type column
        try:
            cursor.execute("SELECT institution_type FROM customers LIMIT 1")
            print("✅ customers.institution_type already exists")
        except sqlite3.OperationalError:
            print("🔧 Adding institution_type column to customers table")
            cursor.execute("ALTER TABLE customers ADD COLUMN institution_type VARCHAR(50) DEFAULT 'pharmacy'")
            
            # Set default institution types
            cursor.execute("""
                UPDATE customers 
                SET institution_type = CASE 
                    WHEN LOWER(name) LIKE '%hospital%' THEN 'hospital'
                    WHEN LOWER(name) LIKE '%clinic%' THEN 'clinic'
                    WHEN LOWER(name) LIKE '%distributor%' THEN 'distributor'
                    ELSE 'pharmacy'
                END
                WHERE institution_type IS NULL
            """)
            fixes_applied += 1
            print("✅ Added institution_type column to customers")
        
        # Commit all changes
        conn.commit()
        conn.close()
        
        print(f"\n📊 Applied {fixes_applied} database column fixes")
        return fixes_applied
    
    except Exception as e:
        print(f"❌ Error fixing database columns: {e}")
        return 0

def update_sql_queries_in_app():
    """Update problematic SQL queries in app.py"""
    
    print("\n🔧 UPDATING SQL QUERIES IN APP.PY")
    print("=" * 50)
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        fixes_applied = 0
        
        # Fix 1: Replace problematic total_amount references
        if 'no such column: total_amount' in content or 'o.total_amount' in content:
            # Replace o.total_amount with COALESCE to handle missing column
            content = content.replace(
                'SUM(o.total_amount)',
                'SUM(COALESCE(o.total_amount, o.order_amount, 0))'
            )
            content = content.replace(
                'o.total_amount',
                'COALESCE(o.total_amount, o.order_amount, 0) as total_amount'
            )
            fixes_applied += 1
            print("✅ Fixed total_amount references in SQL queries")
        
        # Fix 2: Replace d.manager_name references
        if 'd.manager_name' in content:
            content = content.replace(
                'd.manager_name',
                'COALESCE(d.manager_name, \'Manager - \' || d.name) as manager_name'
            )
            fixes_applied += 1
            print("✅ Fixed manager_name references in SQL queries")
        
        # Fix 3: Add mobile_number handling in customer queries
        if 'customers c' in content and 'mobile_number' not in content:
            # Add mobile_number to customer SELECT statements
            content = content.replace(
                'c.phone',
                'c.phone, COALESCE(c.mobile_number, c.phone) as mobile_number'
            )
            fixes_applied += 1
            print("✅ Added mobile_number handling in customer queries")
        
        # Write back if changes were made
        if content != original_content:
            with open('app.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Applied {fixes_applied} SQL query fixes to app.py")
        else:
            print("ℹ️ No SQL query fixes needed in app.py")
        
        return fixes_applied
    
    except Exception as e:
        print(f"❌ Error updating SQL queries: {e}")
        return 0

def test_fixed_queries():
    """Test the fixed database queries"""
    
    print("\n🧪 TESTING FIXED DATABASE QUERIES")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('instance/medivent.db')
        cursor = conn.cursor()
        
        test_queries = [
            ("Orders total amount", """
                SELECT COUNT(*), SUM(COALESCE(total_amount, order_amount, 0)) as total_revenue
                FROM orders 
                WHERE status != 'Cancelled'
            """),
            ("Divisions with managers", """
                SELECT COUNT(*), 
                       COUNT(CASE WHEN manager_name IS NOT NULL THEN 1 END) as with_managers
                FROM divisions
            """),
            ("Customers with mobile numbers", """
                SELECT COUNT(*), 
                       COUNT(CASE WHEN mobile_number IS NOT NULL THEN 1 END) as with_mobile
                FROM customers
            """),
            ("Customer institution types", """
                SELECT institution_type, COUNT(*) as count
                FROM customers
                WHERE institution_type IS NOT NULL
                GROUP BY institution_type
            """)
        ]
        
        all_passed = True
        
        for test_name, query in test_queries:
            try:
                cursor.execute(query)
                result = cursor.fetchall()
                print(f"✅ {test_name}: {result}")
            except Exception as e:
                print(f"❌ {test_name}: {e}")
                all_passed = False
        
        conn.close()
        return all_passed
    
    except Exception as e:
        print(f"❌ Error testing queries: {e}")
        return False

def test_analytics_routes():
    """Test analytics routes that were having database issues"""
    
    print("\n🧪 TESTING ANALYTICS ROUTES")
    print("=" * 50)
    
    import requests
    
    test_routes = [
        ('/sales_analytics/', 'Sales Analytics Dashboard'),
        ('/sales_analytics/division_ledger', 'Division Ledger'),
        ('/delivery_analytics/', 'Delivery Analytics'),
        ('/finance/comprehensive-reports', 'Finance Comprehensive Reports')
    ]
    
    working_routes = 0
    
    for route, description in test_routes:
        try:
            response = requests.get(f"http://127.0.0.1:3000{route}", timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {route} - {description}: Working")
                working_routes += 1
            else:
                print(f"❌ {route} - {description}: Status {response.status_code}")
        
        except Exception as e:
            print(f"❌ {route} - {description}: Error {e}")
    
    print(f"\n📊 {working_routes}/{len(test_routes)} analytics routes working")
    return working_routes == len(test_routes)

def main():
    """Main execution for targeted database fixes"""
    
    print("🚀 TARGETED DATABASE COLUMN FIXES")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Fix database columns
    columns_fixed = fix_database_columns_targeted()
    
    # Update SQL queries
    queries_updated = update_sql_queries_in_app()
    
    # Test fixed queries
    queries_working = test_fixed_queries()
    
    # Test analytics routes
    routes_working = test_analytics_routes()
    
    print(f"\n🎯 TARGETED FIX SUMMARY")
    print("=" * 70)
    print(f"Database columns fixed: {columns_fixed}")
    print(f"SQL queries updated: {queries_updated}")
    print(f"Database queries working: {'✅ YES' if queries_working else '❌ NO'}")
    print(f"Analytics routes working: {'✅ YES' if routes_working else '❌ NO'}")
    
    success = columns_fixed > 0 and queries_working
    
    if success:
        print(f"\n🎉 DATABASE COLUMN ISSUES FIXED!")
        print(f"✅ Added missing columns to database tables")
        print(f"✅ Updated SQL queries to handle missing data gracefully")
        print(f"✅ Analytics routes should now work without column errors")
    else:
        print(f"\n⚠️ Some database issues may remain")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success

if __name__ == "__main__":
    main()
