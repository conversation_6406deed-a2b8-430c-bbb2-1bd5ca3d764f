from flask import Blueprint, render_template, request, jsonify, g, flash, redirect, url_for
from flask_login import login_required, current_user
from utils.permissions import permission_required
from utils.division_validator import get_division_validator, get_active_divisions_for_forms
from utils.unified_division_manager import get_divisions_for_forms_unified
from utils.product_validator import get_product_validator
import sqlite3
import logging
import traceback
from datetime import datetime

products_bp = Blueprint('products', __name__, template_folder='../templates')

def get_db():
    db = getattr(g, '_database', None)
    if db is None:
        db = g._database = sqlite3.connect('instance/medivent.db')
        db.row_factory = sqlite3.Row
    return db

@products_bp.route('/product_management/')
@permission_required('view_product_management')
def product_management():
    """100% error-proof implementation"""
    
    # Default empty state
    safe_data = {
        'products': [],
        'stats': {'total_products':0, 'low_stock':0, 'categories':0, 'manufacturers':0}
    }

    try:
        # 1. Verify database connection
        try:
            db = get_db()
            cursor = db.cursor()
            
            # 2. Ultra-safe query (works with ANY schema)
            cursor.execute("SELECT * FROM products LIMIT 100")
            raw_products = cursor.fetchall()
            
            # 3. Convert to dicts with fallback values
            products = []
            for row in raw_products:
                try:
                    products.append({
                        'product_id': row['id'] if 'id' in row.keys() else 0,
                        'name': row.get('name', 'Unnamed Product'),
                        'description': row.get('description', ''),
                        'price': float(row.get('unit_price', 0)),
                        'category': row.get('category', 'Uncategorized'),
                        'stock_quantity': int(row.get('stock_quantity', 0))
                    })
                except:
                    continue
            
            # 4. Calculate stats
            safe_data['products'] = products
            safe_data['stats'] = {
                'total_products': len(products),
                'categories': len({p['category'] for p in products}),
                'low_stock': 0,  # Disabled until schema confirmed
                'manufacturers': 0  # Disabled until schema confirmed
            }
            
        except Exception as db_error:
            logging.error(f"Database safe mode: {str(db_error)}")
    
    except Exception as route_error:
        logging.error(f"Route safe mode: {str(route_error)}")
    
    # 5. Guaranteed return
    return render_template('products/index.html', **safe_data)

@products_bp.route('/product_test/')
def product_test():
    """Isolated test endpoint"""

    # Test 1: Basic response
    try:
        return "<h1>Basic HTML Test - THIS SHOULD DISPLAY</h1>"
    except Exception as e:
        return f"<h1>Basic Test FAILED: {str(e)}</h1>"


@products_bp.route('/new', methods=['GET', 'POST'])
@login_required
def new_product():
    """Create a new product with comprehensive division validation"""
    if request.method == 'POST':
        try:
            db = get_db()

            # Get form data
            product_data = {
                'name': request.form.get('name', '').strip(),
                'strength': request.form.get('strength', '').strip(),
                'manufacturer': request.form.get('manufacturer', '').strip(),
                'category': request.form.get('category', '').strip(),
                'unit_of_measure': request.form.get('unit_of_measure', '').strip(),
                'unit_price': request.form.get('unit_price'),
                'min_stock_level': request.form.get('min_stock_level'),
                'description': request.form.get('description', '').strip(),
                'division_id': request.form.get('division_id')
            }

            # Validate product data
            product_validator = get_product_validator(db)
            is_valid, errors = product_validator.validate_product_registration_data(product_data)

            if not is_valid:
                for error in errors:
                    flash(error, 'danger')
                return redirect(url_for('products.new_product'))

            # Generate product ID
            division_id = int(product_data['division_id'])
            product_id = product_validator.generate_product_id(division_id)

            # Begin transaction
            db.execute('BEGIN TRANSACTION')

            # Insert product
            db.execute('''
                INSERT INTO products (
                    product_id, name, strength, manufacturer, category,
                    unit_of_measure, unit_price, min_stock_level, description,
                    division_id, created_at, created_by, updated_at, updated_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                product_id,
                product_data['name'],
                product_data['strength'],
                product_data['manufacturer'],
                product_data['category'],
                product_data['unit_of_measure'],
                float(product_data['unit_price']) if product_data['unit_price'] else 0.0,
                int(product_data['min_stock_level']) if product_data['min_stock_level'] else 10,
                product_data['description'],
                division_id,
                datetime.now(),
                current_user.username,
                datetime.now(),
                current_user.username
            ))

            # Commit transaction
            db.execute('COMMIT')

            flash(f'Product {product_id} created successfully!', 'success')
            return redirect(url_for('products.view_product', product_id=product_id))

        except Exception as e:
            # Rollback on error
            try:
                db.execute('ROLLBACK')
            except:
                pass
            flash(f'Error creating product: {str(e)}', 'danger')
            return redirect(url_for('products.new_product'))

    # GET request - show product form
    try:
        db = get_db()

        # Get active divisions for form using unified manager
        divisions = get_divisions_for_forms_unified(db)

        if not divisions:
            flash('No active divisions found. Please create a division first.', 'warning')
            return redirect(url_for('divisions.index'))

        return render_template('products/new.html', divisions=divisions)

    except Exception as e:
        flash(f'Error loading product form: {str(e)}', 'danger')
        return redirect(url_for('dashboard'))


@products_bp.route('/<product_id>')
@login_required
def view_product(product_id):
    """View product details with division information"""
    try:
        db = get_db()
        product_validator = get_product_validator(db)

        # Validate product exists
        is_valid, product_info = product_validator.validate_product_exists(product_id)

        if not is_valid:
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('products.product_management'))

        # Get full product details
        cursor = db.execute('''
            SELECT p.*, d.name as division_name, d.category as division_category,
                   d.manager as division_manager
            FROM products p
            LEFT JOIN divisions d ON p.division_id = d.division_id
            WHERE p.product_id = ?
        ''', (product_id,))

        product = cursor.fetchone()

        if not product:
            flash(f'Product {product_id} not found', 'danger')
            return redirect(url_for('products.product_management'))

        # Get inventory information
        cursor = db.execute('''
            SELECT COUNT(*) as inventory_entries,
                   COALESCE(SUM(stock_quantity), 0) as total_stock,
                   COALESCE(SUM(allocated_quantity), 0) as allocated_stock,
                   COALESCE(SUM(stock_quantity - allocated_quantity), 0) as available_stock
            FROM inventory
            WHERE product_id = ? AND status = 'active'
        ''', (product_id,))

        inventory_summary = cursor.fetchone()

        return render_template('products/view.html',
                             product=product,
                             inventory_summary=inventory_summary)

    except Exception as e:
        flash(f'Error viewing product: {str(e)}', 'danger')
        return redirect(url_for('products.product_management'))

    # (We'll add more diagnostic steps if this works)
