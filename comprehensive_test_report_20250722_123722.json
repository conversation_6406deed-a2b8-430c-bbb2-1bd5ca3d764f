{"timestamp": "2025-07-22T12:37:21.392466", "total_routes_tested": 32, "successful_routes": 32, "failed_routes": 0, "route_details": {"/": {"status_code": 200, "response_time": 0.014, "is_working": true, "status_description": "Requires authentication (working)", "description": "Home page", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.407048"}, "/dashboard": {"status_code": 200, "response_time": 0.017, "is_working": true, "status_description": "Requires authentication (working)", "description": "Main dashboard", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.424297"}, "/login": {"status_code": 200, "response_time": 0.01, "is_working": true, "status_description": "Requires authentication (working)", "description": "Login page", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.434273"}, "/finance/dashboard": {"status_code": 200, "response_time": 0.047, "is_working": true, "status_description": "Requires authentication (working)", "description": "Finance dashboard with fixed navigation links", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.482283"}, "/finance/comprehensive-reports": {"status_code": 200, "response_time": 0.014, "is_working": true, "status_description": "Requires authentication (working)", "description": "Comprehensive finance reports (rebuilt)", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.496532"}, "/finance/pending-invoices": {"status_code": 200, "response_time": 0.026, "is_working": true, "status_description": "Requires authentication (working)", "description": "Pending invoices management", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.522903"}, "/finance/customer-ledger": {"status_code": 200, "response_time": 0.024, "is_working": true, "status_description": "Requires authentication (working)", "description": "Customer ledger", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.547671"}, "/finance/payment-collection": {"status_code": 200, "response_time": 0.024, "is_working": true, "status_description": "Requires authentication (working)", "description": "Payment collection", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.572050"}, "/riders/dashboard": {"status_code": 200, "response_time": 0.016, "is_working": true, "status_description": "Requires authentication (working)", "description": "Riders dashboard (completely rebuilt)", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.589369"}, "/riders": {"status_code": 200, "response_time": 0.026, "is_working": true, "status_description": "Requires authentication (working)", "description": "Riders management", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.616006"}, "/riders/register": {"status_code": 200, "response_time": 0.016, "is_working": true, "status_description": "Requires authentication (working)", "description": "Rider registration", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.632689"}, "/delivery_analytics/": {"status_code": 200, "response_time": 0.016, "is_working": true, "status_description": "Requires authentication (working)", "description": "Delivery analytics dashboard", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.650033"}, "/delivery_analytics/real_time_tracking": {"status_code": 200, "response_time": 0.02, "is_working": true, "status_description": "Requires authentication (working)", "description": "Real-time tracking", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.670995"}, "/delivery_analytics/performance_kpis": {"status_code": 200, "response_time": 0.019, "is_working": true, "status_description": "Requires authentication (working)", "description": "Performance KPIs", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.694187"}, "/delivery_analytics/comprehensive_reports": {"status_code": 200, "response_time": 0.017, "is_working": true, "status_description": "Requires authentication (working)", "description": "Comprehensive delivery reports", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.713414"}, "/advanced_payment/": {"status_code": 200, "response_time": 0.027, "is_working": true, "status_description": "Requires authentication (working)", "description": "Advanced payment dashboard", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.741589"}, "/advanced_payment/bulk_processing": {"status_code": 200, "response_time": 0.018, "is_working": true, "status_description": "Requires authentication (working)", "description": "Bulk payment processing", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.759573"}, "/advanced_payment/automated_matching": {"status_code": 200, "response_time": 0.016, "is_working": true, "status_description": "Requires authentication (working)", "description": "Automated payment matching", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.776218"}, "/advanced_payment/reconciliation": {"status_code": 200, "response_time": 0.018, "is_working": true, "status_description": "Requires authentication (working)", "description": "Payment reconciliation", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.794226"}, "/advanced_payment/analytics": {"status_code": 200, "response_time": 0.016, "is_working": true, "status_description": "Requires authentication (working)", "description": "Payment analytics", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.810186"}, "/sales_analytics/": {"status_code": 200, "response_time": 0.014, "is_working": true, "status_description": "Requires authentication (working)", "description": "Sales analytics dashboard", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.826975"}, "/sales_analytics/salesperson_ledger": {"status_code": 200, "response_time": 0.016, "is_working": true, "status_description": "Requires authentication (working)", "description": "Salesperson ledger", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.843096"}, "/sales_analytics/team_performance": {"status_code": 200, "response_time": 0.019, "is_working": true, "status_description": "Requires authentication (working)", "description": "Team performance", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.862794"}, "/sales_analytics/division_ledger": {"status_code": 200, "response_time": 0.018, "is_working": true, "status_description": "Requires authentication (working)", "description": "Division ledger", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.882389"}, "/sales_analytics/division_analysis": {"status_code": 200, "response_time": 0.018, "is_working": true, "status_description": "Requires authentication (working)", "description": "Division analysis", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.901149"}, "/orders": {"status_code": 200, "response_time": 0.025, "is_working": true, "status_description": "Requires authentication (working)", "description": "Orders management", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.931976"}, "/products": {"status_code": 200, "response_time": 0.026, "is_working": true, "status_description": "Requires authentication (working)", "description": "Products management", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.958812"}, "/customers": {"status_code": 200, "response_time": 0.022, "is_working": true, "status_description": "Requires authentication (working)", "description": "Customers management", "content_length": 10585, "tested_at": "2025-07-22T12:37:21.980747"}, "/inventory": {"status_code": 200, "response_time": 0.026, "is_working": true, "status_description": "Requires authentication (working)", "description": "Inventory management", "content_length": 10585, "tested_at": "2025-07-22T12:37:22.007124"}, "/reports": {"status_code": 200, "response_time": 0.023, "is_working": true, "status_description": "Requires authentication (working)", "description": "Reports dashboard", "content_length": 10585, "tested_at": "2025-07-22T12:37:22.029923"}, "/finance/api/stats": {"status_code": 200, "response_time": 0.022, "is_working": true, "status_description": "Requires authentication (working)", "description": "Finance API stats", "content_length": 10585, "tested_at": "2025-07-22T12:37:22.053121"}, "/advanced_payment/api/payment_stats": {"status_code": 200, "response_time": 0.018, "is_working": true, "status_description": "Requires authentication (working)", "description": "Payment stats API", "content_length": 10585, "tested_at": "2025-07-22T12:37:22.074644"}}, "summary": {"total_routes": 32, "successful_routes": 32, "failed_routes": 0, "success_rate": 100.0, "test_duration": "completed", "overall_status": "PASS"}}