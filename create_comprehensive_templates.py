#!/usr/bin/env python3
"""
Create Comprehensive Templates for All New Routes
Ensure all new analytics and management routes have proper templates
"""

import os
from datetime import datetime

def create_delivery_analytics_templates():
    """Create comprehensive delivery analytics templates"""
    
    print("🎨 CREATING DELIVERY ANALYTICS TEMPLATES")
    print("=" * 50)
    
    templates = {
        'templates/delivery_analytics/real_time_tracking.html': '''{% extends 'base.html' %}

{% block title %}Real-time Delivery Tracking - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-map-marker-alt text-primary"></i> Real-time Delivery Tracking
        </h1>
        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Active Deliveries</h6>
                </div>
                <div class="card-body">
                    {% if active_deliveries %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Address</th>
                                        <th>Status</th>
                                        <th>Rider</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for delivery in active_deliveries %}
                                    <tr>
                                        <td>{{ delivery.order_id }}</td>
                                        <td>{{ delivery.customer_name }}</td>
                                        <td>{{ delivery.delivery_address }}</td>
                                        <td><span class="badge badge-info">{{ delivery.status }}</span></td>
                                        <td>{{ delivery.rider_name or 'Not Assigned' }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No active deliveries at the moment.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">Delivery Map</h6>
                </div>
                <div class="card-body">
                    <div id="delivery-map" style="height: 300px; background: #f8f9fc; border: 1px solid #e3e6f0; border-radius: 5px;">
                        <div class="d-flex align-items-center justify-content-center h-100">
                            <div class="text-center">
                                <i class="fas fa-map fa-3x text-gray-300 mb-3"></i>
                                <p class="text-gray-500">Interactive delivery map will be displayed here</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}''',

        'templates/delivery_analytics/performance_kpis.html': '''{% extends 'base.html' %}

{% block title %}Delivery Performance KPIs - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-bar text-primary"></i> Delivery Performance KPIs
        </h1>
        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <div class="row">
        <div class="col-md-3">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Success Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if success_rate %}
                                    {{ "%.1f"|format(success_rate.delivered_orders / success_rate.total_orders * 100) }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">On-time Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if ontime_rate %}
                                    {{ "%.1f"|format(ontime_rate.ontime_delivered / ontime_rate.total_delivered * 100) }}%
                                {% else %}
                                    0%
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Avg Cost</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ avg_delivery_cost or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Active Riders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ rider_efficiency|length or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-motorcycle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Rider Efficiency</h6>
                </div>
                <div class="card-body">
                    {% if rider_efficiency %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Rider</th>
                                        <th>Total Assignments</th>
                                        <th>Completed</th>
                                        <th>Success Rate</th>
                                        <th>Avg Rating</th>
                                        <th>Avg Delivery Time</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for rider in rider_efficiency %}
                                    <tr>
                                        <td>{{ rider.name }}</td>
                                        <td>{{ rider.total_assignments }}</td>
                                        <td>{{ rider.completed_deliveries }}</td>
                                        <td>{{ "%.1f"|format(rider.completed_deliveries / rider.total_assignments * 100) }}%</td>
                                        <td>{{ "%.1f"|format(rider.avg_rating or 0) }}</td>
                                        <td>{{ "%.1f"|format(rider.avg_delivery_hours or 0) }}h</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No rider performance data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}''',

        'templates/delivery_analytics/comprehensive_reports.html': '''{% extends 'base.html' %}

{% block title %}Comprehensive Delivery Reports - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-file-alt text-primary"></i> Comprehensive Delivery Reports
        </h1>
        <a href="{{ url_for('delivery_analytics.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Analytics
        </a>
    </div>

    <div class="row">
        {% for report in report_types %}
        <div class="col-md-4 mb-4">
            <div class="card shadow h-100">
                <div class="card-body text-center">
                    <i class="fas fa-{{ report.icon }} fa-3x text-primary mb-3"></i>
                    <h5 class="card-title">{{ report.name }}</h5>
                    <p class="card-text">{{ report.description }}</p>
                    <button class="btn btn-primary" onclick="generateReport('{{ report.id }}')">
                        <i class="fas fa-download"></i> Generate Report
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

<script>
function generateReport(reportType) {
    alert('Generating ' + reportType + ' report...');
    // Implementation for report generation
}
</script>
{% endblock %}'''
    }
    
    created_count = 0
    for template_path, content in templates.items():
        os.makedirs(os.path.dirname(template_path), exist_ok=True)
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created: {template_path}")
        created_count += 1
    
    return created_count

def create_advanced_payment_templates():
    """Create advanced payment management templates"""
    
    print("\n💰 CREATING ADVANCED PAYMENT TEMPLATES")
    print("=" * 50)
    
    templates = {
        'templates/advanced_payment/bulk_processing.html': '''{% extends 'base.html' %}

{% block title %}Bulk Payment Processing - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-layer-group text-primary"></i> Bulk Payment Processing
        </h1>
        <a href="{{ url_for('advanced_payment.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Payment Management
        </a>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Customers with Outstanding Balances</h6>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('advanced_payment.process_bulk_payment') }}">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label>Payment Amount</label>
                                <input type="number" class="form-control" name="payment_amount" step="0.01" required>
                            </div>
                            <div class="col-md-3">
                                <label>Payment Method</label>
                                <select class="form-control" name="payment_method">
                                    <option value="Cash">Cash</option>
                                    <option value="Bank Transfer">Bank Transfer</option>
                                    <option value="Cheque">Cheque</option>
                                    <option value="Credit Card">Credit Card</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label>Payment Date</label>
                                <input type="date" class="form-control" name="payment_date" value="{{ now.strftime('%Y-%m-%d') }}">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary form-control">
                                    <i class="fas fa-credit-card"></i> Process Bulk Payment
                                </button>
                            </div>
                        </div>
                        
                        {% if customers_with_balance %}
                            <div class="table-responsive">
                                <table class="table table-bordered">
                                    <thead>
                                        <tr>
                                            <th><input type="checkbox" id="select-all"></th>
                                            <th>Customer</th>
                                            <th>Email</th>
                                            <th>Outstanding Balance</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for customer in customers_with_balance %}
                                        <tr>
                                            <td><input type="checkbox" name="customer_ids" value="{{ customer.customer_id }}"></td>
                                            <td>{{ customer.name }}</td>
                                            <td>{{ customer.email }}</td>
                                            <td>₹{{ "%.2f"|format(customer.outstanding_balance) }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> No customers with outstanding balances found.
                            </div>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('select-all').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('input[name="customer_ids"]');
    checkboxes.forEach(checkbox => checkbox.checked = this.checked);
});
</script>
{% endblock %}''',

        'templates/advanced_payment/automated_matching.html': '''{% extends 'base.html' %}

{% block title %}Automated Payment Matching - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-link text-primary"></i> Automated Payment Matching
        </h1>
        <a href="{{ url_for('advanced_payment.dashboard') }}" class="btn btn-secondary shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Payment Management
        </a>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-warning">Unmatched Payments</h6>
                </div>
                <div class="card-body">
                    {% if unmatched_payments %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Payment ID</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                        <th>Method</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in unmatched_payments %}
                                    <tr>
                                        <td>{{ payment.payment_id }}</td>
                                        <td>₹{{ "%.2f"|format(payment.amount) }}</td>
                                        <td>{{ payment.payment_date }}</td>
                                        <td>{{ payment.payment_method }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-primary" onclick="matchPayment({{ payment.payment_id }})">
                                                <i class="fas fa-link"></i> Match
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> All payments are matched!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">Unmatched Orders</h6>
                </div>
                <div class="card-body">
                    {% if unmatched_orders %}
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Outstanding</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for order in unmatched_orders %}
                                    <tr>
                                        <td>{{ order.order_id }}</td>
                                        <td>{{ order.customer_name }}</td>
                                        <td>₹{{ "%.2f"|format(order.outstanding) }}</td>
                                        <td>
                                            <button class="btn btn-sm btn-success" onclick="matchOrder({{ order.order_id }})">
                                                <i class="fas fa-link"></i> Match
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> All orders have payments!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function matchPayment(paymentId) {
    alert('Matching payment ' + paymentId + '...');
    // Implementation for payment matching
}

function matchOrder(orderId) {
    alert('Matching order ' + orderId + '...');
    // Implementation for order matching
}
</script>
{% endblock %}'''
    }
    
    created_count = 0
    for template_path, content in templates.items():
        os.makedirs(os.path.dirname(template_path), exist_ok=True)
        with open(template_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"✅ Created: {template_path}")
        created_count += 1
    
    return created_count

def main():
    """Main execution"""
    print("🚀 CREATING COMPREHENSIVE TEMPLATES")
    print("=" * 70)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Create delivery analytics templates
    delivery_templates = create_delivery_analytics_templates()
    
    # Create advanced payment templates
    payment_templates = create_advanced_payment_templates()
    
    total_templates = delivery_templates + payment_templates
    
    print(f"\n🎯 TEMPLATE CREATION SUMMARY")
    print("=" * 70)
    print(f"Delivery Analytics templates: {delivery_templates}")
    print(f"Advanced Payment templates: {payment_templates}")
    print(f"Total templates created: {total_templates}")
    
    if total_templates > 0:
        print(f"\n✅ All comprehensive templates created successfully!")
        print(f"🎨 Templates follow established design patterns")
        print(f"📱 Responsive design implemented")
        print(f"🔗 Proper navigation included")
    
    print(f"🏁 Completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
