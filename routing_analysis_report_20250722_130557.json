{"route_functions": {"app.py": {"favicon": {"route_path": "/favicon.ico", "file": "app.py"}, "setup_real_data": {"route_path": "/setup-real-data", "file": "app.py"}, "comprehensive_system_test": {"route_path": "/comprehensive-system-test", "file": "app.py"}, "test_customer_ledger_buttons": {"route_path": "/test-customer-ledger-buttons", "file": "app.py"}, "index": {"route_path": "/", "file": "app.py"}, "test_route": {"route_path": "/test", "file": "app.py"}, "debug_routes": {"route_path": "/debug-routes", "file": "app.py"}, "orders_fixed": {"route_path": "/orders-fixed", "file": "app.py"}, "serve_image": {"route_path": "/api/image/<file_id>", "file": "app.py"}, "api_customer_search": {"route_path": "/api/customer-search", "file": "app.py"}}, "routes/advanced_payment.py": {}, "routes/auth.py": {}, "routes/delivery_analytics.py": {}, "routes/divisions_modern.py": {"index": {"route_path": "/", "file": "routes/divisions_modern.py"}, "api_list": {"route_path": "/api/list", "file": "routes/divisions_modern.py"}, "view": {"route_path": "/<division_id>", "file": "routes/divisions_modern.py"}, "analytics": {"route_path": "/analytics", "file": "routes/divisions_modern.py"}, "export": {"route_path": "/export", "file": "routes/divisions_modern.py"}}, "routes/inventory.py": {}, "routes/modern_riders.py": {}, "routes/notifications.py": {}, "routes/orders.py": {}, "routes/orders_enhanced.py": {}, "routes/orders_minimal.py": {}, "routes/permission_api.py": {}, "routes/products.py": {"product_test": {"route_path": "/product_test/", "file": "routes/products.py"}}, "routes/sales_analytics.py": {}, "routes/tracking.py": {"track_form": {"route_path": "/track", "file": "routes/tracking.py"}, "track_number": {"route_path": "/track/<tracking_number>", "file": "routes/tracking.py"}}, "routes/users.py": {}}, "template_references": {"templates\\404.html": {"dashboard": 1}, "templates\\500.html": {"dashboard": 1}, "templates\\base.html": {"dashboard": 5, "new_order": 1, "orders": 5, "workflow": 4, "products": 2, "update_product_selection": 1, "warehouses": 1, "inventory": 2, "delivery_challans": 1, "warehouse_reports": 1, "riders": 1, "register_rider": 1, "rider_delivery_routes": 1, "rider_performance": 1, "riders_dashboard": 1, "delivery_analytics.dashboard": 1, "delivery_analytics.real_time_tracking": 1, "delivery_analytics.performance_kpis": 1, "delivery_analytics.comprehensive_reports": 1, "delivery_analytics.individual_rider_reports": 1, "delivery_analytics.customer_delivery_tracking": 1, "delivery_analytics.delivery_history": 1, "delivery_analytics.customer_satisfaction": 1, "admin_rider_tracking": 1, "tracking.track_form": 1, "daily_sales_report_new": 1, "weekly_sales_report_new": 1, "monthly_sales_report_new": 1, "custom_date_range_report": 1, "sales_by_agent_report": 2, "product_performance_report": 1, "inventory_report": 1, "reports": 2, "finance_dashboard": 1, "finance_pending_invoices_management": 1, "finance_customer_ledger": 1, "finance_payment_collection": 1, "financial_reports": 1, "comprehensive_finance_reports": 1, "sales_team_dashboard": 1, "sales_division_detail": 4, "users_index": 1, "users_new": 1, "users_permissions": 2, "settings": 3, "divisions.index": 1, "divisions.analytics": 1, "divisions.export": 1, "customers": 6, "import_data": 1, "track_order_page": 1, "logout": 1, "static": 2}, "templates\\dashboard.html": {"orders": 2, "workflow": 12, "delivery_analytics.dashboard": 1, "delivery_analytics.real_time_tracking": 1, "advanced_payment.dashboard": 1, "advanced_payment.bulk_processing": 1, "sales_analytics.dashboard": 1, "sales_analytics.team_performance": 1, "finance_dashboard": 1, "comprehensive_finance_reports": 1, "riders_dashboard": 1, "riders": 1, "new_order": 1}, "templates\\import_data.html": {"import_data": 1}, "templates\\login.html": {"login": 1}, "templates\\settings.html": {"settings": 1, "backup_database": 1}, "templates\\test_notifications.html": {}, "templates\\track_order.html": {}, "templates\\admin\\bulk_operations.html": {"dashboard": 1, "export_orders_excel": 1, "export_inventory_excel": 1}, "templates\\admin\\data_export.html": {"dashboard": 1, "export_orders_excel": 1, "export_inventory_excel": 1}, "templates\\admin\\feedback.html": {}, "templates\\admin\\rider_tracking.html": {"warehouses": 1, "dashboard": 1}, "templates\\admin\\settings.html": {"dashboard": 1, "update_settings": 1, "backup_database": 1}, "templates\\admin\\system_health.html": {"dashboard": 1}, "templates\\advanced_payment\\automated_matching.html": {"advanced_payment.dashboard": 1}, "templates\\advanced_payment\\bulk_processing.html": {"advanced_payment.dashboard": 1, "advanced_payment.process_bulk_payment": 1}, "templates\\advanced_payment\\dashboard.html": {"dashboard": 1}, "templates\\auth\\login.html": {"login": 1}, "templates\\auth\\profile.html": {"dashboard": 1}, "templates\\components\\intelligent_search.html": {}, "templates\\customers\\add_customer.html": {"customers": 2, "add_customer": 1}, "templates\\customers\\all_orders.html": {"customers": 7}, "templates\\customers\\by_type.html": {"customers": 9}, "templates\\customers\\customers_list.html": {"customers": 9}, "templates\\customers\\index.html": {"customers": 9, "export_customers": 1, "add_customer": 1}, "templates\\customers\\orders.html": {"customers": 6, "view_order_history": 1, "view_invoice": 1, "view_challan": 1}, "templates\\customers\\pricing.html": {"customers": 6}, "templates\\customers\\select_customer.html": {"customers": 2}, "templates\\customers\\set_pricing.html": {"customers": 6}, "templates\\dashboard\\ceo.html": {"orders": 2, "workflow": 9}, "templates\\delivery_analytics\\comprehensive_reports.html": {"delivery_analytics.dashboard": 1}, "templates\\delivery_analytics\\dashboard.html": {"delivery_analytics.real_time_tracking": 1, "delivery_analytics.comprehensive_reports": 1, "dashboard": 1}, "templates\\delivery_analytics\\individual_rider_reports.html": {"delivery_analytics.dashboard": 1, "delivery_analytics.individual_rider_reports": 1}, "templates\\delivery_analytics\\performance_kpis.html": {"delivery_analytics.dashboard": 1}, "templates\\delivery_analytics\\real_time_tracking.html": {"delivery_analytics.dashboard": 1}, "templates\\delivery_challans\\index.html": {"orders": 1, "view_order_history": 1, "view_challan": 1}, "templates\\divisions\\analytics.html": {"divisions.index": 2, "divisions.export": 1}, "templates\\divisions\\create.html": {"divisions.index": 3, "divisions.create": 1}, "templates\\divisions\\edit.html": {"divisions.view": 3, "divisions.index": 2, "divisions.edit": 1, "divisions.delete": 1}, "templates\\divisions\\index.html": {"divisions.analytics": 1, "divisions.export": 1}, "templates\\divisions\\modern_index.html": {"divisions.analytics": 1, "divisions.view": 2, "divisions.edit": 2, "divisions.create": 1, "divisions.delete": 1, "divisions.api_list": 1, "divisions.export": 1}, "templates\\divisions\\view.html": {"divisions.edit": 2, "divisions.index": 2, "divisions.export": 1, "divisions.analytics": 1, "divisions.delete": 1}, "templates\\feedback\\index.html": {"feedback": 2}, "templates\\finance\\accounts_payable.html": {}, "templates\\finance\\accounts_receivable.html": {}, "templates\\finance\\advanced_analytics.html": {}, "templates\\finance\\aging_analysis.html": {"finance_aging_analysis": 1}, "templates\\finance\\comprehensive_reports.html": {"advanced_payment.analytics": 1, "finance_dashboard": 1, "advanced_payment.bulk_processing": 1}, "templates\\finance\\customer_ledger.html": {"finance_dashboard": 1}, "templates\\finance\\customer_ledger_enhanced.html": {"finance_customer_ledger": 1}, "templates\\finance\\duplicate_detection.html": {}, "templates\\finance\\financial_reports.html": {}, "templates\\finance\\invoice_generation_enhanced.html": {"finance_pending_invoices": 1}, "templates\\finance\\invoice_generation_new.html": {}, "templates\\finance\\modern_dashboard.html": {"finance_pending_invoices_management": 1, "finance_customer_ledger": 1, "finance_payment_collection": 1, "financial_reports": 1, "accounts_receivable": 1, "accounts_payable": 1, "duplicate_order_detection": 1, "advanced_payment.automated_matching": 1, "sales_analytics.salesperson_ledger": 1, "sales_analytics.division_ledger": 1, "comprehensive_finance_reports": 1, "advanced_financial_analytics": 1, "finance_api_stats": 1}, "templates\\finance\\payment_attachments.html": {"finance_payment_collection": 1, "add_payment_attachment": 1, "serve_uploaded_file": 3, "remove_payment_attachment": 1}, "templates\\finance\\payment_collection.html": {"finance_dashboard": 2, "finance_process_payment": 1}, "templates\\finance\\payment_collection_enhanced.html": {"finance_process_payment": 1}, "templates\\finance\\payment_collection_new.html": {}, "templates\\finance\\pending_invoices.html": {"finance_dashboard": 2, "finance_process_payment": 1}, "templates\\inventory\\add.html": {"inventory": 3, "inventory_add": 1, "products": 1, "warehouse": 1}, "templates\\inventory\\history.html": {"inventory": 1}, "templates\\inventory\\index.html": {"inventory": 1, "export_inventory_excel": 1, "new_stock": 2, "transfer_stock": 2, "toggle_inventory_status": 1, "inventory_history": 1}, "templates\\inventory\\inventory_view.html": {"add_inventory": 1, "inventory_transfer": 2, "products": 2, "inventory": 8, "edit_inventory": 1, "inventory_history": 1}, "templates\\inventory\\new.html": {"new_stock": 1, "inventory": 1}, "templates\\inventory\\transfer.html": {"transfer_stock": 1, "inventory": 1}, "templates\\notifications\\index.html": {}, "templates\\notifications\\notification_center.html": {}, "templates\\notifications\\test_notifications.html": {}, "templates\\orders\\allocate_inventory.html": {"auto_allocate_inventory": 1, "release_allocation": 2, "manual_allocate_inventory": 1, "orders": 1}, "templates\\orders\\approval.html": {"orders.index": 1}, "templates\\orders\\challan.html": {"display_challan": 1, "workflow": 1}, "templates\\orders\\deliver.html": {"deliver_order": 1, "workflow": 1}, "templates\\orders\\dispatch.html": {"dispatch_order": 1, "workflow": 1}, "templates\\orders\\duplicate_detection.html": {}, "templates\\orders\\enhanced_search.html": {"orders_enhanced.advanced_search": 5, "orders.view_order": 1, "orders_enhanced.update_order": 1, "orders_enhanced.order_history": 1}, "templates\\orders\\enhanced_update.html": {"orders.view_order": 2, "orders_enhanced.order_history": 1}, "templates\\orders\\generate_challan.html": {"workflow": 1}, "templates\\orders\\generate_invoice.html": {"finance_invoices": 1, "finance_pending_invoices": 1}, "templates\\orders\\history.html": {"orders": 1, "view_challan": 1}, "templates\\orders\\index.html": {"orders.new_order": 1, "new_order": 1}, "templates\\orders\\invoice.html": {"display_invoice": 1, "workflow": 1}, "templates\\orders\\new.html": {"orders.new_order": 1, "orders.index": 1}, "templates\\orders\\new_order.html": {"create_order_with_workflow": 1, "orders": 1}, "templates\\orders\\orders_list.html": {"new_order": 1, "workflow": 1, "orders": 8, "view_order": 1, "update_order": 1, "allocate_inventory_page": 1, "generate_challan": 1, "view_invoice": 1, "view_order_history": 1}, "templates\\orders\\order_details.html": {"orders": 1, "update_order": 1, "allocate_inventory_page": 1, "generate_challan": 1, "view_invoice": 1}, "templates\\orders\\order_history.html": {}, "templates\\orders\\search_results.html": {"orders": 4, "view_order": 1, "update_order": 1, "view_invoice": 1}, "templates\\orders\\update.html": {"update_order": 1, "orders": 1}, "templates\\orders\\view.html": {"orders.index": 2, "orders.approve_order": 1, "finance_generate_invoice": 1, "generate_challan": 1, "orders.update_order": 1, "finance.view_invoice": 1}, "templates\\orders\\workflow.html": {"workflow": 2, "orders": 1, "view_order_history": 1, "approve_order": 1, "generate_challan": 1, "view_challan": 1, "display_challan": 1, "finance_generate_invoice": 1, "view_invoice": 1, "display_invoice": 1, "dispatch_order": 1, "deliver_order": 1, "update_order": 1}, "templates\\orders\\workflow_status.html": {"approve_order_manual": 1, "reject_order_manual": 1, "orders": 1, "view_order": 1, "allocate_inventory_page": 1, "generate_challan": 1}, "templates\\organization\\by_division.html": {"organization": 5}, "templates\\organization\\chart.html": {"organization": 4}, "templates\\organization\\divisions.html": {"organization": 6}, "templates\\organization\\hierarchy.html": {"organization": 1}, "templates\\organization\\index.html": {}, "templates\\organization\\members.html": {"organization": 4}, "templates\\organization\\member_details.html": {"organization": 1}, "templates\\organization\\performance.html": {"organization": 1}, "templates\\organization\\team_by_division.html": {"organization": 6}, "templates\\products\\add_product_enhanced.html": {"view_all_products": 2, "products": 1}, "templates\\products\\index.html": {"new_product": 2, "inventory": 1, "products": 9, "update_product": 1}, "templates\\products\\new.html": {"products": 2, "new_product": 1}, "templates\\products\\products_list.html": {"new_product": 1, "inventory": 2, "products": 10, "update_product": 1, "inventory_add": 1, "reports": 1}, "templates\\products\\product_details.html": {"view_order": 1, "products": 1, "update_product": 1, "inventory_add": 1, "inventory": 1, "reports": 1}, "templates\\products\\product_management.html": {"new_product": 2, "inventory": 1, "product_management": 12, "view_product": 1, "update_product": 1}, "templates\\products\\update.html": {"products": 1, "view_product": 2, "update_product": 1, "update_product_selection": 1}, "templates\\products\\update_selection.html": {"update_product": 1, "update_product_selection": 4}, "templates\\products\\view_all_products.html": {"view_all_products": 3}, "templates\\reports\\agent_performance_ranking.html": {"reports": 1}, "templates\\reports\\customer_purchase_history.html": {"reports": 1, "customer_purchase_history": 1, "view_order": 1, "generate_invoice": 1}, "templates\\reports\\custom_date_range.html": {"reports": 1, "custom_date_range_report": 1}, "templates\\reports\\daily_sales.html": {}, "templates\\reports\\delivery_performance.html": {"reports": 1}, "templates\\reports\\division_performance.html": {"reports": 1, "division_performance_chart": 1}, "templates\\reports\\division_revenue_comparison.html": {"reports": 1}, "templates\\reports\\expiry.html": {"reports": 1, "expiry_report": 1}, "templates\\reports\\expiry_report.html": {"reports": 1}, "templates\\reports\\index.html": {"low_stock_report": 1, "expiry_report": 1, "stock_movement_report": 1, "inventory_report": 1, "daily_sales_report_new": 1, "weekly_sales_report": 1, "monthly_sales_report_new": 1, "custom_date_range_report": 1, "sales_by_agent_report": 1, "product_performance_report": 1, "customer_purchase_history": 1, "delivery_performance_report": 1, "financial_summary_report": 1, "sales_team_performance": 1, "warehouse_inventory_report": 1, "batch_tracking_report": 1, "warehouse_performance_report": 1, "sales_analytics": 1, "inventory_analytics": 1, "division_performance_chart": 1, "trend_analysis": 1}, "templates\\reports\\inventory_analytics.html": {"reports": 1, "inventory_analytics": 1}, "templates\\reports\\low_stock.html": {"reports": 1, "low_stock_report": 1, "new_stock": 2}, "templates\\reports\\low_stock_alert.html": {}, "templates\\reports\\monthly_sales.html": {}, "templates\\reports\\monthly_summary.html": {}, "templates\\reports\\product_inventory.html": {"reports": 1, "product_inventory_report": 1}, "templates\\reports\\product_performance.html": {"reports": 1, "product_performance_report": 1}, "templates\\reports\\reports_base.html": {"static": 1}, "templates\\reports\\reports_dashboard.html": {"daily_sales_report_new": 2, "weekly_sales_report_new": 2, "monthly_sales_report_new": 2, "inventory_report": 2, "product_performance_report": 2, "sales_by_agent_report": 2, "custom_date_range_report": 1, "inventory": 2, "reports": 1, "financial_reports": 1, "finance_pending_invoices": 1, "finance_customer_ledger": 1, "finance_payment_collection": 1, "customers": 2}, "templates\\reports\\sales_analytics.html": {"reports": 1, "sales_analytics": 1}, "templates\\reports\\sales_by_agent.html": {"reports": 1, "sales_by_agent_report": 1}, "templates\\reports\\sales_team_performance.html": {"reports": 1}, "templates\\reports\\stock_movements.html": {"reports": 1, "stock_movement_report": 1}, "templates\\reports\\top_customers_revenue.html": {"reports": 1}, "templates\\reports\\trend_analysis.html": {"reports": 1}, "templates\\reports\\warehouse_inventory.html": {"reports": 1, "export_warehouse_inventory": 2, "warehouse_inventory_report": 1}, "templates\\reports\\warehouse_performance.html": {"reports": 1, "export_warehouse_performance": 2, "warehouse_performance_report": 1}, "templates\\reports\\warehouse_utilization.html": {"warehouse_reports": 1}, "templates\\reports\\weekly_sales.html": {"reports": 1}, "templates\\reports\\advanced\\customer_history.html": {"reports": 1, "customer_purchase_history": 1}, "templates\\reports\\advanced\\delivery-performance.html": {"reports": 1, "delivery_performance_report": 1}, "templates\\reports\\advanced\\delivery_performance.html": {"reports": 1, "delivery_performance_report": 1}, "templates\\reports\\advanced\\financial_summary.html": {"reports": 1, "financial_summary_report": 4}, "templates\\reports\\advanced\\index.html": {"reports": 1, "financial_summary_report": 1, "sales_team_performance": 1, "delivery_performance_report": 1}, "templates\\reports\\advanced\\product_performance.html": {"static": 2, "export_product_performance_pdf": 1, "reports": 1, "product_performance_report": 3}, "templates\\reports\\advanced\\sales_team.html": {"static": 4, "export_sales_team_pdf": 1, "reports": 1, "sales_team_performance": 3}, "templates\\reports\\sales\\by_agent.html": {"reports": 1, "sales_by_agent_report": 1}, "templates\\reports\\sales\\custom.html": {"reports": 1, "custom_date_range_report": 1}, "templates\\reports\\sales\\daily.html": {"reports": 1, "daily_sales_report_new": 1}, "templates\\reports\\sales\\index.html": {"reports_dashboard": 1, "daily_sales_report": 1, "weekly_sales_report": 1, "monthly_sales_report": 1, "sales_by_agent_report": 1, "product_performance_report": 1, "top_customers_revenue_report": 1}, "templates\\reports\\sales\\monthly.html": {"reports": 1, "monthly_sales_report_new": 1}, "templates\\reports\\sales\\weekly.html": {"reports": 1, "weekly_sales_report": 1}, "templates\\rider\\professional_dashboard.html": {"riders_orders": 1, "dashboard": 1}, "templates\\riders\\dashboard.html": {"riders_orders": 1, "rider_delivery_routes": 1, "rider_performance": 1}, "templates\\riders\\delivery_routes.html": {"riders": 1}, "templates\\riders\\edit.html": {"view_rider": 3, "edit_rider": 1, "riders": 1}, "templates\\riders\\index.html": {"register_rider": 1}, "templates\\riders\\modern_dashboard.html": {"riders.tracking": 2, "riders.analytics": 1}, "templates\\riders\\modern_tracking.html": {}, "templates\\riders\\orders.html": {"riders_orders": 3, "update_delivery_status": 1, "riders_dashboard": 1, "workflow": 1, "claim_pickup": 1}, "templates\\riders\\performance.html": {}, "templates\\riders\\professional_dashboard.html": {"dashboard": 1, "delivery_analytics.dashboard": 1, "riders_management": 1, "delivery_analytics.real_time_tracking": 1, "delivery_analytics.performance_kpis": 1}, "templates\\riders\\register.html": {"register_rider": 1}, "templates\\riders\\register_simple.html": {"riders": 1, "register_rider": 1}, "templates\\riders\\view.html": {"riders": 1, "edit_rider": 2}, "templates\\sales_analytics\\dashboard.html": {"dashboard": 1}, "templates\\sales_team\\dashboard.html": {"sales_division_detail": 2, "orders": 4, "sales_by_agent_report": 1, "reports": 1}, "templates\\sales_team\\division_detail.html": {"dashboard": 1, "sales_team_dashboard": 2, "orders": 1, "reports": 1}, "templates\\search\\advanced.html": {}, "templates\\search\\universal_search.html": {"universal_search": 2, "customer_statement": 1, "products": 1, "view_order": 1}, "templates\\tracking\\track_form.html": {"tracking.track_post": 1}, "templates\\tracking\\track_result.html": {"tracking.track_form": 2}, "templates\\users\\add.html": {"users.add": 1, "users.manage": 1}, "templates\\users\\edit.html": {"users.edit": 1, "users.manage": 1}, "templates\\users\\index.html": {"users.add": 2, "users.reset_password": 3, "users.logs": 1}, "templates\\users\\logs.html": {"users.manage": 1}, "templates\\users\\manage.html": {"users.add": 1, "users.roles": 1, "users.logs": 1, "users.edit": 1, "users.toggle_status": 2, "users.delete": 1, "users.reset_password": 1}, "templates\\users\\new.html": {"users_index": 2, "users_new": 1}, "templates\\users\\permissions.html": {"users.roles": 2, "users.permissions": 1}, "templates\\users\\profile.html": {"users": 2}, "templates\\users\\reset_password.html": {"users.reset_password": 1, "users.manage": 1}, "templates\\users\\roles.html": {"users.permissions": 1, "users.manage": 1, "users.sync_permissions": 1}, "templates\\warehouse\\dc_generate.html": {"dc_pending": 1}, "templates\\warehouse\\dc_pending.html": {"orders.view_order": 1, "dc_generate": 1, "orders.index": 2, "warehouses": 1, "workflow_management": 1, "reports": 1}, "templates\\warehouse\\edit.html": {"warehouse": 2, "edit_warehouse": 1}, "templates\\warehouse\\index.html": {"inventory": 2, "warehouse": 1}, "templates\\warehouse\\orders.html": {"orders": 1, "admin_rider_tracking": 1, "dashboard": 1}, "templates\\warehouse\\reports.html": {"warehouses": 1, "warehouse_utilization_report": 1, "low_stock_alert_report": 1, "warehouse_performance_report": 1, "monthly_summary_report": 1}, "templates\\warehouse\\warehouses.html": {"inventory": 2, "delivery_challans": 2}, "templates\\warehouses\\index.html": {"dashboard": 1, "warehouse_generate_dc": 1, "view_challan": 1, "view_order_history": 1, "inventory": 1, "workflow": 1, "reports": 1, "delivery_challans": 1}, "templates\\workflow\\management.html": {"orders.view_order": 1, "order_approval": 1, "dc_generate": 1, "orders.new_order": 1, "orders.index": 1, "dc_pending": 1, "warehouses": 1, "reports": 1, "dashboard": 1}}, "conflicts": [], "missing_routes": ["create_order_with_workflow", "sales_analytics.division_ledger", "sales_division_detail", "manual_allocate_inventory", "add_payment_attachment", "approve_order", "customer_statement", "riders", "finance_process_payment", "login", "admin_rider_tracking", "users_new", "duplicate_order_detection", "divisions.view", "users.sync_permissions", "delivery_performance_report", "sales_analytics.dashboard", "export_warehouse_inventory", "weekly_sales_report_new", "divisions.create", "display_challan", "new_product", "update_delivery_status", "advanced_payment.automated_matching", "customers", "riders_dashboard", "expiry_report", "warehouse_inventory_report", "batch_tracking_report", "register_rider", "finance_pending_invoices_management", "transfer_stock", "orders.view_order", "import_data", "add_inventory", "tracking.track_form", "delivery_analytics.delivery_history", "rider_performance", "inventory_add", "warehouse", "users.permissions", "product_inventory_report", "users.manage", "advanced_payment.analytics", "low_stock_alert_report", "new_stock", "divisions.index", "riders.tracking", "dispatch_order", "workflow", "allocate_inventory_page", "users.toggle_status", "divisions.export", "export_sales_team_pdf", "sales_team_dashboard", "track_order_page", "add_customer", "daily_sales_report_new", "customer_purchase_history", "view_all_products", "users.add", "generate_invoice", "sales_analytics", "warehouses", "accounts_receivable", "inventory_history", "view_order_history", "release_allocation", "reports_dashboard", "users.logs", "view_product", "orders_enhanced.order_history", "monthly_summary_report", "view_challan", "users_index", "products", "inventory_report", "users.delete", "inventory", "view_invoice", "edit_inventory", "rider_delivery_routes", "toggle_inventory_status", "view_rider", "update_product", "users_permissions", "product_management", "users.roles", "order_approval", "sales_analytics.team_performance", "users.reset_password", "orders.update_order", "backup_database", "sales_team_performance", "sales_by_agent_report", "advanced_payment.dashboard", "dashboard", "inventory_transfer", "warehouse_performance_report", "delivery_analytics.performance_kpis", "delivery_analytics.customer_delivery_tracking", "view_order", "export_orders_excel", "finance.view_invoice", "organization", "new_order", "logout", "divisions.analytics", "finance_invoices", "static", "export_product_performance_pdf", "divisions.edit", "finance_generate_invoice", "orders.index", "warehouse_utilization_report", "finance_aging_analysis", "trend_analysis", "riders_orders", "orders", "finance_api_stats", "daily_sales_report", "finance_payment_collection", "monthly_sales_report_new", "users.edit", "delivery_analytics.individual_rider_reports", "divisions.api_list", "update_product_selection", "universal_search", "finance_pending_invoices", "custom_date_range_report", "stock_movement_report", "orders.new_order", "warehouse_generate_dc", "delivery_analytics.customer_satisfaction", "divisions.delete", "low_stock_report", "workflow_management", "export_inventory_excel", "division_performance_chart", "financial_summary_report", "advanced_payment.bulk_processing", "auto_allocate_inventory", "update_settings", "remove_payment_attachment", "riders.analytics", "sales_analytics.salesperson_ledger", "claim_pickup", "accounts_payable", "edit_warehouse", "delivery_analytics.dashboard", "users", "delivery_challans", "financial_reports", "delivery_analytics.real_time_tracking", "dc_generate", "export_customers", "tracking.track_post", "monthly_sales_report", "reject_order_manual", "approve_order_manual", "feedback", "finance_customer_ledger", "export_warehouse_performance", "inventory_analytics", "finance_dashboard", "reports", "display_invoice", "weekly_sales_report", "delivery_analytics.comprehensive_reports", "top_customers_revenue_report", "deliver_order", "advanced_financial_analytics", "settings", "orders_enhanced.update_order", "orders.approve_order", "product_performance_report", "generate_challan", "edit_rider", "serve_uploaded_file", "orders_enhanced.advanced_search", "update_order", "warehouse_reports", "advanced_payment.process_bulk_payment", "comprehensive_finance_reports", "riders_management", "dc_pending"], "duplicate_routes": [{"function": "index", "files": ["app.py", "routes/divisions_modern.py"]}], "recommendations": [{"type": "fix_template_reference", "action": "Update template references from riders_dashboard to correct function name", "priority": "HIGH"}, {"type": "critical_fix", "action": "Fix riders_dashboard vs rider_dashboard conflict", "details": "Ensure templates reference the correct function name", "priority": "CRITICAL"}]}