{% extends 'base.html' %}

{% block title %}Comprehensive Finance Reports - Medivent ERP{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Heading -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-pie text-primary"></i> Comprehensive Finance Reports
        </h1>
        <div>
            <a href="{{ url_for('advanced_payment.analytics') }}" class="btn btn-primary shadow-sm mr-2">
                <i class="fas fa-analytics fa-sm text-white-50"></i> Payment Analytics
            </a>
            <a href="{{ url_for('finance_dashboard') }}" class="btn btn-secondary shadow-sm">
                <i class="fas fa-arrow-left fa-sm text-white-50"></i> Back to Finance
            </a>
        </div>
    </div>

    <!-- Financial Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Orders</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ financial_summary.total_orders or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Total Revenue</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ "%.2f"|format(financial_summary.total_revenue or 0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">Total Payments</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ financial_summary.total_payments or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-credit-card fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Amount Collected</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">₹{{ "%.2f"|format(financial_summary.total_collected or 0) }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-coins fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts and Tables Row -->
    <div class="row">
        <!-- Monthly Revenue Chart -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Revenue Trend</h6>
                </div>
                <div class="card-body">
                    {% if monthly_revenue %}
                        <div class="chart-area">
                            <canvas id="monthlyRevenueChart"></canvas>
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No monthly revenue data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Payment Methods Pie Chart -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Payment Methods</h6>
                </div>
                <div class="card-body">
                    {% if payment_methods %}
                        <div class="chart-pie pt-4 pb-2">
                            <canvas id="paymentMethodsChart"></canvas>
                        </div>
                        <div class="mt-4 text-center small">
                            {% for method in payment_methods %}
                            <span class="mr-2">
                                <i class="fas fa-circle text-primary"></i> {{ method.payment_method }}
                            </span>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No payment method data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Outstanding Payments Table -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Outstanding Payments</h6>
                </div>
                <div class="card-body">
                    {% if outstanding_payments %}
                        <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer</th>
                                        <th>Total Amount</th>
                                        <th>Paid Amount</th>
                                        <th>Outstanding</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for payment in outstanding_payments %}
                                    <tr>
                                        <td>{{ payment.order_id }}</td>
                                        <td>{{ payment.customer_name }}</td>
                                        <td>₹{{ "%.2f"|format(payment.total_amount) }}</td>
                                        <td>₹{{ "%.2f"|format(payment.paid_amount) }}</td>
                                        <td>₹{{ "%.2f"|format(payment.outstanding) }}</td>
                                        <td>
                                            <a href="{{ url_for('advanced_payment.bulk_processing') }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-credit-card"></i> Collect
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i> No outstanding payments!
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Top Customers</h6>
                </div>
                <div class="card-body">
                    {% if top_customers %}
                        {% for customer in top_customers %}
                        <div class="d-flex align-items-center border-bottom py-2">
                            <div class="mr-3">
                                <div class="icon-circle bg-primary">
                                    <i class="fas fa-user text-white"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <div class="font-weight-bold">{{ customer.customer_name }}</div>
                                <div class="small text-gray-500">{{ customer.order_count }} orders</div>
                                <div class="small">₹{{ "%.2f"|format(customer.total_revenue) }}</div>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> No customer data available.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Revenue Chart
    {% if monthly_revenue %}
    const monthlyRevenueCtx = document.getElementById('monthlyRevenueChart').getContext('2d');
    new Chart(monthlyRevenueCtx, {
        type: 'line',
        data: {
            labels: [{% for month in monthly_revenue %}'{{ month.month }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                label: 'Revenue',
                data: [{% for month in monthly_revenue %}{{ month.revenue or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.1)',
                borderWidth: 2,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    {% endif %}

    // Payment Methods Chart
    {% if payment_methods %}
    const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
    new Chart(paymentMethodsCtx, {
        type: 'doughnut',
        data: {
            labels: [{% for method in payment_methods %}'{{ method.payment_method }}'{% if not loop.last %},{% endif %}{% endfor %}],
            datasets: [{
                data: [{% for method in payment_methods %}{{ method.total_amount or 0 }}{% if not loop.last %},{% endif %}{% endfor %}],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b']
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
    {% endif %}
});
</script>
{% endblock %}